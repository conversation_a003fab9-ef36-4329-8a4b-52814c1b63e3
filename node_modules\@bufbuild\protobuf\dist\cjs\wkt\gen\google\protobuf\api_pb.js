"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.MixinSchema = exports.MethodSchema = exports.ApiSchema = exports.file_google_protobuf_api = void 0;
const file_js_1 = require("../../../../codegenv2/file.js");
const source_context_pb_js_1 = require("./source_context_pb.js");
const type_pb_js_1 = require("./type_pb.js");
const message_js_1 = require("../../../../codegenv2/message.js");
/**
 * Describes the file google/protobuf/api.proto.
 */
exports.file_google_protobuf_api = (0, file_js_1.fileDesc)("Chlnb29nbGUvcHJvdG9idWYvYXBpLnByb3RvEg9nb29nbGUucHJvdG9idWYigQIKA0FwaRIMCgRuYW1lGAEgASgJEigKB21ldGhvZHMYAiADKAsyFy5nb29nbGUucHJvdG9idWYuTWV0aG9kEigKB29wdGlvbnMYAyADKAsyFy5nb29nbGUucHJvdG9idWYuT3B0aW9uEg8KB3ZlcnNpb24YBCABKAkSNgoOc291cmNlX2NvbnRleHQYBSABKAsyHi5nb29nbGUucHJvdG9idWYuU291cmNlQ29udGV4dBImCgZtaXhpbnMYBiADKAsyFi5nb29nbGUucHJvdG9idWYuTWl4aW4SJwoGc3ludGF4GAcgASgOMhcuZ29vZ2xlLnByb3RvYnVmLlN5bnRheCLVAQoGTWV0aG9kEgwKBG5hbWUYASABKAkSGAoQcmVxdWVzdF90eXBlX3VybBgCIAEoCRIZChFyZXF1ZXN0X3N0cmVhbWluZxgDIAEoCBIZChFyZXNwb25zZV90eXBlX3VybBgEIAEoCRIaChJyZXNwb25zZV9zdHJlYW1pbmcYBSABKAgSKAoHb3B0aW9ucxgGIAMoCzIXLmdvb2dsZS5wcm90b2J1Zi5PcHRpb24SJwoGc3ludGF4GAcgASgOMhcuZ29vZ2xlLnByb3RvYnVmLlN5bnRheCIjCgVNaXhpbhIMCgRuYW1lGAEgASgJEgwKBHJvb3QYAiABKAlCdgoTY29tLmdvb2dsZS5wcm90b2J1ZkIIQXBpUHJvdG9QAVosZ29vZ2xlLmdvbGFuZy5vcmcvcHJvdG9idWYvdHlwZXMva25vd24vYXBpcGKiAgNHUEKqAh5Hb29nbGUuUHJvdG9idWYuV2VsbEtub3duVHlwZXNiBnByb3RvMw", [source_context_pb_js_1.file_google_protobuf_source_context, type_pb_js_1.file_google_protobuf_type]);
/**
 * Describes the message google.protobuf.Api.
 * Use `create(ApiSchema)` to create a new message.
 */
exports.ApiSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_api, 0);
/**
 * Describes the message google.protobuf.Method.
 * Use `create(MethodSchema)` to create a new message.
 */
exports.MethodSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_api, 1);
/**
 * Describes the message google.protobuf.Mixin.
 * Use `create(MixinSchema)` to create a new message.
 */
exports.MixinSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_api, 2);
