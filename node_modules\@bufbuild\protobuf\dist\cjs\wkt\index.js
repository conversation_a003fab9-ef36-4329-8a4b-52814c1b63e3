"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
__exportStar(require("./timestamp.js"), exports);
__exportStar(require("./any.js"), exports);
__exportStar(require("./wrappers.js"), exports);
__exportStar(require("./gen/google/protobuf/any_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/api_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/cpp_features_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/descriptor_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/duration_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/empty_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/field_mask_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/go_features_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/java_features_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/source_context_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/struct_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/timestamp_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/type_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/wrappers_pb.js"), exports);
__exportStar(require("./gen/google/protobuf/compiler/plugin_pb.js"), exports);
