{"version": 3, "file": "color.global.legacy.js", "sources": ["../node_modules/core-js/internals/global.js", "../node_modules/core-js/internals/fails.js", "../node_modules/core-js/internals/descriptors.js", "../node_modules/core-js/internals/function-bind-native.js", "../node_modules/core-js/internals/function-call.js", "../node_modules/core-js/internals/object-property-is-enumerable.js", "../node_modules/core-js/internals/create-property-descriptor.js", "../node_modules/core-js/internals/function-uncurry-this.js", "../node_modules/core-js/internals/classof-raw.js", "../node_modules/core-js/internals/indexed-object.js", "../node_modules/core-js/internals/is-null-or-undefined.js", "../node_modules/core-js/internals/require-object-coercible.js", "../node_modules/core-js/internals/to-indexed-object.js", "../node_modules/core-js/internals/is-callable.js", "../node_modules/core-js/internals/is-object.js", "../node_modules/core-js/internals/get-built-in.js", "../node_modules/core-js/internals/object-is-prototype-of.js", "../node_modules/core-js/internals/engine-user-agent.js", "../node_modules/core-js/internals/engine-v8-version.js", "../node_modules/core-js/internals/symbol-constructor-detection.js", "../node_modules/core-js/internals/use-symbol-as-uid.js", "../node_modules/core-js/internals/is-symbol.js", "../node_modules/core-js/internals/try-to-string.js", "../node_modules/core-js/internals/a-callable.js", "../node_modules/core-js/internals/get-method.js", "../node_modules/core-js/internals/ordinary-to-primitive.js", "../node_modules/core-js/internals/is-pure.js", "../node_modules/core-js/internals/define-global-property.js", "../node_modules/core-js/internals/shared-store.js", "../node_modules/core-js/internals/shared.js", "../node_modules/core-js/internals/to-object.js", "../node_modules/core-js/internals/has-own-property.js", "../node_modules/core-js/internals/uid.js", "../node_modules/core-js/internals/well-known-symbol.js", "../node_modules/core-js/internals/to-primitive.js", "../node_modules/core-js/internals/to-property-key.js", "../node_modules/core-js/internals/document-create-element.js", "../node_modules/core-js/internals/ie8-dom-define.js", "../node_modules/core-js/internals/object-get-own-property-descriptor.js", "../node_modules/core-js/internals/v8-prototype-define-bug.js", "../node_modules/core-js/internals/an-object.js", "../node_modules/core-js/internals/object-define-property.js", "../node_modules/core-js/internals/create-non-enumerable-property.js", "../node_modules/core-js/internals/function-name.js", "../node_modules/core-js/internals/inspect-source.js", "../node_modules/core-js/internals/weak-map-basic-detection.js", "../node_modules/core-js/internals/shared-key.js", "../node_modules/core-js/internals/hidden-keys.js", "../node_modules/core-js/internals/internal-state.js", "../node_modules/core-js/internals/make-built-in.js", "../node_modules/core-js/internals/define-built-in.js", "../node_modules/core-js/internals/math-trunc.js", "../node_modules/core-js/internals/to-integer-or-infinity.js", "../node_modules/core-js/internals/to-absolute-index.js", "../node_modules/core-js/internals/to-length.js", "../node_modules/core-js/internals/length-of-array-like.js", "../node_modules/core-js/internals/array-includes.js", "../node_modules/core-js/internals/object-keys-internal.js", "../node_modules/core-js/internals/enum-bug-keys.js", "../node_modules/core-js/internals/object-get-own-property-names.js", "../node_modules/core-js/internals/object-get-own-property-symbols.js", "../node_modules/core-js/internals/own-keys.js", "../node_modules/core-js/internals/copy-constructor-properties.js", "../node_modules/core-js/internals/is-forced.js", "../node_modules/core-js/internals/export.js", "../node_modules/core-js/internals/is-array.js", "../node_modules/core-js/internals/array-set-length.js", "../node_modules/core-js/internals/does-not-exceed-safe-integer.js", "../node_modules/core-js/modules/es.array.push.js", "../src/multiply-matrices.js", "../src/util.js", "../src/hooks.js", "../src/defaults.js", "../node_modules/core-js/internals/function-apply.js", "../node_modules/core-js/internals/function-uncurry-this-accessor.js", "../node_modules/core-js/internals/is-possible-prototype.js", "../node_modules/core-js/internals/a-possible-prototype.js", "../node_modules/core-js/internals/object-set-prototype-of.js", "../node_modules/core-js/internals/proxy-accessor.js", "../node_modules/core-js/internals/inherit-if-required.js", "../node_modules/core-js/internals/to-string-tag-support.js", "../node_modules/core-js/internals/classof.js", "../node_modules/core-js/internals/to-string.js", "../node_modules/core-js/internals/normalize-string-argument.js", "../node_modules/core-js/internals/install-error-cause.js", "../node_modules/core-js/internals/error-stack-clear.js", "../node_modules/core-js/internals/error-stack-installable.js", "../node_modules/core-js/internals/error-stack-install.js", "../node_modules/core-js/internals/wrap-error-constructor-with-cause.js", "../node_modules/core-js/modules/es.error.cause.js", "../src/adapt.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/deltaE/deltaEJz.js", "../src/spaces/ictcp.js", "../src/deltaE/deltaEITP.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/toGamut.js", "../src/to.js", "../node_modules/core-js/internals/delete-property-or-throw.js", "../node_modules/core-js/modules/es.array.unshift.js", "../src/serialize.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/equals.js", "../src/luminance.js", "../src/contrast/WCAG21.js", "../src/contrast/APCA.js", "../src/contrast/Michelson.js", "../src/contrast/Weber.js", "../src/contrast/Lstar.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/contrast.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/variations.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js", "../src/color.js", "../src/spaces/index.js", "../node_modules/core-js/internals/set-to-string-tag.js", "../node_modules/core-js/modules/es.reflect.to-string-tag.js", "../src/space-accessors.js", "../src/index.js"], "sourcesContent": ["'use strict';\nvar check = function (it) {\n  return it && it.Math === Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  check(typeof this == 'object' && this) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n", "'use strict';\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] !== 7;\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-function-prototype-bind -- safe\n  var test = (function () { /* empty */ }).bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\n\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n", "'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n", "'use strict';\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar call = FunctionPrototype.call;\nvar uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? uncurryThisWithBind : function (fn) {\n  return function () {\n    return call.apply(fn, arguments);\n  };\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) === 'String' ? split(it, '') : $Object(it);\n} : $Object;\n", "'use strict';\n// we can't use just `it == null` since of `document.all` special case\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot-aec\nmodule.exports = function (it) {\n  return it === null || it === undefined;\n};\n", "'use strict';\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\nvar $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (isNullOrUndefined(it)) throw new $TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "'use strict';\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "'use strict';\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\nvar documentAll = typeof document == 'object' && document.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nmodule.exports = typeof documentAll == 'undefined' && documentAll !== undefined ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(global[namespace]) : global[namespace] && global[namespace][method];\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n", "'use strict';\nmodule.exports = typeof navigator != 'undefined' && String(navigator.userAgent) || '';\n", "'use strict';\nvar global = require('../internals/global');\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\nvar global = require('../internals/global');\n\nvar $String = global.String;\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol('symbol detection');\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  // nb: Do not call `String` directly to avoid this being optimized out to `symbol+''` which will,\n  // of course, fail.\n  return !$String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};\n", "'use strict';\nvar $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return isNullOrUndefined(func) ? undefined : aCallable(func);\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw new $TypeError(\"Can't convert object to primitive value\");\n};\n", "'use strict';\nmodule.exports = false;\n", "'use strict';\nvar global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "'use strict';\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n\n(store.versions || (store.versions = [])).push({\n  version: '3.36.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2024 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n", "'use strict';\nvar store = require('../internals/shared-store');\n\nmodule.exports = function (key, value) {\n  return store[key] || (store[key] = value || {});\n};\n", "'use strict';\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Symbol = global.Symbol;\nvar WellKnownSymbolsStore = shared('wks');\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol['for'] || Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name)) {\n    WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol, name)\n      ? Symbol[name]\n      : createWellKnownSymbol('Symbol.' + name);\n  } return WellKnownSymbolsStore[name];\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw new $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n", "'use strict';\nvar toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a !== 7;\n});\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () { /* empty */ }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype !== 42;\n});\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw new $TypeError($String(argument) + ' is not an object');\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar ENUMERABLE = 'enumerable';\nvar CONFIGURABLE = 'configurable';\nvar WRITABLE = 'writable';\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (typeof O === 'function' && P === 'prototype' && 'value' in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {\n    var current = $getOwnPropertyDescriptor(O, P);\n    if (current && current[WRITABLE]) {\n      O[P] = Attributes.value;\n      Attributes = {\n        configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],\n        enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],\n        writable: false\n      };\n    }\n  } return $defineProperty(O, P, Attributes);\n} : $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw new $TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n", "'use strict';\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));\n", "'use strict';\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "'use strict';\nmodule.exports = {};\n", "'use strict';\nvar NATIVE_WEAK_MAP = require('../internals/weak-map-basic-detection');\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\nvar shared = require('../internals/shared-store');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\nvar TypeError = global.TypeError;\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw new TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP || shared.state) {\n  var store = shared.state || (shared.state = new WeakMap());\n  /* eslint-disable no-self-assign -- prototype methods protection */\n  store.get = store.get;\n  store.has = store.has;\n  store.set = store.set;\n  /* eslint-enable no-self-assign -- prototype methods protection */\n  set = function (it, metadata) {\n    if (store.has(it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    store.set(it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return store.get(it) || {};\n  };\n  has = function (it) {\n    return store.has(it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    if (hasOwn(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return hasOwn(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return hasOwn(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar $String = String;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nvar stringSlice = uncurryThis(''.slice);\nvar replace = uncurryThis(''.replace);\nvar join = uncurryThis([].join);\n\nvar CONFIGURABLE_LENGTH = DESCRIPTORS && !fails(function () {\n  return defineProperty(function () { /* empty */ }, 'length', { value: 8 }).length !== 8;\n});\n\nvar TEMPLATE = String(String).split('String');\n\nvar makeBuiltIn = module.exports = function (value, name, options) {\n  if (stringSlice($String(name), 0, 7) === 'Symbol(') {\n    name = '[' + replace($String(name), /^Symbol\\(([^)]*)\\).*$/, '$1') + ']';\n  }\n  if (options && options.getter) name = 'get ' + name;\n  if (options && options.setter) name = 'set ' + name;\n  if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n    if (DESCRIPTORS) defineProperty(value, 'name', { value: name, configurable: true });\n    else value.name = name;\n  }\n  if (CONFIGURABLE_LENGTH && options && hasOwn(options, 'arity') && value.length !== options.arity) {\n    defineProperty(value, 'length', { value: options.arity });\n  }\n  try {\n    if (options && hasOwn(options, 'constructor') && options.constructor) {\n      if (DESCRIPTORS) defineProperty(value, 'prototype', { writable: false });\n    // in V8 ~ Chrome 53, prototypes of some methods, like `Array.prototype.values`, are non-writable\n    } else if (value.prototype) value.prototype = undefined;\n  } catch (error) { /* empty */ }\n  var state = enforceInternalState(value);\n  if (!hasOwn(state, 'source')) {\n    state.source = join(TEMPLATE, typeof name == 'string' ? name : '');\n  } return value;\n};\n\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n// eslint-disable-next-line no-extend-native -- required\nFunction.prototype.toString = makeBuiltIn(function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n}, 'toString');\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar definePropertyModule = require('../internals/object-define-property');\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nmodule.exports = function (O, key, value, options) {\n  if (!options) options = {};\n  var simple = options.enumerable;\n  var name = options.name !== undefined ? options.name : key;\n  if (isCallable(value)) makeBuiltIn(value, name, options);\n  if (options.global) {\n    if (simple) O[key] = value;\n    else defineGlobalProperty(key, value);\n  } else {\n    try {\n      if (!options.unsafe) delete O[key];\n      else if (O[key]) simple = true;\n    } catch (error) { /* empty */ }\n    if (simple) O[key] = value;\n    else definePropertyModule.f(O, key, {\n      value: value,\n      enumerable: false,\n      configurable: !options.nonConfigurable,\n      writable: !options.nonWritable\n    });\n  } return O;\n};\n", "'use strict';\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `Math.trunc` method\n// https://tc39.es/ecma262/#sec-math.trunc\n// eslint-disable-next-line es/no-math-trunc -- safe\nmodule.exports = Math.trunc || function trunc(x) {\n  var n = +x;\n  return (n > 0 ? floor : ceil)(n);\n};\n", "'use strict';\nvar trunc = require('../internals/math-trunc');\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- NaN check\n  return number !== number || number === 0 ? 0 : trunc(number);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  var len = toIntegerOrInfinity(argument);\n  return len > 0 ? min(len, 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "'use strict';\nvar toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n", "'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = lengthOfArrayLike(O);\n    if (length === 0) return !IS_INCLUDES && -1;\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el !== el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value !== value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar hasOwn = require('../internals/has-own-property');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar push = uncurryThis([].push);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !hasOwn(hiddenKeys, key) && hasOwn(O, key) && push(result, key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (hasOwn(O, key = names[i++])) {\n    ~indexOf(result, key) || push(result, key);\n  }\n  return result;\n};\n", "'use strict';\n// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "'use strict';\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar concat = uncurryThis([].concat);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? concat(keys, getOwnPropertySymbols(it)) : keys;\n};\n", "'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source, exceptions) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key) && !(exceptions && hasOwn(exceptions, key))) {\n      defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n    }\n  }\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value === POLYFILL ? true\n    : value === NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "'use strict';\nvar global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || defineGlobalProperty(TARGET, {});\n  } else {\n    target = global[TARGET] && global[TARGET].prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    defineBuiltIn(target, key, sourceProperty, options);\n  }\n};\n", "'use strict';\nvar classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) === 'Array';\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar isArray = require('../internals/is-array');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Safari < 13 does not throw an error in this case\nvar SILENT_ON_NON_WRITABLE_LENGTH_SET = DESCRIPTORS && !function () {\n  // makes no sense without proper strict mode support\n  if (this !== undefined) return true;\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).length = 1;\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n}();\n\nmodule.exports = SILENT_ON_NON_WRITABLE_LENGTH_SET ? function (O, length) {\n  if (isArray(O) && !getOwnPropertyDescriptor(O, 'length').writable) {\n    throw new $TypeError('Cannot set read only .length');\n  } return O.length = length;\n} : function (O, length) {\n  return O.length = length;\n};\n", "'use strict';\nvar $TypeError = TypeError;\nvar MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFF; // 2 ** 53 - 1 == 9007199254740991\n\nmodule.exports = function (it) {\n  if (it > MAX_SAFE_INTEGER) throw $TypeError('Maximum allowed index exceeded');\n  return it;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\nvar fails = require('../internals/fails');\n\nvar INCORRECT_TO_LENGTH = fails(function () {\n  return [].push.call({ length: 0x100000000 }, 1) !== 4294967297;\n});\n\n// V8 <= 121 and Safari <= 15.4; FF < 23 throws InternalError\n// https://bugs.chromium.org/p/v8/issues/detail?id=12681\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).push();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_TO_LENGTH || !properErrorOnNonWritableLength();\n\n// `Array.prototype.push` method\n// https://tc39.es/ecma262/#sec-array.prototype.push\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  push: function push(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    doesNotExceedSafeInteger(len + argCount);\n    for (var i = 0; i < argCount; i++) {\n      O[len] = arguments[i];\n      len++;\n    }\n    setArrayLength(O, len);\n    return len;\n  }\n});\n", "// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};\n", "'use strict';\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n", "'use strict';\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar anObject = require('../internals/an-object');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\n\nmodule.exports = function (Target, Source, key) {\n  key in Target || defineProperty(Target, key, {\n    configurable: true,\n    get: function () { return Source[key]; },\n    set: function (it) { Source[key] = it; }\n  });\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    isCallable(NewTarget = dummy.constructor) &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "'use strict';\nvar classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n", "'use strict';\nvar toString = require('../internals/to-string');\n\nmodule.exports = function (argument, $default) {\n  return argument === undefined ? arguments.length < 2 ? '' : $default : toString(argument);\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\n// `InstallErrorCause` abstract operation\n// https://tc39.es/proposal-error-cause/#sec-errorobjects-install-error-cause\nmodule.exports = function (O, options) {\n  if (isObject(options) && 'cause' in options) {\n    createNonEnumerableProperty(O, 'cause', options.cause);\n  }\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar $Error = Error;\nvar replace = uncurryThis(''.replace);\n\nvar TEST = (function (arg) { return String(new $Error(arg).stack); })('zxcasd');\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar V8_OR_CHAKRA_STACK_ENTRY = /\\n\\s*at [^:]*:[^\\n]*/;\nvar IS_V8_OR_CHAKRA_STACK = V8_OR_CHAKRA_STACK_ENTRY.test(TEST);\n\nmodule.exports = function (stack, dropEntries) {\n  if (IS_V8_OR_CHAKRA_STACK && typeof stack == 'string' && !$Error.prepareStackTrace) {\n    while (dropEntries--) stack = replace(stack, V8_OR_CHAKRA_STACK_ENTRY, '');\n  } return stack;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = !fails(function () {\n  var error = new Error('a');\n  if (!('stack' in error)) return true;\n  // eslint-disable-next-line es/no-object-defineproperty -- safe\n  Object.defineProperty(error, 'stack', createPropertyDescriptor(1, 7));\n  return error.stack !== 7;\n});\n", "'use strict';\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar clearErrorStack = require('../internals/error-stack-clear');\nvar ERROR_STACK_INSTALLABLE = require('../internals/error-stack-installable');\n\n// non-standard V8\nvar captureStackTrace = Error.captureStackTrace;\n\nmodule.exports = function (error, C, stack, dropEntries) {\n  if (ERROR_STACK_INSTALLABLE) {\n    if (captureStackTrace) captureStackTrace(error, C);\n    else createNonEnumerableProperty(error, 'stack', clearErrorStack(stack, dropEntries));\n  }\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar hasOwn = require('../internals/has-own-property');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar proxyAccessor = require('../internals/proxy-accessor');\nvar inheritIfRequired = require('../internals/inherit-if-required');\nvar normalizeStringArgument = require('../internals/normalize-string-argument');\nvar installErrorCause = require('../internals/install-error-cause');\nvar installErrorStack = require('../internals/error-stack-install');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IS_PURE = require('../internals/is-pure');\n\nmodule.exports = function (FULL_NAME, wrapper, FORCED, IS_AGGREGATE_ERROR) {\n  var STACK_TRACE_LIMIT = 'stackTraceLimit';\n  var OPTIONS_POSITION = IS_AGGREGATE_ERROR ? 2 : 1;\n  var path = FULL_NAME.split('.');\n  var ERROR_NAME = path[path.length - 1];\n  var OriginalError = getBuiltIn.apply(null, path);\n\n  if (!OriginalError) return;\n\n  var OriginalErrorPrototype = OriginalError.prototype;\n\n  // V8 9.3- bug https://bugs.chromium.org/p/v8/issues/detail?id=12006\n  if (!IS_PURE && hasOwn(OriginalErrorPrototype, 'cause')) delete OriginalErrorPrototype.cause;\n\n  if (!FORCED) return OriginalError;\n\n  var BaseError = getBuiltIn('Error');\n\n  var WrappedError = wrapper(function (a, b) {\n    var message = normalizeStringArgument(IS_AGGREGATE_ERROR ? b : a, undefined);\n    var result = IS_AGGREGATE_ERROR ? new OriginalError(a) : new OriginalError();\n    if (message !== undefined) createNonEnumerableProperty(result, 'message', message);\n    installErrorStack(result, WrappedError, result.stack, 2);\n    if (this && isPrototypeOf(OriginalErrorPrototype, this)) inheritIfRequired(result, this, WrappedError);\n    if (arguments.length > OPTIONS_POSITION) installErrorCause(result, arguments[OPTIONS_POSITION]);\n    return result;\n  });\n\n  WrappedError.prototype = OriginalErrorPrototype;\n\n  if (ERROR_NAME !== 'Error') {\n    if (setPrototypeOf) setPrototypeOf(WrappedError, BaseError);\n    else copyConstructorProperties(WrappedError, BaseError, { name: true });\n  } else if (DESCRIPTORS && STACK_TRACE_LIMIT in OriginalError) {\n    proxyAccessor(WrappedError, OriginalError, STACK_TRACE_LIMIT);\n    proxyAccessor(WrappedError, OriginalError, 'prepareStackTrace');\n  }\n\n  copyConstructorProperties(WrappedError, OriginalError);\n\n  if (!IS_PURE) try {\n    // Safari 13- bug: WebAssembly errors does not have a proper `.name`\n    if (OriginalErrorPrototype.name !== ERROR_NAME) {\n      createNonEnumerableProperty(OriginalErrorPrototype, 'name', ERROR_NAME);\n    }\n    OriginalErrorPrototype.constructor = WrappedError;\n  } catch (error) { /* empty */ }\n\n  return WrappedError;\n};\n", "'use strict';\n/* eslint-disable no-unused-vars -- required for functions `.length` */\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar apply = require('../internals/function-apply');\nvar wrapErrorConstructorWithCause = require('../internals/wrap-error-constructor-with-cause');\n\nvar WEB_ASSEMBLY = 'WebAssembly';\nvar WebAssembly = global[WEB_ASSEMBLY];\n\n// eslint-disable-next-line es/no-error-cause -- feature detection\nvar FORCED = new Error('e', { cause: 7 }).cause !== 7;\n\nvar exportGlobalErrorCauseWrapper = function (ERROR_NAME, wrapper) {\n  var O = {};\n  O[ERROR_NAME] = wrapErrorConstructorWithCause(ERROR_NAME, wrapper, FORCED);\n  $({ global: true, constructor: true, arity: 1, forced: FORCED }, O);\n};\n\nvar exportWebAssemblyErrorCauseWrapper = function (ERROR_NAME, wrapper) {\n  if (WebAssembly && WebAssembly[ERROR_NAME]) {\n    var O = {};\n    O[ERROR_NAME] = wrapErrorConstructorWithCause(WEB_ASSEMBLY + '.' + ERROR_NAME, wrapper, FORCED);\n    $({ target: WEB_ASSEMBLY, stat: true, constructor: true, arity: 1, forced: FORCED }, O);\n  }\n};\n\n// https://tc39.es/ecma262/#sec-nativeerror\nexportGlobalErrorCauseWrapper('Error', function (init) {\n  return function Error(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('EvalError', function (init) {\n  return function EvalError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('RangeError', function (init) {\n  return function RangeError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('ReferenceError', function (init) {\n  return function ReferenceError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('SyntaxError', function (init) {\n  return function SyntaxError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('TypeError', function (init) {\n  return function TypeError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('URIError', function (init) {\n  return function URIError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('CompileError', function (init) {\n  return function CompileError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('LinkError', function (init) {\n  return function LinkError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('RuntimeError', function (init) {\n  return function RuntimeError(message) { return apply(init, this, arguments); };\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "'use strict';\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (O, P) {\n  if (!delete O[P]) throw new $TypeError('Cannot delete property ' + tryToString(P) + ' of ' + tryToString(O));\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar deletePropertyOrThrow = require('../internals/delete-property-or-throw');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\n\n// IE8-\nvar INCORRECT_RESULT = [].unshift(0) !== 1;\n\n// V8 ~ Chrome < 71 and Safari <= 15.4, FF < 23 throws InternalError\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).unshift();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_RESULT || !properErrorOnNonWritableLength();\n\n// `Array.prototype.unshift` method\n// https://tc39.es/ecma262/#sec-array.prototype.unshift\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  unshift: function unshift(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    if (argCount) {\n      doesNotExceedSafeInteger(len + argCount);\n      var k = len;\n      while (k--) {\n        var to = k + argCount;\n        if (k in O) O[to] = O[k];\n        else deletePropertyOrThrow(O, to);\n      }\n      for (var j = 0; j < argCount; j++) {\n        O[j] = arguments[j];\n      }\n    } return setArrayLength(O, len + argCount);\n  }\n});\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport defaults from \"./defaults.js\";\nimport ColorSpace from \"./space.js\";\nimport {WHITES} from \"./adapt.js\";\nimport {\n\tgetColor,\n\tparse,\n\tto,\n\tserialize,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\tequals,\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tdisplay,\n} from \"./index-fn.js\";\n\n\nimport \"./spaces/xyz-d50.js\";\nimport \"./spaces/srgb.js\";\n\n/**\n * Class that represents a color\n */\nexport default class Color {\n\t/**\n\t * Creates an instance of Color.\n\t * Signatures:\n\t * - `new Color(stringToParse)`\n\t * - `new Color(otherColor)`\n\t * - `new Color({space, coords, alpha})`\n\t * - `new Color(space, coords, alpha)`\n\t * - `new Color(spaceId, coords, alpha)`\n\t */\n\tconstructor (...args) {\n\t\tlet color;\n\n\t\tif (args.length === 1) {\n\t\t\tcolor = getColor(args[0]);\n\t\t}\n\n\t\tlet space, coords, alpha;\n\n\t\tif (color) {\n\t\t\tspace = color.space || color.spaceId;\n\t\t\tcoords = color.coords;\n\t\t\talpha = color.alpha;\n\t\t}\n\t\telse {\n\t\t\t// default signature new Color(ColorSpace, array [, alpha])\n\t\t\t[space, coords, alpha] = args;\n\t\t}\n\n\t\tObject.defineProperty(this, \"space\", {\n\t\t\tvalue: ColorSpace.get(space),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true, // see note in https://262.ecma-international.org/8.0/#sec-proxy-object-internal-methods-and-internal-slots-get-p-receiver\n\t\t});\n\n\t\tthis.coords = coords ? coords.slice() : [0, 0, 0];\n\n\t\t// Clamp alpha to [0, 1]\n\t\tthis.alpha = alpha > 1 || alpha === undefined ? 1 : (alpha < 0 ? 0 : alpha);\n\n\t\t// Convert \"NaN\" to NaN\n\t\tfor (let i = 0; i < this.coords.length; i++) {\n\t\t\tif (this.coords[i] === \"NaN\") {\n\t\t\t\tthis.coords[i] = NaN;\n\t\t\t}\n\t\t}\n\n\t\t// Define getters and setters for each coordinate\n\t\tfor (let id in this.space.coords) {\n\t\t\tObject.defineProperty(this, id, {\n\t\t\t\tget: () => this.get(id),\n\t\t\t\tset: value => this.set(id, value),\n\t\t\t});\n\t\t}\n\t}\n\n\tget spaceId () {\n\t\treturn this.space.id;\n\t}\n\n\tclone () {\n\t\treturn new Color(this.space, this.coords, this.alpha);\n\t}\n\n\ttoJSON () {\n\t\treturn {\n\t\t\tspaceId: this.spaceId,\n\t\t\tcoords: this.coords,\n\t\t\talpha: this.alpha,\n\t\t};\n\t}\n\n\tdisplay (...args) {\n\t\tlet ret = display(this, ...args);\n\n\t\t// Convert color object to Color instance\n\t\tret.color = new Color(ret.color);\n\n\t\treturn ret;\n\t}\n\n\t/**\n\t * Get a color from the argument passed\n\t * Basically gets us the same result as new Color(color) but doesn't clone an existing color object\n\t */\n\tstatic get (color, ...args) {\n\t\tif (color instanceof Color) {\n\t\t\treturn color;\n\t\t}\n\n\t\treturn new Color(color, ...args);\n\t}\n\n\tstatic defineFunction (name, code, o = code) {\n\t\tlet {instance = true, returns} = o;\n\n\t\tlet func = function (...args) {\n\t\t\tlet ret = code(...args);\n\n\t\t\tif (returns === \"color\") {\n\t\t\t\tret = Color.get(ret);\n\t\t\t}\n\t\t\telse if (returns === \"function<color>\") {\n\t\t\t\tlet f = ret;\n\t\t\t\tret = function (...args) {\n\t\t\t\t\tlet ret = f(...args);\n\t\t\t\t\treturn Color.get(ret);\n\t\t\t\t};\n\t\t\t\t// Copy any function metadata\n\t\t\t\tObject.assign(ret, f);\n\t\t\t}\n\t\t\telse if (returns === \"array<color>\") {\n\t\t\t\tret = ret.map(c => Color.get(c));\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t};\n\n\t\tif (!(name in Color)) {\n\t\t\tColor[name] = func;\n\t\t}\n\n\t\tif (instance) {\n\t\t\tColor.prototype[name] = function (...args) {\n\t\t\t\treturn func(this, ...args);\n\t\t\t};\n\t\t}\n\t}\n\n\tstatic defineFunctions (o) {\n\t\tfor (let name in o) {\n\t\t\tColor.defineFunction(name, o[name], o[name]);\n\t\t}\n\t}\n\n\tstatic extend (exports) {\n\t\tif (exports.register) {\n\t\t\texports.register(Color);\n\t\t}\n\t\telse {\n\t\t\t// No register method, just add the module's functions\n\t\t\tfor (let name in exports) {\n\t\t\t\tColor.defineFunction(name, exports[name]);\n\t\t\t}\n\t\t}\n\t}\n}\n\nColor.defineFunctions({\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tto,\n\tequals,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\ttoString: serialize,\n});\n\nObject.assign(Color, {\n\tutil,\n\thooks,\n\tWHITES,\n\tSpace: ColorSpace,\n\tspaces: ColorSpace.registry,\n\tparse,\n\n\t// Global defaults one may want to configure\n\tdefaults,\n});\n", "import ColorSpace from \"../space.js\";\nimport * as spaces from \"./index-fn.js\";\n\nexport * as spaces from \"./index-fn.js\";\n\nfor (let key of Object.keys(spaces)) {\n\tColorSpace.register(spaces[key]);\n}\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (target, TAG, STATIC) {\n  if (target && !STATIC) target = target.prototype;\n  if (target && !hasOwn(target, TO_STRING_TAG)) {\n    defineProperty(target, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar setToStringTag = require('../internals/set-to-string-tag');\n\n$({ global: true }, { Reflect: {} });\n\n// Reflect[@@toStringTag] property\n// https://tc39.es/ecma262/#sec-reflect-@@tostringtag\nsetToStringTag(global.Reflect, 'Reflect', true);\n", "/**\n * This plugin defines getters and setters for color[spaceId]\n * e.g. color.lch on *any* color gives us the lch coords\n */\nimport ColorSpace from \"./space.js\";\nimport Color from \"./color.js\";\nimport hooks from \"./hooks.js\";\n\n// Add space accessors to existing color spaces\nfor (let id in ColorSpace.registry) {\n\taddSpaceAccessors(id, ColorSpace.registry[id]);\n}\n\n// Add space accessors to color spaces not yet created\nhooks.add(\"colorspace-init-end\", space => {\n\taddSpaceAccessors(space.id, space);\n\tspace.aliases?.forEach(alias => {\n\t\taddSpaceAccessors(alias, space);\n\t});\n});\n\nfunction addSpaceAccessors (id, space) {\n\tlet propId = id.replace(/-/g, \"_\");\n\n\tObject.defineProperty(Color.prototype, propId, {\n\t\t// Convert coords to coords in another colorspace and return them\n\t\t// Source colorspace: this.spaceId\n\t\t// Target colorspace: id\n\t\tget () {\n\t\t\tlet ret = this.getAll(id);\n\n\t\t\tif (typeof Proxy === \"undefined\") {\n\t\t\t\t// If proxies are not supported, just return a static array\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\t// Enable color.spaceId.coordName syntax\n\t\t\treturn new Proxy(ret, {\n\t\t\t\thas: (obj, property) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tColorSpace.resolveCoord([space, property]);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tcatch (e) {}\n\n\t\t\t\t\treturn Reflect.has(obj, property);\n\t\t\t\t},\n\t\t\t\tget: (obj, property, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj)) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\treturn obj[index];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.get(obj, property, receiver);\n\t\t\t\t},\n\t\t\t\tset: (obj, property, value, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj) || property >= 0) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\tobj[index] = value;\n\n\t\t\t\t\t\t\t// Update color.coords\n\t\t\t\t\t\t\tthis.setAll(id, obj);\n\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.set(obj, property, value, receiver);\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\t// Convert coords in another colorspace to internal coords and set them\n\t\t// Target colorspace: this.spaceId\n\t\t// Source colorspace: id\n\t\tset (coords) {\n\t\t\tthis.setAll(id, coords);\n\t\t},\n\t\tconfigurable: true,\n\t\tenumerable: true,\n\t});\n}\n", "// Import all modules of Color.js\nimport Color from \"./color.js\";\n\n// Import all color spaces\nimport \"./spaces/index.js\";\n\n// Import all DeltaE methods\nimport deltaE from \"./deltaE.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nColor.extend(deltaEMethods);\nColor.extend({deltaE});\nObject.assign(Color, {deltaEMethods});\n\n// Import optional modules\nimport * as variations from \"./variations.js\";\nColor.extend(variations);\n\nimport contrast from \"./contrast.js\";\nColor.extend({contrast});\n\nimport * as chromaticity from \"./chromaticity.js\";\nColor.extend(chromaticity);\n\nimport * as luminance from \"./luminance.js\";\nColor.extend(luminance);\n\nimport * as interpolation from \"./interpolation.js\";\nColor.extend(interpolation);\n\nimport * as contrastMethods from \"./contrast/index.js\";\nColor.extend(contrastMethods);\n\nimport \"./CATs.js\";\nimport \"./space-accessors.js\";\n\n// Re-export everything\nexport default Color;\n"], "names": ["global", "this", "require$$0", "require$$1", "require$$2", "require$$3", "sharedStoreModule", "require$$4", "require$$5", "require$$6", "require$$7", "makeBuiltIn", "makeBuiltInModule", "multiplyMatrices", "A", "B", "m", "length", "Array", "isArray", "map", "x", "p", "B_cols", "_", "i", "product", "row", "col", "ret", "c", "isString", "str", "type", "o", "Object", "prototype", "toString", "call", "match", "toLowerCase", "serializeNumber", "n", "_ref", "precision", "unit", "isNone", "toPrecision", "Number", "isNaN", "none", "<PERSON><PERSON><PERSON>", "integer", "digits", "Math", "log10", "abs", "multiplier", "floor", "angleFactor", "deg", "grad", "rad", "PI", "turn", "parseFunction", "trim", "isFunctionRegex", "isNumberRegex", "unitValueRegex", "singleArgument", "parts", "args", "replace", "$0", "rawArg", "arg", "unitlessArg", "slice", "test", "NaN", "startsWith", "alpha", "raw", "push", "name", "rawName", "rawArgs", "last", "arr", "interpolate", "start", "end", "interpolateInv", "value", "mapRange", "from", "to", "parseCoordGrammar", "coordGrammars", "coordGrammar", "split", "range", "String", "clamp", "min", "val", "max", "copySign", "sign", "spow", "base", "exp", "zdiv", "d", "bisectLeft", "lo", "arguments", "undefined", "hi", "mid", "<PERSON>s", "add", "callback", "first", "for<PERSON>ach", "run", "env", "context", "hooks", "gamut_mapping", "deltaE", "verbose", "globalThis", "_globalThis$process", "process", "NODE_ENV", "warn", "msg", "_globalThis$console", "_globalThis$console$w", "console", "require$$8", "require$$9", "require$$10", "require$$11", "require$$12", "WHITES", "D50", "D65", "<PERSON><PERSON><PERSON><PERSON>", "adapt", "W1", "W2", "XYZ", "options", "TypeError", "M", "noneTypes", "Set", "coerceCoords", "space", "format", "coords", "types", "entries", "id", "coordMeta", "providedType", "find", "has", "coordName", "fromRange", "to<PERSON><PERSON><PERSON>", "refRange", "util", "parse", "_String", "meta", "color", "parsed", "shift", "alternateId", "substring", "ids", "indexOf", "pop", "ColorSpace", "all", "colorSpec", "getFormat", "_colorSpec$ids", "includes", "filter", "specId", "keys", "assign", "formatId", "defaults", "spaceId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registryId", "registry", "_ColorSpace$registry$", "cssId", "formats", "lastAlpha", "_color$alpha", "getColor", "get", "ε", "constructor", "_options$coords", "_options$white", "_options$formats", "_this$formats$color", "aliases", "fromBase", "toBase", "white", "_this$formats$color2", "gamutSpace", "isPolar", "isUnbounded", "inGamut", "referred", "defineProperty", "<PERSON><PERSON><PERSON>", "reverse", "writable", "enumerable", "configurable", "epsilon", "equals", "values", "every", "coord", "_this$formats", "processFormat", "myPath", "path", "otherPath", "connectionSpace", "connectionSpaceIndex", "Error", "getMinCoords", "_range$min", "register", "alias", "argType", "_len", "alternatives", "_key", "resolveCoord", "ref", "workingSpace", "coordType", "coordId", "index", "normalizedCoord", "_meta$name", "join", "DEFAULT_FORMAT", "s", "coordFormats", "_ref2", "outputType", "suffix", "serializeCoords", "y", "z", "RGBColorSpace", "_options$referred", "r", "g", "b", "XYZ_D65", "toXYZ_M", "fromXYZ_M", "_options$toBase", "_options$fromBase", "rgb", "xyz", "getAll", "prop", "setAll", "returns", "set", "object", "ε3", "κ", "l", "a", "xyz_d50", "f", "cbrt", "Lab", "pow", "constrain", "angle", "adjust", "arc", "angles", "a1", "a2", "angleDiff", "h", "L", "hue", "atan2", "sqrt", "constrainAngle", "LCH", "Lightness", "Chroma", "<PERSON><PERSON>", "cos", "sin", "Gfactor", "π", "r2d", "d2r", "pow7", "x2", "x7", "sample", "kL", "kC", "kH", "L1", "b1", "lab", "C1", "lch", "L2", "b2", "C2", "Cbar", "C7", "G", "adash1", "adash2", "Cdash1", "Cdash2", "h1", "h2", "ΔL", "ΔC", "hdiff", "hsum", "habs", "Δh", "ΔH", "Ldash", "Cdash", "Cdash7", "hdash", "lsq", "SL", "SC", "T", "SH", "Δθ", "RC", "RT", "dE", "XYZtoLMS_M", "LMStoXYZ_M", "LMStoLab_M", "LabtoLMS_M", "LMS", "LMSg", "OKLab", "oklab", "Δa", "Δb", "clone", "distance", "color1", "color2", "coords1", "coords2", "reduce", "acc", "c1", "c2", "deltaE76", "H1", "H2", "C4", "F", "Yw", "v", "AbsXYZ", "ninv", "c3", "pinv", "d0", "XYZtoCone_M", "ConetoXYZ_M", "ConetoIab_M", "IabtoCone_M", "jz", "az", "bz", "XYZ_Abs_D65", "Xa", "Ya", "<PERSON>a", "Xm", "Ym", "PQLMS", "num", "denom", "Iz", "Jz", "Jzazbz", "cz", "hz", "jzazbz", "jzczhz", "Jz1", "Cz1", "Hz1", "Jz2", "Cz2", "Hz2", "ΔJ", "m1", "m2", "im1", "im2", "LMStoIPT_M", "IPTtoLMS_M", "ct", "cp", "LMStoICtCp", "ICtCp", "ICtCptoLMS", "I1", "T1", "P1", "ictcp", "I2", "T2", "P2", "<PERSON><PERSON><PERSON><PERSON>", "adaptedCoefInv", "tau", "cat16", "cat16Inv", "surroundMap", "dark", "dim", "average", "hueQuadMap", "e", "H", "rad2deg", "deg2rad", "fl", "temp", "unadapt", "adapted", "constant", "cabs", "hueQuadrature", "hp", "hii", "ei", "eii", "Hi", "t", "invHueQuadrature", "Hp", "environment", "refWhite", "adaptingLuminance", "backgroundLuminance", "surround", "discounting", "xyzW", "la", "yb", "yw", "rgbW", "nc", "k", "k4", "flRoot", "nbb", "ncb", "dRgb", "dRgbInv", "rgbCW", "rgbAW", "aW", "viewingConditions", "fromCam16", "cam16", "J", "Q", "C", "hRad", "cosh", "sinh", "<PERSON><PERSON>", "et", "p1", "p2", "rgb_c", "toCam16", "xyzd65", "xyz100", "rgbA", "j", "xyz_d65", "toLstar", "fy", "fromLstar", "lstar", "fromHct", "threshold", "max_attempts", "attempt", "Infinity", "delta", "toHct", "hct", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertUcsAb", "log", "hrad", "t1", "t2", "deltaECMC", "deltaE2000", "deltaEJz", "deltaEITP", "deltaEOK", "deltaEHCT", "calcEpsilon", "jnd", "order", "parseFloat", "GMAPPRESET", "method", "deltaEMethod", "blackWhiteClamp", "channel", "toGamut", "spaceColor", "toGamutCSS", "hasOwnProperty", "de", "deltaEMethods", "clipped", "channelMeta", "mapSpace", "mappedColor", "bounds", "low", "high", "COLORS", "WHITE", "BLACK", "origin", "JND", "oklchSpace", "origin_OKLCH", "black", "clip", "_color", "destColor", "spaceCoords", "min_inGamut", "current", "E", "chroma", "serialize", "_color$space$getForma", "customOptions", "checkInGamut", "_format$ids", "unshift", "strAlpha", "noAlpha", "commas", "α", "β", "REC2020Linear", "RGB", "fill", "coordGrammarNumber", "sRGBLinear", "rgba", "component", "parseInt", "collapse", "round", "collapsible", "hex", "padStart", "KEYWORDS", "P3Linear", "sRGB", "display_space", "supportsNone", "CSS", "supports", "REC2020", "P3", "display", "fallbackColor", "hasNone", "some", "_supportsNone", "getLuminance", "setLuminance", "Color", "contrastWCAG21", "Y1", "Y2", "normBG", "normTXT", "revTXT", "revBG", "blkThrs", "blkClmp", "loClip", "deltaYmin", "scaleBoW", "loBoWoffset", "scaleWoB", "fclamp", "Y", "linearize", "contrastAPCA", "background", "foreground", "S", "Sapc", "R", "lumTxt", "lumBg", "Ytxt", "Ybg", "BoW", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "contrastLstar", "phi", "contrastDeltaPhi", "Lstr1", "lab_d65", "Lstr2", "deltaPhiStar", "contrast", "SQRT2", "algorithm", "rest", "algorithms", "contrastAlgorithms", "uv", "X", "Z", "xy", "sum", "lighten", "amount", "lightness", "darken", "mix", "steps", "colorRange", "isRange", "rangeArgs", "colors", "maxDeltaE", "maxSteps", "rangeOptions", "totalDelta", "actualSteps", "ceil", "step", "max<PERSON><PERSON><PERSON>", "cur", "ΔΕ", "prev", "splice", "outputSpace", "progression", "premultiplied", "interpolationSpace", "θ1", "θ2", "defineFunction", "hsl", "HSL", "hsv", "w", "HSV", "hwb", "gray", "A98Linear", "XYZ_D50", "Et", "Et2", "ProPhotoLinear", "oklch", "U_PRIME_WHITE", "V_PRIME_WHITE", "u", "up", "vp", "isFinite", "<PERSON><PERSON>", "m_r0", "m_r1", "m_r2", "m_g0", "m_g1", "m_g2", "m_b0", "m_b1", "m_b2", "distanceFromOriginAngle", "slope", "intercept", "calculateBoundingLines", "sub1", "sub2", "s1r", "s2r", "s3r", "s1g", "s2g", "s3g", "s1b", "s2b", "s3b", "r0s", "r0i", "r1s", "r1i", "g0s", "g0i", "g1s", "g1i", "b0s", "b0i", "b1s", "b1i", "calcMaxChromaHsluv", "lines", "hueRad", "r0", "r1", "g0", "g1", "b0", "LCHuv", "distanceFromOrigin", "calcMaxChromaHpluv", "minv", "scale", "CATs", "defineCAT", "toCone_M", "fromCone_M", "ρs", "γs", "βs", "ρd", "γd", "βd", "scaled_cone_M", "adapt_M", "D55", "D75", "F2", "F7", "F11", "ACES", "ACES_min_nonzero", "ACES_cc_max", "log2", "ACEScg", "toJSON", "_len2", "_key2", "_len3", "_key3", "code", "instance", "func", "_len4", "_key4", "defineFunctions", "extend", "exports", "Space", "spaces", "key", "addSpaceAccessors", "_space$aliases", "propId", "Proxy", "obj", "property", "Reflect", "receiver", "variations", "chromaticity", "luminance", "interpolation", "contrastMethods"], "mappings": ";;;;;;;;;;;;;CACA,CAAA,IAAI,KAAK,GAAG,UAAU,EAAE,EAAE;IACxB,OAAO,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;CACtC,EAAC,CAAC;AACF;CACA;CACA,CAAcA,QAAA;CACd;IACE,KAAK,CAAC,OAAO,UAAU,IAAI,QAAQ,IAAI,UAAU,CAAC;IAClD,KAAK,CAAC,OAAO,MAAM,IAAI,QAAQ,IAAI,MAAM,CAAC;CAC5C;IACE,KAAK,CAAC,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;IACtC,KAAK,CAAC,OAAOA,cAAM,IAAI,QAAQ,IAAIA,cAAM,CAAC;IAC1C,KAAK,CAAC,OAAOC,cAAI,IAAI,QAAQ,IAAIA,cAAI,CAAC;CACxC;CACA,GAAE,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAA;;;;;;;;;;;;CCd/D,CAAc,KAAA,GAAG,UAAU,IAAI,EAAE;CACjC,GAAE,IAAI;CACN,KAAI,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;KACjB,CAAC,OAAO,KAAK,EAAE;MACd,OAAO,IAAI,CAAC;KACb;GACF,CAAA;;;;;;;;;;ECND,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;AAC1C;CACA;CACA,CAAA,WAAc,GAAG,CAAC,KAAK,CAAC,YAAY;CACpC;IACE,OAAO,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;CACnF,EAAC,CAAC,CAAA;;;;;;;;;;ECNF,IAAI,KAAK,GAAGA,YAAA,EAA6B,CAAC;AAC1C;CACA,CAAA,kBAAc,GAAG,CAAC,KAAK,CAAC,YAAY;CACpC;IACE,IAAI,IAAI,GAAG,CAAC,YAAY,eAAe,EAAE,IAAI,EAAE,CAAC;CAClD;CACA,GAAE,OAAO,OAAO,IAAI,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;CACvE,EAAC,CAAC,CAAA;;;;;;;;;;ECPF,IAAI,WAAW,GAAGA,yBAAA,EAA4C,CAAC;AAC/D;CACA,CAAA,IAAI,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;AACnC;CACA,CAAc,YAAA,GAAG,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,YAAY;IAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;GACpC,CAAA;;;;;;;;;;;CCND,CAAA,IAAI,qBAAqB,GAAG,EAAE,CAAC,oBAAoB,CAAC;CACpD;CACA,CAAA,IAAI,wBAAwB,GAAG,MAAM,CAAC,wBAAwB,CAAC;AAC/D;CACA;CACA,CAAA,IAAI,WAAW,GAAG,wBAAwB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACvF;CACA;CACA;CACA,CAAA,0BAAA,CAAA,CAAS,GAAG,WAAW,GAAG,SAAS,oBAAoB,CAAC,CAAC,EAAE;IACzD,IAAI,UAAU,GAAG,wBAAwB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACnD,OAAO,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC;CAC/C,EAAC,GAAG,qBAAqB,CAAA;;;;;;;;;;CCZzB,CAAA,wBAAc,GAAG,UAAU,MAAM,EAAE,KAAK,EAAE;CAC1C,GAAE,OAAO;CACT,KAAI,UAAU,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;CAC7B,KAAI,YAAY,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;CAC/B,KAAI,QAAQ,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;MACvB,KAAK,EAAE,KAAK;CAChB,IAAG,CAAC;GACH,CAAA;;;;;;;;;;ECPD,IAAI,WAAW,GAAGA,yBAAA,EAA4C,CAAC;AAC/D;CACA,CAAA,IAAI,iBAAiB,GAAG,QAAQ,CAAC,SAAS,CAAC;CAC3C,CAAA,IAAI,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC;CAClC,CAAA,IAAI,mBAAmB,GAAG,WAAW,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjF;CACA,CAAA,mBAAc,GAAG,WAAW,GAAG,mBAAmB,GAAG,UAAU,EAAE,EAAE;CACnE,GAAE,OAAO,YAAY;MACjB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;CACrC,IAAG,CAAC;GACH,CAAA;;;;;;;;;;ECVD,IAAI,WAAW,GAAGA,0BAAA,EAA6C,CAAC;AAChE;EACA,IAAI,QAAQ,GAAG,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;EACxC,IAAI,WAAW,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AACxC;CACA,CAAc,UAAA,GAAG,UAAU,EAAE,EAAE;CAC/B,GAAE,OAAO,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACzC,CAAA;;;;;;;;;;ECPD,IAAI,WAAW,GAAGA,0BAAA,EAA6C,CAAC;EAChE,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;EAC1C,IAAI,OAAO,GAAGC,iBAAA,EAAmC,CAAC;AAClD;EACA,IAAI,OAAO,GAAG,MAAM,CAAC;EACrB,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAClC;CACA;CACA,CAAc,aAAA,GAAG,KAAK,CAAC,YAAY;CACnC;CACA;IACE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;CAC/C,EAAC,CAAC,GAAG,UAAU,EAAE,EAAE;CACnB,GAAE,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,QAAQ,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;CAChE,EAAC,GAAG,OAAO,CAAA;;;;;;;;;;CCdX;CACA;CACA,CAAc,iBAAA,GAAG,UAAU,EAAE,EAAE;IAC7B,OAAO,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,SAAS,CAAC;GACxC,CAAA;;;;;;;;;;ECJD,IAAI,iBAAiB,GAAGF,wBAAA,EAA4C,CAAC;AACrE;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;AAC3B;CACA;CACA;CACA,CAAc,sBAAA,GAAG,UAAU,EAAE,EAAE;CAC/B,GAAE,IAAI,iBAAiB,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,UAAU,CAAC,uBAAuB,GAAG,EAAE,CAAC,CAAC;IAC9E,OAAO,EAAE,CAAC;GACX,CAAA;;;;;;;;;;CCTD;EACA,IAAI,aAAa,GAAGA,oBAAA,EAAsC,CAAC;EAC3D,IAAI,sBAAsB,GAAGC,6BAAA,EAAgD,CAAC;AAC9E;CACA,CAAc,eAAA,GAAG,UAAU,EAAE,EAAE;IAC7B,OAAO,aAAa,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;GAClD,CAAA;;;;;;;;;;CCND;EACA,IAAI,WAAW,GAAG,OAAO,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC;AAC9D;CACA;CACA;CACA;CACA,CAAA,UAAc,GAAG,OAAO,WAAW,IAAI,WAAW,IAAI,WAAW,KAAK,SAAS,GAAG,UAAU,QAAQ,EAAE;IACpG,OAAO,OAAO,QAAQ,IAAI,UAAU,IAAI,QAAQ,KAAK,WAAW,CAAC;GAClE,GAAG,UAAU,QAAQ,EAAE;CACxB,GAAE,OAAO,OAAO,QAAQ,IAAI,UAAU,CAAC;GACtC,CAAA;;;;;;;;;;ECVD,IAAI,UAAU,GAAGD,iBAAA,EAAmC,CAAC;AACrD;CACA,CAAc,QAAA,GAAG,UAAU,EAAE,EAAE;CAC/B,GAAE,OAAO,OAAO,EAAE,IAAI,QAAQ,GAAG,EAAE,KAAK,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;GAC7D,CAAA;;;;;;;;;;ECJD,IAAI,MAAM,GAAGA,aAAA,EAA8B,CAAC;EAC5C,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;AACrD;CACA,CAAA,IAAI,SAAS,GAAG,UAAU,QAAQ,EAAE;IAClC,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC;CACrD,EAAC,CAAC;AACF;CACA,CAAA,UAAc,GAAG,UAAU,SAAS,EAAE,MAAM,EAAE;IAC5C,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;GAC7G,CAAA;;;;;;;;;;ECTD,IAAI,WAAW,GAAGD,0BAAA,EAA6C,CAAC;AAChE;CACA,CAAA,mBAAc,GAAG,WAAW,CAAC,EAAE,CAAC,aAAa,CAAC,CAAA;;;;;;;;;;CCF9C,CAAA,eAAc,GAAG,OAAO,SAAS,IAAI,WAAW,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;;;;;;;;;;ECArF,IAAI,MAAM,GAAGA,aAAA,EAA8B,CAAC;EAC5C,IAAI,SAAS,GAAGC,sBAAA,EAAyC,CAAC;AAC1D;CACA,CAAA,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;CAC7B,CAAA,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;CACvB,CAAA,IAAI,QAAQ,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;CACnE,CAAA,IAAI,EAAE,GAAG,QAAQ,IAAI,QAAQ,CAAC,EAAE,CAAC;EACjC,IAAI,KAAK,EAAE,OAAO,CAAC;AACnB;CACA,CAAA,IAAI,EAAE,EAAE;IACN,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;CACxB;CACA;CACA,GAAE,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;GACrE;AACD;CACA;CACA;CACA,CAAA,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;IACzB,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACvC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;MAC5B,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;MACzC,IAAI,KAAK,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAChC;GACF;AACD;CACA,CAAA,eAAc,GAAG,OAAO,CAAA;;;;;;;;;;CC1BxB;EACA,IAAI,UAAU,GAAGD,sBAAA,EAAyC,CAAC;EAC3D,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;EAC1C,IAAI,MAAM,GAAGC,aAAA,EAA8B,CAAC;AAC5C;CACA,CAAA,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B;CACA;CACA,CAAc,0BAAA,GAAG,CAAC,CAAC,MAAM,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,YAAY;CACtE,GAAE,IAAI,MAAM,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;CAC1C;CACA;CACA;CACA;CACA,GAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,YAAY,MAAM,CAAC;CAChE;MACI,CAAC,MAAM,CAAC,IAAI,IAAI,UAAU,IAAI,UAAU,GAAG,EAAE,CAAC;CAClD,EAAC,CAAC,CAAA;;;;;;;;;;CCjBF;EACA,IAAI,aAAa,GAAGF,iCAAA,EAAoD,CAAC;AACzE;CACA,CAAA,cAAc,GAAG,aAAa;OACzB,CAAC,MAAM,CAAC,IAAI;CACjB,MAAK,OAAO,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAA;;;;;;;;;;ECLvC,IAAI,UAAU,GAAGA,iBAAA,EAAoC,CAAC;EACtD,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;EACrD,IAAI,aAAa,GAAGC,0BAAA,EAA8C,CAAC;EACnE,IAAI,iBAAiB,GAAGC,qBAAA,EAAyC,CAAC;AAClE;EACA,IAAI,OAAO,GAAG,MAAM,CAAC;AACrB;CACA,CAAA,QAAc,GAAG,iBAAiB,GAAG,UAAU,EAAE,EAAE;CACnD,GAAE,OAAO,OAAO,EAAE,IAAI,QAAQ,CAAC;GAC9B,GAAG,UAAU,EAAE,EAAE;CAClB,GAAE,IAAI,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;CACrC,GAAE,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;GAC7E,CAAA;;;;;;;;;;ECZD,IAAI,OAAO,GAAG,MAAM,CAAC;AACrB;CACA,CAAc,WAAA,GAAG,UAAU,QAAQ,EAAE;CACrC,GAAE,IAAI;CACN,KAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1B,CAAC,OAAO,KAAK,EAAE;MACd,OAAO,QAAQ,CAAC;KACjB;GACF,CAAA;;;;;;;;;;ECRD,IAAI,UAAU,GAAGH,iBAAA,EAAmC,CAAC;EACrD,IAAI,WAAW,GAAGC,kBAAA,EAAqC,CAAC;AACxD;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;AAC3B;CACA;CACA,CAAc,SAAA,GAAG,UAAU,QAAQ,EAAE;IACnC,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ,CAAC;IAC1C,MAAM,IAAI,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,oBAAoB,CAAC,CAAC;GACpE,CAAA;;;;;;;;;;ECTD,IAAI,SAAS,GAAGD,gBAAA,EAAkC,CAAC;EACnD,IAAI,iBAAiB,GAAGC,wBAAA,EAA4C,CAAC;AACrE;CACA;CACA;CACA,CAAA,SAAc,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;CACjC,GAAE,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CAClB,GAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;GAC9D,CAAA;;;;;;;;;;ECRD,IAAI,IAAI,GAAGD,mBAAA,EAAqC,CAAC;EACjD,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;EACrD,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;AACjD;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;AAC3B;CACA;CACA;CACA,CAAA,mBAAc,GAAG,UAAU,KAAK,EAAE,IAAI,EAAE;CACxC,GAAE,IAAI,EAAE,EAAE,GAAG,CAAC;CACd,GAAE,IAAI,IAAI,KAAK,QAAQ,IAAI,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;IACzG,IAAI,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;CACrF,GAAE,IAAI,IAAI,KAAK,QAAQ,IAAI,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC;CAC3G,GAAE,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC,CAAC;GACjE,CAAA;;;;;;;;;;;;CCdD,CAAA,MAAc,GAAG,KAAK,CAAA;;;;;;;;;;ECAtB,IAAI,MAAM,GAAGF,aAAA,EAA8B,CAAC;AAC5C;CACA;CACA,CAAA,IAAI,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AAC3C;CACA,CAAA,oBAAc,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;CACvC,GAAE,IAAI;MACF,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;KACnF,CAAC,OAAO,KAAK,EAAE;CAClB,KAAI,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;KACrB,CAAC,OAAO,KAAK,CAAC;GAChB,CAAA;;;;;;;;;ECXD,IAAI,OAAO,GAAGA,aAAA,EAA+B,CAAC;EAC9C,IAAI,UAAU,GAAGC,aAAA,EAA8B,CAAC;EAChD,IAAI,oBAAoB,GAAGC,2BAAA,EAA8C,CAAC;AAC1E;EACA,IAAI,MAAM,GAAG,oBAAoB,CAAC;CAClC,CAAA,IAAI,KAAK,GAAGE,WAAc,CAAA,OAAA,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACpF;CACA,CAAA,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;IAC7C,OAAO,EAAE,QAAQ;CACnB,GAAE,IAAI,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ;IACjC,SAAS,EAAE,2CAA2C;IACtD,OAAO,EAAE,0DAA0D;IACnE,MAAM,EAAE,qCAAqC;CAC/C,EAAC,CAAC,CAAA;;;;;;;;;;ECbF,IAAI,KAAK,GAAGJ,kBAAA,EAAoC,CAAC;AACjD;CACA,CAAA,MAAc,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;CACvC,GAAE,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;GACjD,CAAA;;;;;;;;;;ECJD,IAAI,sBAAsB,GAAGA,6BAAA,EAAgD,CAAC;AAC9E;EACA,IAAI,OAAO,GAAG,MAAM,CAAC;AACrB;CACA;CACA;CACA,CAAc,QAAA,GAAG,UAAU,QAAQ,EAAE;IACnC,OAAO,OAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;GAClD,CAAA;;;;;;;;;;ECRD,IAAI,WAAW,GAAGA,0BAAA,EAA6C,CAAC;EAChE,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;AACjD;EACA,IAAI,cAAc,GAAG,WAAW,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;AACpD;CACA;CACA;CACA;CACA,CAAc,gBAAA,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE;IACzD,OAAO,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;GAC1C,CAAA;;;;;;;;;;ECVD,IAAI,WAAW,GAAGD,0BAAA,EAA6C,CAAC;AAChE;EACA,IAAI,EAAE,GAAG,CAAC,CAAC;CACX,CAAA,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;EAC5B,IAAI,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACzC;CACA,CAAc,GAAA,GAAG,UAAU,GAAG,EAAE;IAC9B,OAAO,SAAS,IAAI,GAAG,KAAK,SAAS,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC;GACzF,CAAA;;;;;;;;;;ECRD,IAAI,MAAM,GAAGA,aAAA,EAA8B,CAAC;EAC5C,IAAI,MAAM,GAAGC,aAAA,EAA8B,CAAC;EAC5C,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;EACtD,IAAI,GAAG,GAAGC,UAAA,EAA2B,CAAC;EACtC,IAAI,aAAa,GAAGE,iCAAA,EAAoD,CAAC;EACzE,IAAI,iBAAiB,GAAGC,qBAAA,EAAyC,CAAC;AAClE;CACA,CAAA,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;CAC3B,CAAA,IAAI,qBAAqB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;CAC1C,CAAA,IAAI,qBAAqB,GAAG,iBAAiB,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,aAAa,IAAI,GAAG,CAAC;AAChH;CACA,CAAc,eAAA,GAAG,UAAU,IAAI,EAAE;IAC/B,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;CAC5C,KAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC;UAC/D,MAAM,CAAC,IAAI,CAAC;CACpB,SAAQ,qBAAqB,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;CAChD,IAAG,CAAC,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;GACtC,CAAA;;;;;;;;;;ECjBD,IAAI,IAAI,GAAGN,mBAAA,EAAqC,CAAC;EACjD,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,SAAS,GAAGC,gBAAA,EAAkC,CAAC;EACnD,IAAI,mBAAmB,GAAGE,0BAAA,EAA6C,CAAC;EACxE,IAAI,eAAe,GAAGC,sBAAA,EAAyC,CAAC;AAChE;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;CAC3B,CAAA,IAAI,YAAY,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;AAClD;CACA;CACA;CACA,CAAA,WAAc,GAAG,UAAU,KAAK,EAAE,IAAI,EAAE;CACxC,GAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;IACtD,IAAI,YAAY,GAAG,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IAClD,IAAI,MAAM,CAAC;IACX,IAAI,YAAY,EAAE;MAChB,IAAI,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,SAAS,CAAC;MACzC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;CAC7C,KAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC;CAC7D,KAAI,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC,CAAC;KACjE;IACD,IAAI,IAAI,KAAK,SAAS,EAAE,IAAI,GAAG,QAAQ,CAAC;CAC1C,GAAE,OAAO,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;GACzC,CAAA;;;;;;;;;;ECxBD,IAAI,WAAW,GAAGN,kBAAA,EAAoC,CAAC;EACvD,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;AACjD;CACA;CACA;CACA,CAAc,aAAA,GAAG,UAAU,QAAQ,EAAE;IACnC,IAAI,GAAG,GAAG,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC1C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;GACvC,CAAA;;;;;;;;;;ECRD,IAAI,MAAM,GAAGD,aAAA,EAA8B,CAAC;EAC5C,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;AACjD;CACA,CAAA,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;CAC/B;CACA,CAAA,IAAI,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AACpE;CACA,CAAc,qBAAA,GAAG,UAAU,EAAE,EAAE;IAC7B,OAAO,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;GACjD,CAAA;;;;;;;;;;ECTD,IAAI,WAAW,GAAGD,kBAAA,EAAmC,CAAC;EACtD,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;EAC1C,IAAI,aAAa,GAAGC,4BAAA,EAA+C,CAAC;AACpE;CACA;CACA,CAAA,YAAc,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,YAAY;CACpD;IACE,OAAO,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE;CAC1D,KAAI,GAAG,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;CAClC,IAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;CACb,EAAC,CAAC,CAAA;;;;;;;;;ECVF,IAAI,WAAW,GAAGF,kBAAA,EAAmC,CAAC;EACtD,IAAI,IAAI,GAAGC,mBAAA,EAAqC,CAAC;EACjD,IAAI,0BAA0B,GAAGC,iCAAA,EAAqD,CAAC;EACvF,IAAI,wBAAwB,GAAGC,+BAAA,EAAkD,CAAC;EAClF,IAAI,eAAe,GAAGE,sBAAA,EAAyC,CAAC;EAChE,IAAI,aAAa,GAAGC,oBAAA,EAAuC,CAAC;EAC5D,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;EACtD,IAAI,cAAc,GAAGC,mBAAA,EAAsC,CAAC;AAC5D;CACA;CACA,CAAA,IAAI,yBAAyB,GAAG,MAAM,CAAC,wBAAwB,CAAC;AAChE;CACA;CACA;CACA,CAAS,8BAAA,CAAA,CAAA,GAAG,WAAW,GAAG,yBAAyB,GAAG,SAAS,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE;CAC9F,GAAE,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;CACzB,GAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI,cAAc,EAAE,IAAI;CAC1B,KAAI,OAAO,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CAC3C,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;CACjC,GAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,wBAAwB,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpG,CAAA;;;;;;;;;;;;ECrBD,IAAI,WAAW,GAAGR,kBAAA,EAAmC,CAAC;EACtD,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;AAC1C;CACA;CACA;CACA,CAAA,oBAAc,GAAG,WAAW,IAAI,KAAK,CAAC,YAAY;CAClD;IACE,OAAO,MAAM,CAAC,cAAc,CAAC,YAAY,eAAe,EAAE,WAAW,EAAE;MACrE,KAAK,EAAE,EAAE;MACT,QAAQ,EAAE,KAAK;CACnB,IAAG,CAAC,CAAC,SAAS,KAAK,EAAE,CAAC;CACtB,EAAC,CAAC,CAAA;;;;;;;;;;ECXF,IAAI,QAAQ,GAAGD,eAAA,EAAiC,CAAC;AACjD;EACA,IAAI,OAAO,GAAG,MAAM,CAAC;EACrB,IAAI,UAAU,GAAG,SAAS,CAAC;AAC3B;CACA;CACA,CAAc,QAAA,GAAG,UAAU,QAAQ,EAAE;IACnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ,CAAC;IACxC,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mBAAmB,CAAC,CAAC;GAC/D,CAAA;;;;;;;;;ECTD,IAAI,WAAW,GAAGA,kBAAA,EAAmC,CAAC;EACtD,IAAI,cAAc,GAAGC,mBAAA,EAAsC,CAAC;EAC5D,IAAI,uBAAuB,GAAGC,2BAAA,EAA+C,CAAC;EAC9E,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,aAAa,GAAGE,oBAAA,EAAuC,CAAC;AAC5D;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;CAC3B;CACA,CAAA,IAAI,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC;CAC5C;CACA,CAAA,IAAI,yBAAyB,GAAG,MAAM,CAAC,wBAAwB,CAAC;EAChE,IAAI,UAAU,GAAG,YAAY,CAAC;EAC9B,IAAI,YAAY,GAAG,cAAc,CAAC;EAClC,IAAI,QAAQ,GAAG,UAAU,CAAC;AAC1B;CACA;CACA;CACA,CAAA,oBAAA,CAAA,CAAS,GAAG,WAAW,GAAG,uBAAuB,GAAG,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;CAC9F,GAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;CACd,GAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CACvB,GAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;IACrB,IAAI,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,WAAW,IAAI,OAAO,IAAI,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC5H,IAAI,OAAO,GAAG,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CAClD,KAAI,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;CAC9B,OAAM,UAAU,GAAG;CACnB,SAAQ,YAAY,EAAE,YAAY,IAAI,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC;CACnG,SAAQ,UAAU,EAAE,UAAU,IAAI,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC;UACnF,QAAQ,EAAE,KAAK;CACvB,QAAO,CAAC;OACH;KACF,CAAC,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;GAC5C,GAAG,eAAe,GAAG,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;CAChE,GAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;CACd,GAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CACvB,GAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;IACrB,IAAI,cAAc,EAAE,IAAI;MACtB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;CAC7C,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;CACjC,GAAE,IAAI,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,UAAU,EAAE,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC,CAAC;CAClG,GAAE,IAAI,OAAO,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;IACnD,OAAO,CAAC,CAAC;GACV,CAAA;;;;;;;;;;EC1CD,IAAI,WAAW,GAAGL,kBAAA,EAAmC,CAAC;EACtD,IAAI,oBAAoB,GAAGC,2BAAA,EAA8C,CAAC;EAC1E,IAAI,wBAAwB,GAAGC,+BAAA,EAAkD,CAAC;AAClF;CACA,CAAc,2BAAA,GAAG,WAAW,GAAG,UAAU,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;CAC7D,GAAE,OAAO,oBAAoB,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,wBAAwB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;CACjF,EAAC,GAAG,UAAU,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;CAClC,GAAE,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACpB,OAAO,MAAM,CAAC;GACf,CAAA;;;;;;;;;;;;ECTD,IAAI,WAAW,GAAGF,kBAAA,EAAmC,CAAC;EACtD,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;AACtD;CACA,CAAA,IAAI,iBAAiB,GAAG,QAAQ,CAAC,SAAS,CAAC;CAC3C;CACA,CAAA,IAAI,aAAa,GAAG,WAAW,IAAI,MAAM,CAAC,wBAAwB,CAAC;AACnE;EACA,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;CAC/C;CACA,CAAA,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,SAAS,GAAG,eAAe,EAAE,IAAI,KAAK,WAAW,CAAC;CACnF,CAAA,IAAI,YAAY,GAAG,MAAM,KAAK,CAAC,WAAW,KAAK,WAAW,IAAI,aAAa,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AACtH;CACA,CAAA,YAAc,GAAG;IACf,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,MAAM;IACd,YAAY,EAAE,YAAY;GAC3B,CAAA;;;;;;;;;;EChBD,IAAI,WAAW,GAAGD,0BAAA,EAA6C,CAAC;EAChE,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;EACrD,IAAI,KAAK,GAAGC,kBAAA,EAAoC,CAAC;AACjD;EACA,IAAI,gBAAgB,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACtD;CACA;CACA,CAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;CACtC,GAAE,KAAK,CAAC,aAAa,GAAG,UAAU,EAAE,EAAE;CACtC,KAAI,OAAO,gBAAgB,CAAC,EAAE,CAAC,CAAC;CAChC,IAAG,CAAC;GACH;AACD;CACA,CAAc,aAAA,GAAG,KAAK,CAAC,aAAa,CAAA;;;;;;;;;;ECbpC,IAAI,MAAM,GAAGF,aAAA,EAA8B,CAAC;EAC5C,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;AACrD;CACA,CAAA,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC7B;CACA,CAAA,qBAAc,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAA;;;;;;;;;;ECL3E,IAAI,MAAM,GAAGD,aAAA,EAA8B,CAAC;EAC5C,IAAI,GAAG,GAAGC,UAAA,EAA2B,CAAC;AACtC;CACA,CAAA,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC1B;CACA,CAAc,SAAA,GAAG,UAAU,GAAG,EAAE;CAChC,GAAE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;GAC5C,CAAA;;;;;;;;;;CCPD,CAAA,UAAc,GAAG,EAAE,CAAA;;;;;;;;;;ECAnB,IAAI,eAAe,GAAGD,4BAAA,EAAgD,CAAC;EACvE,IAAI,MAAM,GAAGC,aAAA,EAA8B,CAAC;EAC5C,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,2BAA2B,GAAGC,kCAAA,EAAsD,CAAC;EACzF,IAAI,MAAM,GAAGE,qBAAA,EAAwC,CAAC;EACtD,IAAI,MAAM,GAAGC,kBAAA,EAAoC,CAAC;EAClD,IAAI,SAAS,GAAGC,gBAAA,EAAkC,CAAC;EACnD,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;AACrD;EACA,IAAI,0BAA0B,GAAG,4BAA4B,CAAC;CAC9D,CAAA,IAAI,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;CACjC,CAAA,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;CAC7B,CAAA,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClB;CACA,CAAA,IAAI,OAAO,GAAG,UAAU,EAAE,EAAE;CAC5B,GAAE,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,EAAC,CAAC;AACF;CACA,CAAA,IAAI,SAAS,GAAG,UAAU,IAAI,EAAE;IAC9B,OAAO,UAAU,EAAE,EAAE;MACnB,IAAI,KAAK,CAAC;CACd,KAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI,EAAE;QACpD,MAAM,IAAI,SAAS,CAAC,yBAAyB,GAAG,IAAI,GAAG,WAAW,CAAC,CAAC;OACrE,CAAC,OAAO,KAAK,CAAC;CACnB,IAAG,CAAC;CACJ,EAAC,CAAC;AACF;CACA,CAAA,IAAI,eAAe,IAAI,MAAM,CAAC,KAAK,EAAE;CACrC,GAAE,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,GAAG,IAAI,OAAO,EAAE,CAAC,CAAC;CAC7D;CACA,GAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACxB,GAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACxB,GAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACxB;CACA,GAAE,GAAG,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE;CAChC,KAAI,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAC;CACvE,KAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC;MACrB,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;MACxB,OAAO,QAAQ,CAAC;CACpB,IAAG,CAAC;CACJ,GAAE,GAAG,GAAG,UAAU,EAAE,EAAE;MAClB,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;CAC/B,IAAG,CAAC;CACJ,GAAE,GAAG,GAAG,UAAU,EAAE,EAAE;CACtB,KAAI,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;CACzB,IAAG,CAAC;CACJ,EAAC,MAAM;CACP,GAAE,IAAI,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;CACjC,GAAE,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;CAC3B,GAAE,GAAG,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE;CAChC,KAAI,IAAI,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAC;CAC3E,KAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC;MACrB,2BAA2B,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;MACjD,OAAO,QAAQ,CAAC;CACpB,IAAG,CAAC;CACJ,GAAE,GAAG,GAAG,UAAU,EAAE,EAAE;CACtB,KAAI,OAAO,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;CAC9C,IAAG,CAAC;CACJ,GAAE,GAAG,GAAG,UAAU,EAAE,EAAE;CACtB,KAAI,OAAO,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;CAC7B,IAAG,CAAC;GACH;AACD;CACA,CAAA,aAAc,GAAG;IACf,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,OAAO,EAAE,OAAO;IAChB,SAAS,EAAE,SAAS;GACrB,CAAA;;;;;;;;;ECrED,IAAI,WAAW,GAAGR,0BAAA,EAA6C,CAAC;EAChE,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;EAC1C,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;EACrD,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;EACtD,IAAI,WAAW,GAAGE,kBAAA,EAAmC,CAAC;CACtD,CAAA,IAAI,0BAA0B,GAAGC,mBAAqC,EAAA,CAAC,YAAY,CAAC;EACpF,IAAI,aAAa,GAAGC,oBAAA,EAAsC,CAAC;EAC3D,IAAI,mBAAmB,GAAGC,oBAAA,EAAsC,CAAC;AACjE;CACA,CAAA,IAAI,oBAAoB,GAAG,mBAAmB,CAAC,OAAO,CAAC;CACvD,CAAA,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,GAAG,CAAC;EAC/C,IAAI,OAAO,GAAG,MAAM,CAAC;CACrB;CACA,CAAA,IAAI,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;EAC3C,IAAI,WAAW,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;EACxC,IAAI,OAAO,GAAG,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;EACtC,IAAI,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAChC;CACA,CAAA,IAAI,mBAAmB,GAAG,WAAW,IAAI,CAAC,KAAK,CAAC,YAAY;CAC5D,GAAE,OAAO,cAAc,CAAC,YAAY,eAAe,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;CAC1F,EAAC,CAAC,CAAC;AACH;EACA,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC9C;EACA,IAAIC,aAAW,GAAGC,WAAA,CAAA,OAAc,GAAG,UAAU,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;CACnE,GAAE,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE;CACtD,KAAI,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,uBAAuB,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;KAC1E;CACH,GAAE,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;CACtD,GAAE,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;CACtD,GAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,0BAA0B,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;CACrF,KAAI,IAAI,WAAW,EAAE,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;CACxF,UAAS,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;KACxB;CACH,GAAE,IAAI,mBAAmB,IAAI,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,KAAK,EAAE;CACpG,KAAI,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;KAC3D;CACH,GAAE,IAAI;CACN,KAAI,IAAI,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE;CAC1E,OAAM,IAAI,WAAW,EAAE,cAAc,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;CAC/E;OACK,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;CAC5D,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;CACjC,GAAE,IAAI,KAAK,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;CAChC,KAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;KACpE,CAAC,OAAO,KAAK,CAAC;CACjB,EAAC,CAAC;AACF;CACA;CACA;EACA,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAGD,aAAW,CAAC,SAAS,QAAQ,GAAG;CAC9D,GAAE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;GACjF,EAAE,UAAU,CAAC,CAAA;;;;;;;;;;ECrDd,IAAI,UAAU,GAAGT,iBAAA,EAAmC,CAAC;EACrD,IAAI,oBAAoB,GAAGC,2BAAA,EAA8C,CAAC;EAC1E,IAAI,WAAW,GAAGC,kBAAA,EAAqC,CAAC;EACxD,IAAI,oBAAoB,GAAGC,2BAAA,EAA8C,CAAC;AAC1E;CACA,CAAc,aAAA,GAAG,UAAU,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;CACnD,GAAE,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,CAAC;CAC7B,GAAE,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;CAClC,GAAE,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,GAAG,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC;CAC7D,GAAE,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;CAC3D,GAAE,IAAI,OAAO,CAAC,MAAM,EAAE;MAClB,IAAI,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;CAC/B,UAAS,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;CAC1C,IAAG,MAAM;CACT,KAAI,IAAI;QACF,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACrC,MAAK,CAAC,OAAO,KAAK,EAAE,eAAe;MAC/B,IAAI,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;CAC/B,UAAS,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;QAClC,KAAK,EAAE,KAAK;QACZ,UAAU,EAAE,KAAK;CACvB,OAAM,YAAY,EAAE,CAAC,OAAO,CAAC,eAAe;CAC5C,OAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW;CACpC,MAAK,CAAC,CAAC;KACJ,CAAC,OAAO,CAAC,CAAC;GACZ,CAAA;;;;;;;;;;;;CC1BD,CAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACrB,CAAA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB;CACA;CACA;CACA;CACA,CAAc,SAAA,GAAG,IAAI,CAAC,KAAK,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;CACjD,GAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;CACb,GAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;GAClC,CAAA;;;;;;;;;;ECTD,IAAI,KAAK,GAAGH,gBAAA,EAAkC,CAAC;AAC/C;CACA;CACA;CACA,CAAc,mBAAA,GAAG,UAAU,QAAQ,EAAE;CACrC,GAAE,IAAI,MAAM,GAAG,CAAC,QAAQ,CAAC;CACzB;CACA,GAAE,OAAO,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;GAC9D,CAAA;;;;;;;;;;ECRD,IAAI,mBAAmB,GAAGA,0BAAA,EAA8C,CAAC;AACzE;CACA,CAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;CACnB,CAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACnB;CACA;CACA;CACA;CACA,CAAA,eAAc,GAAG,UAAU,KAAK,EAAE,MAAM,EAAE;CAC1C,GAAE,IAAI,OAAO,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACzC,OAAO,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;GACtE,CAAA;;;;;;;;;;ECXD,IAAI,mBAAmB,GAAGA,0BAAA,EAA8C,CAAC;AACzE;CACA,CAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACnB;CACA;CACA;CACA,CAAc,QAAA,GAAG,UAAU,QAAQ,EAAE;CACrC,GAAE,IAAI,GAAG,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;CAC1C,GAAE,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC;GACjD,CAAA;;;;;;;;;;ECTD,IAAI,QAAQ,GAAGA,eAAA,EAAiC,CAAC;AACjD;CACA;CACA;CACA,CAAc,iBAAA,GAAG,UAAU,GAAG,EAAE;CAChC,GAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;GAC7B,CAAA;;;;;;;;;;ECND,IAAI,eAAe,GAAGA,sBAAA,EAAyC,CAAC;EAChE,IAAI,eAAe,GAAGC,sBAAA,EAAyC,CAAC;EAChE,IAAI,iBAAiB,GAAGC,wBAAA,EAA4C,CAAC;AACrE;CACA;CACA,CAAA,IAAI,YAAY,GAAG,UAAU,WAAW,EAAE;CAC1C,GAAE,OAAO,UAAU,KAAK,EAAE,EAAE,EAAE,SAAS,EAAE;CACzC,KAAI,IAAI,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;CACnC,KAAI,IAAI,MAAM,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAClC,IAAI,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;MAC5C,IAAI,KAAK,GAAG,eAAe,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;MAC/C,IAAI,KAAK,CAAC;CACd;CACA;MACI,IAAI,WAAW,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,MAAM,GAAG,KAAK,EAAE;CACzD,OAAM,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;CACzB;CACA,OAAM,IAAI,KAAK,KAAK,KAAK,EAAE,OAAO,IAAI,CAAC;CACvC;OACK,MAAM,MAAM,MAAM,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;QACpC,IAAI,CAAC,WAAW,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,WAAW,IAAI,KAAK,IAAI,CAAC,CAAC;CAC3F,MAAK,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;CAChC,IAAG,CAAC;CACJ,EAAC,CAAC;AACF;CACA,CAAA,aAAc,GAAG;CACjB;CACA;CACA,GAAE,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC;CAC9B;CACA;CACA,GAAE,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC;GAC7B,CAAA;;;;;;;;;;EChCD,IAAI,WAAW,GAAGF,0BAAA,EAA6C,CAAC;EAChE,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;EACtD,IAAI,eAAe,GAAGC,sBAAA,EAAyC,CAAC;CAChE,CAAA,IAAI,OAAO,GAAGC,oBAAsC,EAAA,CAAC,OAAO,CAAC;EAC7D,IAAI,UAAU,GAAGE,iBAAA,EAAmC,CAAC;AACrD;EACA,IAAI,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAChC;CACA,CAAA,kBAAc,GAAG,UAAU,MAAM,EAAE,KAAK,EAAE;CAC1C,GAAE,IAAI,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;CAClC,GAAE,IAAI,CAAC,GAAG,CAAC,CAAC;CACZ,GAAE,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,GAAG,CAAC;IACR,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACjF;IACE,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;CAC5D,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAC5C;IACD,OAAO,MAAM,CAAC;GACf,CAAA;;;;;;;;;;CCnBD;CACA,CAAA,WAAc,GAAG;CACjB,GAAE,aAAa;CACf,GAAE,gBAAgB;CAClB,GAAE,eAAe;CACjB,GAAE,sBAAsB;CACxB,GAAE,gBAAgB;CAClB,GAAE,UAAU;CACZ,GAAE,SAAS;GACV,CAAA;;;;;;;;;ECTD,IAAI,kBAAkB,GAAGL,yBAAA,EAA4C,CAAC;EACtE,IAAI,WAAW,GAAGC,kBAAA,EAAqC,CAAC;AACxD;EACA,IAAI,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC3D;CACA;CACA;CACA;CACA,CAAS,yBAAA,CAAA,CAAA,GAAG,MAAM,CAAC,mBAAmB,IAAI,SAAS,mBAAmB,CAAC,CAAC,EAAE;CAC1E,GAAE,OAAO,kBAAkB,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;GAC1C,CAAA;;;;;;;;;;;CCVD;CACA,CAAS,2BAAA,CAAA,CAAA,GAAG,MAAM,CAAC,qBAAqB,CAAA;;;;;;;;;;ECDxC,IAAI,UAAU,GAAGD,iBAAA,EAAoC,CAAC;EACtD,IAAI,WAAW,GAAGC,0BAAA,EAA6C,CAAC;EAChE,IAAI,yBAAyB,GAAGC,gCAAA,EAAqD,CAAC;EACtF,IAAI,2BAA2B,GAAGC,kCAAA,EAAuD,CAAC;EAC1F,IAAI,QAAQ,GAAGE,eAAA,EAAiC,CAAC;AACjD;EACA,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACpC;CACA;CACA,CAAA,OAAc,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,SAAS,OAAO,CAAC,EAAE,EAAE;CAC1E,GAAE,IAAI,IAAI,GAAG,yBAAyB,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;CACvD,GAAE,IAAI,qBAAqB,GAAG,2BAA2B,CAAC,CAAC,CAAC;CAC5D,GAAE,OAAO,qBAAqB,GAAG,MAAM,CAAC,IAAI,EAAE,qBAAqB,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;GAC/E,CAAA;;;;;;;;;;ECbD,IAAI,MAAM,GAAGL,qBAAA,EAAwC,CAAC;EACtD,IAAI,OAAO,GAAGC,cAAA,EAAgC,CAAC;EAC/C,IAAI,8BAA8B,GAAGC,qCAAA,EAA0D,CAAC;EAChG,IAAI,oBAAoB,GAAGC,2BAAA,EAA8C,CAAC;AAC1E;CACA,CAAA,yBAAc,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;CACvD,GAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAC7B,GAAE,IAAI,cAAc,GAAG,oBAAoB,CAAC,CAAC,CAAC;CAC9C,GAAE,IAAI,wBAAwB,GAAG,8BAA8B,CAAC,CAAC,CAAC;CAClE,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACxC,KAAI,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;CACtB,KAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE;CAC1E,OAAM,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;OACpE;KACF;GACF,CAAA;;;;;;;;;;ECfD,IAAI,KAAK,GAAGH,YAAA,EAA6B,CAAC;EAC1C,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;AACrD;EACA,IAAI,WAAW,GAAG,iBAAiB,CAAC;AACpC;CACA,CAAA,IAAI,QAAQ,GAAG,UAAU,OAAO,EAAE,SAAS,EAAE;IAC3C,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;CACvC,GAAE,OAAO,KAAK,KAAK,QAAQ,GAAG,IAAI;CAClC,OAAM,KAAK,KAAK,MAAM,GAAG,KAAK;QACxB,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC;QACxC,CAAC,CAAC,SAAS,CAAC;CAClB,EAAC,CAAC;AACF;EACA,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,UAAU,MAAM,EAAE;CACvD,GAAE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;CAChE,EAAC,CAAC;AACF;CACA,CAAA,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;CAC9B,CAAA,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;CACnC,CAAA,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;AACvC;CACA,CAAA,UAAc,GAAG,QAAQ,CAAA;;;;;;;;;;ECrBzB,IAAI,MAAM,GAAGD,aAAA,EAA8B,CAAC;CAC5C,CAAA,IAAI,wBAAwB,GAAGC,qCAA0D,EAAA,CAAC,CAAC,CAAC;EAC5F,IAAI,2BAA2B,GAAGC,kCAAA,EAAsD,CAAC;EACzF,IAAI,aAAa,GAAGC,oBAAA,EAAuC,CAAC;EAC5D,IAAI,oBAAoB,GAAGE,2BAAA,EAA8C,CAAC;EAC1E,IAAI,yBAAyB,GAAGC,gCAAA,EAAmD,CAAC;EACpF,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;AACjD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,OAAc,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE;CAC5C,GAAE,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CAC9B,GAAE,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CAC9B,GAAE,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAC5B,GAAE,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,CAAC;IACpE,IAAI,MAAM,EAAE;MACV,MAAM,GAAG,MAAM,CAAC;KACjB,MAAM,IAAI,MAAM,EAAE;CACrB,KAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;CAChE,IAAG,MAAM;CACT,KAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;KACrD;CACH,GAAE,IAAI,MAAM,EAAE,KAAK,GAAG,IAAI,MAAM,EAAE;CAClC,KAAI,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;CACjC,KAAI,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1B,UAAU,GAAG,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACzD,OAAM,cAAc,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC;CACtD,MAAK,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;MACpC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;CAC1F;CACA,KAAI,IAAI,CAAC,MAAM,IAAI,cAAc,KAAK,SAAS,EAAE;CACjD,OAAM,IAAI,OAAO,cAAc,IAAI,OAAO,cAAc,EAAE,SAAS;CACnE,OAAM,yBAAyB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;OAC3D;CACL;MACI,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;QAC3D,2BAA2B,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;OAC3D;MACD,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;KACrD;GACF,CAAA;;;;;;;;;;ECrDD,IAAI,OAAO,GAAGP,iBAAA,EAAmC,CAAC;AAClD;CACA;CACA;CACA;CACA,CAAc,OAAA,GAAG,KAAK,CAAC,OAAO,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;CAC7D,GAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,OAAO,CAAC;GACtC,CAAA;;;;;;;;;;ECPD,IAAI,WAAW,GAAGA,kBAAA,EAAmC,CAAC;EACtD,IAAI,OAAO,GAAGC,cAAA,EAAgC,CAAC;AAC/C;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;CAC3B;CACA,CAAA,IAAI,wBAAwB,GAAG,MAAM,CAAC,wBAAwB,CAAC;AAC/D;CACA;CACA,CAAA,IAAI,iCAAiC,GAAG,WAAW,IAAI,CAAC,YAAY;CACpE;CACA,GAAE,IAAI,IAAI,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC;CACtC,GAAE,IAAI;CACN;CACA,KAAI,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrE,CAAC,OAAO,KAAK,EAAE;CAClB,KAAI,OAAO,KAAK,YAAY,SAAS,CAAC;KACnC;CACH,EAAC,EAAE,CAAC;AACJ;CACA,CAAA,cAAc,GAAG,iCAAiC,GAAG,UAAU,CAAC,EAAE,MAAM,EAAE;CAC1E,GAAE,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE;CACrE,KAAI,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;CACzD,IAAG,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;CAC7B,EAAC,GAAG,UAAU,CAAC,EAAE,MAAM,EAAE;CACzB,GAAE,OAAO,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;GAC1B,CAAA;;;;;;;;;;ECzBD,IAAI,UAAU,GAAG,SAAS,CAAC;EAC3B,IAAI,gBAAgB,GAAG,gBAAgB,CAAC;AACxC;CACA,CAAc,wBAAA,GAAG,UAAU,EAAE,EAAE;IAC7B,IAAI,EAAE,GAAG,gBAAgB,EAAE,MAAM,UAAU,CAAC,gCAAgC,CAAC,CAAC;IAC9E,OAAO,EAAE,CAAC;GACX,CAAA;;;;;;;;;ECND,IAAI,CAAC,GAAGD,cAAA,EAA8B,CAAC;EACvC,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,iBAAiB,GAAGC,wBAAA,EAA4C,CAAC;EACrE,IAAI,cAAc,GAAGC,qBAAA,EAAwC,CAAC;EAC9D,IAAI,wBAAwB,GAAGE,+BAAA,EAAoD,CAAC;EACpF,IAAI,KAAK,GAAGC,YAAA,EAA6B,CAAC;AAC1C;CACA,CAAA,IAAI,mBAAmB,GAAG,KAAK,CAAC,YAAY;CAC5C,GAAE,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,KAAK,UAAU,CAAC;CACjE,EAAC,CAAC,CAAC;AACH;CACA;CACA;EACA,IAAI,8BAA8B,GAAG,YAAY;CACjD,GAAE,IAAI;CACN;CACA,KAAI,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;KACjE,CAAC,OAAO,KAAK,EAAE;CAClB,KAAI,OAAO,KAAK,YAAY,SAAS,CAAC;KACnC;CACH,EAAC,CAAC;AACF;CACA,CAAA,IAAI,MAAM,GAAG,mBAAmB,IAAI,CAAC,8BAA8B,EAAE,CAAC;AACtE;CACA;CACA;CACA,CAAA,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;CAC9D;CACA,GAAE,IAAI,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE;CAC5B,KAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC3B,KAAI,IAAI,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;CACnC,KAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;CACpC,KAAI,wBAAwB,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC;CAC7C,KAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;QACjC,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACtB,GAAG,EAAE,CAAC;OACP;CACL,KAAI,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MACvB,OAAO,GAAG,CAAC;KACZ;CACH,EAAC,CAAC,CAAA;;;;;;CCzCF;CACe,SAASK,gBAAgBA,CAAEC,CAAC,EAAEC,CAAC,EAAE;CAC/C,EAAA,IAAIC,CAAC,GAAGF,CAAC,CAACG,MAAM,CAAA;GAEhB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CACzB;KACAA,CAAC,GAAG,CAACA,CAAC,CAAC,CAAA;CACR,GAAA;GAEA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;CACzB;KACAA,CAAC,GAAGA,CAAC,CAACK,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAA;CACpB,GAAA;CAEA,EAAA,IAAIC,CAAC,GAAGP,CAAC,CAAC,CAAC,CAAC,CAACE,MAAM,CAAA;GACnB,IAAIM,MAAM,GAAGR,CAAC,CAAC,CAAC,CAAC,CAACK,GAAG,CAAC,CAACI,CAAC,EAAEC,CAAC,KAAKV,CAAC,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;CAClD,EAAA,IAAIC,OAAO,GAAGZ,CAAC,CAACM,GAAG,CAACO,GAAG,IAAIJ,MAAM,CAACH,GAAG,CAACQ,GAAG,IAAI;KAC5C,IAAIC,GAAG,GAAG,CAAC,CAAA;CAEX,IAAA,IAAI,CAACX,KAAK,CAACC,OAAO,CAACQ,GAAG,CAAC,EAAE;CACxB,MAAA,KAAK,IAAIG,CAAC,IAAIF,GAAG,EAAE;SAClBC,GAAG,IAAIF,GAAG,GAAGG,CAAC,CAAA;CACf,OAAA;CAEA,MAAA,OAAOD,GAAG,CAAA;CACX,KAAA;CAEA,IAAA,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,GAAG,CAACV,MAAM,EAAEQ,CAAC,EAAE,EAAE;CACpCI,MAAAA,GAAG,IAAIF,GAAG,CAACF,CAAC,CAAC,IAAIG,GAAG,CAACH,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;CAC9B,KAAA;CAEA,IAAA,OAAOI,GAAG,CAAA;CACX,GAAC,CAAC,CAAC,CAAA;GAEH,IAAIb,CAAC,KAAK,CAAC,EAAE;CACZU,IAAAA,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;CACtB,GAAA;GAEA,IAAIJ,CAAC,KAAK,CAAC,EAAE;CACZ,IAAA,OAAOI,OAAO,CAACN,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC/B,GAAA;CAEA,EAAA,OAAOK,OAAO,CAAA;CACf;;CCrCA;CACA;CACA;CACA;CACA;CACO,SAASK,QAAQA,CAAEC,GAAG,EAAE;CAC9B,EAAA,OAAOC,IAAI,CAACD,GAAG,CAAC,KAAK,QAAQ,CAAA;CAC9B,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACO,SAASC,IAAIA,CAAEC,CAAC,EAAE;GACxB,IAAIF,GAAG,GAAGG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,CAAA;CAE3C,EAAA,OAAO,CAACF,GAAG,CAACO,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,WAAW,EAAE,CAAA;CAClE,CAAA;CAEO,SAASC,eAAeA,CAAEC,CAAC,EAAAC,IAAA,EAAsB;GAAA,IAApB;KAACC,SAAS;CAAEC,IAAAA,IAAAA;CAAK,GAAC,GAAAF,IAAA,CAAA;CACrD,EAAA,IAAIG,MAAM,CAACJ,CAAC,CAAC,EAAE;CACd,IAAA,OAAO,MAAM,CAAA;CACd,GAAA;CAEA,EAAA,OAAOK,WAAW,CAACL,CAAC,EAAEE,SAAS,CAAC,IAAIC,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAA,KAAA,CAAA,GAAJA,IAAI,GAAI,EAAE,CAAC,CAAA;CAChD,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACO,SAASC,MAAMA,CAAEJ,CAAC,EAAE;CAC1B,EAAA,OAAOM,MAAM,CAACC,KAAK,CAACP,CAAC,CAAC,IAAKA,CAAC,YAAYM,MAAM,KAAIN,CAAC,KAADA,IAAAA,IAAAA,CAAC,uBAADA,CAAC,CAAEQ,IAAI,CAAC,CAAA;CAC3D,CAAA;;CAEA;CACA;CACA;CACO,SAASC,QAAQA,CAAET,CAAC,EAAE;CAC5B,EAAA,OAAOI,MAAM,CAACJ,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAA;CACzB,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACO,SAASK,WAAWA,CAAEL,CAAC,EAAEE,SAAS,EAAE;GAC1C,IAAIF,CAAC,KAAK,CAAC,EAAE;CACZ,IAAA,OAAO,CAAC,CAAA;CACT,GAAA;CACA,EAAA,IAAIU,OAAO,GAAG,CAAC,CAACV,CAAC,CAAA;GACjB,IAAIW,MAAM,GAAG,CAAC,CAAA;GACd,IAAID,OAAO,IAAIR,SAAS,EAAE;CACzBS,IAAAA,MAAM,GAAG,CAAC,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACJ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;CAC7C,GAAA;CACA,EAAA,MAAMK,UAAU,GAAG,IAAI,KAAKb,SAAS,GAAGS,MAAM,CAAC,CAAA;GAC/C,OAAOC,IAAI,CAACI,KAAK,CAAChB,CAAC,GAAGe,UAAU,GAAG,GAAG,CAAC,GAAGA,UAAU,CAAA;CACrD,CAAA;CAEA,MAAME,WAAW,GAAG;CACnBC,EAAAA,GAAG,EAAE,CAAC;CACNC,EAAAA,IAAI,EAAE,GAAG;CACTC,EAAAA,GAAG,EAAE,GAAG,GAAGR,IAAI,CAACS,EAAE;CAClBC,EAAAA,IAAI,EAAE,GAAA;CACP,CAAC,CAAA;;CAED;CACA;CACA;CACA;CACA;CACO,SAASC,aAAaA,CAAEjC,GAAG,EAAE;GACnC,IAAI,CAACA,GAAG,EAAE;CACT,IAAA,OAAA;CACD,GAAA;CAEAA,EAAAA,GAAG,GAAGA,GAAG,CAACkC,IAAI,EAAE,CAAA;GAEhB,MAAMC,eAAe,GAAG,sBAAsB,CAAA;GAC9C,MAAMC,aAAa,GAAG,YAAY,CAAA;GAClC,MAAMC,cAAc,GAAG,mBAAmB,CAAA;GAC1C,MAAMC,cAAc,GAAG,4CAA4C,CAAA;CACnE,EAAA,IAAIC,KAAK,GAAGvC,GAAG,CAACO,KAAK,CAAC4B,eAAe,CAAC,CAAA;CAEtC,EAAA,IAAII,KAAK,EAAE;CACV;KACA,IAAIC,IAAI,GAAG,EAAE,CAAA;CACbD,IAAAA,KAAK,CAAC,CAAC,CAAC,CAACE,OAAO,CAACH,cAAc,EAAE,CAACI,EAAE,EAAEC,MAAM,KAAK;CAChD,MAAA,IAAIpC,KAAK,GAAGoC,MAAM,CAACpC,KAAK,CAAC8B,cAAc,CAAC,CAAA;OACxC,IAAIO,GAAG,GAAGD,MAAM,CAAA;CAEhB,MAAA,IAAIpC,KAAK,EAAE;CACV,QAAA,IAAIM,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC,CAAA;CACnB;CACA,QAAA,IAAIsC,WAAW,GAAGD,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAACjC,IAAI,CAAC5B,MAAM,CAAC,CAAA;SAE5C,IAAI4B,IAAI,KAAK,GAAG,EAAE;CACjB;CACA+B,UAAAA,GAAG,GAAG,IAAI5B,MAAM,CAAC6B,WAAW,GAAG,GAAG,CAAC,CAAA;WACnCD,GAAG,CAAC3C,IAAI,GAAG,cAAc,CAAA;CAC1B,SAAC,MACI;CACJ;WACA2C,GAAG,GAAG,IAAI5B,MAAM,CAAC6B,WAAW,GAAGlB,WAAW,CAACd,IAAI,CAAC,CAAC,CAAA;WACjD+B,GAAG,CAAC3C,IAAI,GAAG,SAAS,CAAA;WACpB2C,GAAG,CAAC/B,IAAI,GAAGA,IAAI,CAAA;CAChB,SAAA;QACA,MACI,IAAIuB,aAAa,CAACW,IAAI,CAACH,GAAG,CAAC,EAAE;CACjC;CACAA,QAAAA,GAAG,GAAG,IAAI5B,MAAM,CAAC4B,GAAG,CAAC,CAAA;SACrBA,GAAG,CAAC3C,IAAI,GAAG,UAAU,CAAA;CACtB,OAAC,MACI,IAAI2C,GAAG,KAAK,MAAM,EAAE;CACxBA,QAAAA,GAAG,GAAG,IAAI5B,MAAM,CAACgC,GAAG,CAAC,CAAA;SACrBJ,GAAG,CAAC1B,IAAI,GAAG,IAAI,CAAA;CAChB,OAAA;CAEA,MAAA,IAAIwB,EAAE,CAACO,UAAU,CAAC,GAAG,CAAC,EAAE;CACvB;SACAL,GAAG,GAAGA,GAAG,YAAY5B,MAAM,GAAG4B,GAAG,GAAG,IAAI5B,MAAM,CAAC4B,GAAG,CAAC,CAAA;SACnDA,GAAG,CAACM,KAAK,GAAG,IAAI,CAAA;CACjB,OAAA;OAEA,IAAI,OAAON,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAY5B,MAAM,EAAE;SACrD4B,GAAG,CAACO,GAAG,GAAGR,MAAM,CAAA;CACjB,OAAA;CAEAH,MAAAA,IAAI,CAACY,IAAI,CAACR,GAAG,CAAC,CAAA;CACf,KAAC,CAAC,CAAA;KAEF,OAAO;OACNS,IAAI,EAAEd,KAAK,CAAC,CAAC,CAAC,CAAC/B,WAAW,EAAE;CAC5B8C,MAAAA,OAAO,EAAEf,KAAK,CAAC,CAAC,CAAC;CACjBgB,MAAAA,OAAO,EAAEhB,KAAK,CAAC,CAAC,CAAC;CACjB;CACA;CACAC,MAAAA,IAAAA;MACA,CAAA;CACF,GAAA;CACD,CAAA;CAEO,SAASgB,IAAIA,CAAEC,GAAG,EAAE;CAC1B,EAAA,OAAOA,GAAG,CAACA,GAAG,CAACxE,MAAM,GAAG,CAAC,CAAC,CAAA;CAC3B,CAAA;CAEO,SAASyE,WAAWA,CAAEC,KAAK,EAAEC,GAAG,EAAEtE,CAAC,EAAE;CAC3C,EAAA,IAAI2B,KAAK,CAAC0C,KAAK,CAAC,EAAE;CACjB,IAAA,OAAOC,GAAG,CAAA;CACX,GAAA;CAEA,EAAA,IAAI3C,KAAK,CAAC2C,GAAG,CAAC,EAAE;CACf,IAAA,OAAOD,KAAK,CAAA;CACb,GAAA;CAEA,EAAA,OAAOA,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAK,IAAIrE,CAAC,CAAA;CACjC,CAAA;CAEO,SAASuE,cAAcA,CAAEF,KAAK,EAAEC,GAAG,EAAEE,KAAK,EAAE;GAClD,OAAO,CAACA,KAAK,GAAGH,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC,CAAA;CACvC,CAAA;CAEO,SAASI,QAAQA,CAAEC,IAAI,EAAEC,EAAE,EAAEH,KAAK,EAAE;GAC1C,OAAOJ,WAAW,CAACO,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEJ,cAAc,CAACG,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAA;CAC1E,CAAA;CAEO,SAASI,iBAAiBA,CAAEC,aAAa,EAAE;CACjD,EAAA,OAAOA,aAAa,CAAC/E,GAAG,CAACgF,YAAY,IAAI;KACxC,OAAOA,YAAY,CAACC,KAAK,CAAC,GAAG,CAAC,CAACjF,GAAG,CAACa,IAAI,IAAI;CAC1CA,MAAAA,IAAI,GAAGA,IAAI,CAACiC,IAAI,EAAE,CAAA;CAClB,MAAA,IAAIoC,KAAK,GAAGrE,IAAI,CAACM,KAAK,CAAC,2CAA2C,CAAC,CAAA;CAEnE,MAAA,IAAI+D,KAAK,EAAE;SACV,IAAIzE,GAAG,GAAG,IAAI0E,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAC9BzE,QAAAA,GAAG,CAACyE,KAAK,GAAG,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAClC,QAAA,OAAOzE,GAAG,CAAA;CACX,OAAA;CAEA,MAAA,OAAOI,IAAI,CAAA;CACZ,KAAC,CAAC,CAAA;CACH,GAAC,CAAC,CAAA;CACH,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAASuE,KAAKA,CAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;CACrC,EAAA,OAAOrD,IAAI,CAACqD,GAAG,CAACrD,IAAI,CAACmD,GAAG,CAACE,GAAG,EAAED,GAAG,CAAC,EAAED,GAAG,CAAC,CAAA;CACzC,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACO,SAASG,QAAQA,CAAEX,EAAE,EAAED,IAAI,EAAE;CACnC,EAAA,OAAO1C,IAAI,CAACuD,IAAI,CAACZ,EAAE,CAAC,KAAK3C,IAAI,CAACuD,IAAI,CAACb,IAAI,CAAC,GAAGC,EAAE,GAAG,CAACA,EAAE,CAAA;CACpD,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACO,SAASa,IAAIA,CAAEC,IAAI,EAAEC,GAAG,EAAE;CAChC,EAAA,OAAOJ,QAAQ,CAACtD,IAAI,CAACE,GAAG,CAACuD,IAAI,CAAC,IAAIC,GAAG,EAAED,IAAI,CAAC,CAAA;CAC7C,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACO,SAASE,IAAIA,CAAEvE,CAAC,EAAEwE,CAAC,EAAE;GAC3B,OAAQA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAGxE,CAAC,GAAGwE,CAAC,CAAA;CAC7B,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAASC,UAAUA,CAAE1B,GAAG,EAAEK,KAAK,EAA2B;CAAA,EAAA,IAAzBsB,EAAE,GAAAC,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC,CAAA;CAAA,EAAA,IAAEE,EAAE,GAAAF,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG5B,CAAAA,CAAAA,GAAAA,GAAG,CAACxE,MAAM,CAAA;GAC9D,OAAOmG,EAAE,GAAGG,EAAE,EAAE;CACf,IAAA,MAAMC,GAAG,GAAIJ,EAAE,GAAGG,EAAE,IAAK,CAAC,CAAA;CAC1B,IAAA,IAAI9B,GAAG,CAAC+B,GAAG,CAAC,GAAG1B,KAAK,EAAE;OACrBsB,EAAE,GAAGI,GAAG,GAAG,CAAC,CAAA;CACb,KAAC,MACI;CACJD,MAAAA,EAAE,GAAGC,GAAG,CAAA;CACT,KAAA;CACD,GAAA;CACA,EAAA,OAAOJ,EAAE,CAAA;CACV;;;;;;;;;;;;;;;;;;;;;;;;CC7PA;CACA;CACA;CACO,MAAMK,KAAK,CAAC;CAClBC,EAAAA,GAAGA,CAAErC,IAAI,EAAEsC,QAAQ,EAAEC,KAAK,EAAE;CAC3B,IAAA,IAAI,OAAOP,SAAS,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;CACpC;CACA,MAAA,KAAK,IAAIhC,IAAI,IAAIgC,SAAS,CAAC,CAAC,CAAC,EAAE;CAC9B,QAAA,IAAI,CAACK,GAAG,CAACrC,IAAI,EAAEgC,SAAS,CAAC,CAAC,CAAC,CAAChC,IAAI,CAAC,EAAEgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;CACjD,OAAA;CAEA,MAAA,OAAA;CACD,KAAA;CAEA,IAAA,CAACnG,KAAK,CAACC,OAAO,CAACkE,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,EAAEwC,OAAO,CAAC,UAAUxC,IAAI,EAAE;OAC7D,IAAI,CAACA,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC,IAAI,EAAE,CAAA;CAE7B,MAAA,IAAIsC,QAAQ,EAAE;CACb,QAAA,IAAI,CAACtC,IAAI,CAAC,CAACuC,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC,CAACD,QAAQ,CAAC,CAAA;CACjD,OAAA;MACA,EAAE,IAAI,CAAC,CAAA;CACT,GAAA;CAEAG,EAAAA,GAAGA,CAAEzC,IAAI,EAAE0C,GAAG,EAAE;KACf,IAAI,CAAC1C,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC,IAAI,EAAE,CAAA;KAC7B,IAAI,CAACA,IAAI,CAAC,CAACwC,OAAO,CAAC,UAAUF,QAAQ,EAAE;CACtCA,MAAAA,QAAQ,CAACrF,IAAI,CAACyF,GAAG,IAAIA,GAAG,CAACC,OAAO,GAAGD,GAAG,CAACC,OAAO,GAAGD,GAAG,EAAEA,GAAG,CAAC,CAAA;CAC3D,KAAC,CAAC,CAAA;CACH,GAAA;CACD,CAAA;;CAEA;CACA;CACA;CACA,MAAME,KAAK,GAAG,IAAIR,KAAK,EAAE;;;CClCzB;AACA,gBAAe;CACdS,EAAAA,aAAa,EAAE,KAAK;CACpBtF,EAAAA,SAAS,EAAE,CAAC;CACZuF,EAAAA,MAAM,EAAE,IAAI;CAAE;CACdC,EAAAA,OAAO,EAAE,CAAAC,UAAU,KAAVA,IAAAA,IAAAA,UAAU,gBAAAC,mBAAA,GAAVD,UAAU,CAAEE,OAAO,MAAAD,IAAAA,IAAAA,mBAAA,KAAAA,KAAAA,CAAAA,IAAAA,CAAAA,mBAAA,GAAnBA,mBAAA,CAAqBP,GAAG,MAAA,IAAA,IAAAO,mBAAA,KAAAA,KAAAA,CAAAA,IAAAA,CAAAA,mBAAA,GAAxBA,mBAAA,CAA0BE,QAAQ,MAAA,IAAA,IAAAF,mBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlCA,mBAAA,CAAoC9F,WAAW,EAAE,MAAK,MAAM;CACrEiG,EAAAA,IAAI,EAAE,SAASA,IAAIA,CAAEC,GAAG,EAAE;KACzB,IAAI,IAAI,CAACN,OAAO,EAAE;OAAA,IAAAO,mBAAA,EAAAC,qBAAA,CAAA;OACjBP,UAAU,KAAA,IAAA,IAAVA,UAAU,KAAA,KAAA,CAAA,IAAA,CAAAM,mBAAA,GAAVN,UAAU,CAAEQ,OAAO,MAAA,IAAA,IAAAF,mBAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAAnBD,mBAAA,CAAqBF,IAAI,MAAAG,IAAAA,IAAAA,qBAAA,KAAzBA,KAAAA,CAAAA,IAAAA,qBAAA,CAAAtG,IAAA,CAAAqG,mBAAA,EAA4BD,GAAG,CAAC,CAAA;CACjC,KAAA;CACD,GAAA;CACD,CAAC;;;;;;;;;;ECVD,IAAI,WAAW,GAAGxI,yBAAA,EAA4C,CAAC;AAC/D;CACA,CAAA,IAAI,iBAAiB,GAAG,QAAQ,CAAC,SAAS,CAAC;CAC3C,CAAA,IAAI,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC;CACpC,CAAA,IAAI,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAClC;CACA;CACA,CAAA,aAAc,GAAG,OAAO,OAAO,IAAI,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY;IAC5G,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;CACtC,EAAC,CAAC,CAAA;;;;;;;;;;ECTF,IAAI,WAAW,GAAGA,0BAAA,EAA6C,CAAC;EAChE,IAAI,SAAS,GAAGC,gBAAA,EAAkC,CAAC;AACnD;CACA,CAAA,2BAAc,GAAG,UAAU,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;CAChD,GAAE,IAAI;CACN;CACA,KAAI,OAAO,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;CACxF,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;GAChC,CAAA;;;;;;;;;;ECRD,IAAI,QAAQ,GAAGD,eAAA,EAAiC,CAAC;AACjD;CACA,CAAc,mBAAA,GAAG,UAAU,QAAQ,EAAE;IACnC,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC;GAChD,CAAA;;;;;;;;;;ECJD,IAAI,mBAAmB,GAAGA,0BAAA,EAA6C,CAAC;AACxE;EACA,IAAI,OAAO,GAAG,MAAM,CAAC;EACrB,IAAI,UAAU,GAAG,SAAS,CAAC;AAC3B;CACA,CAAc,kBAAA,GAAG,UAAU,QAAQ,EAAE;IACnC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ,CAAC;CACrD,GAAE,MAAM,IAAI,UAAU,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,CAAC;GAC5E,CAAA;;;;;;;;;;CCRD;EACA,IAAI,mBAAmB,GAAGA,kCAAA,EAAsD,CAAC;EACjF,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,kBAAkB,GAAGC,yBAAA,EAA4C,CAAC;AACtE;CACA;CACA;CACA;CACA;CACA,CAAc,oBAAA,GAAG,MAAM,CAAC,cAAc,KAAK,WAAW,IAAI,EAAE,GAAG,YAAY;CAC3E,GAAE,IAAI,cAAc,GAAG,KAAK,CAAC;CAC7B,GAAE,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,MAAM,CAAC;CACb,GAAE,IAAI;CACN,KAAI,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;CACvE,KAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;CACrB,KAAI,cAAc,GAAG,IAAI,YAAY,KAAK,CAAC;CAC3C,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;CACjC,GAAE,OAAO,SAAS,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE;CAC3C,KAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;CAChB,KAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;MAC1B,IAAI,cAAc,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;CACzC,UAAS,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;MACzB,OAAO,CAAC,CAAC;CACb,IAAG,CAAC;GACH,EAAE,GAAG,SAAS,CAAC,CAAA;;;;;;;;;;CCzBhB,CAAA,IAAI,cAAc,GAAGF,2BAA8C,EAAA,CAAC,CAAC,CAAC;AACtE;CACA,CAAA,aAAc,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE;IAC9C,GAAG,IAAI,MAAM,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE;MAC3C,YAAY,EAAE,IAAI;MAClB,GAAG,EAAE,YAAY,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;CAC5C,KAAI,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE;CAC5C,IAAG,CAAC,CAAC;GACJ,CAAA;;;;;;;;;;ECRD,IAAI,UAAU,GAAGA,iBAAA,EAAmC,CAAC;EACrD,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,cAAc,GAAGC,2BAAA,EAA+C,CAAC;AACrE;CACA;CACA,CAAA,iBAAc,GAAG,UAAU,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE;CAClD,GAAE,IAAI,SAAS,EAAE,kBAAkB,CAAC;IAClC;CACF;CACA,KAAI,cAAc;CAClB;CACA,KAAI,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC;MACzC,SAAS,KAAK,OAAO;CACzB,KAAI,QAAQ,CAAC,kBAAkB,GAAG,SAAS,CAAC,SAAS,CAAC;CACtD,KAAI,kBAAkB,KAAK,OAAO,CAAC,SAAS;CAC5C,KAAI,cAAc,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;IAC5C,OAAO,KAAK,CAAC;GACd,CAAA;;;;;;;;;;ECjBD,IAAI,eAAe,GAAGF,sBAAA,EAAyC,CAAC;AAChE;CACA,CAAA,IAAI,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;EACnD,IAAI,IAAI,GAAG,EAAE,CAAC;AACd;CACA,CAAA,IAAI,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;AAC1B;CACA,CAAA,kBAAc,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,YAAY,CAAA;;;;;;;;;;ECP9C,IAAI,qBAAqB,GAAGA,yBAAA,EAA6C,CAAC;EAC1E,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;EACrD,IAAI,UAAU,GAAGC,iBAAA,EAAmC,CAAC;EACrD,IAAI,eAAe,GAAGC,sBAAA,EAAyC,CAAC;AAChE;CACA,CAAA,IAAI,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;EACnD,IAAI,OAAO,GAAG,MAAM,CAAC;AACrB;CACA;CACA,CAAA,IAAI,iBAAiB,GAAG,UAAU,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,CAAC,KAAK,WAAW,CAAC;AACxF;CACA;CACA,CAAA,IAAI,MAAM,GAAG,UAAU,EAAE,EAAE,GAAG,EAAE;CAChC,GAAE,IAAI;CACN,KAAI,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;CACnB,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;CACjC,EAAC,CAAC;AACF;CACA;CACA,CAAA,OAAc,GAAG,qBAAqB,GAAG,UAAU,GAAG,UAAU,EAAE,EAAE;CACpE,GAAE,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,KAAK,SAAS,GAAG,WAAW,GAAG,EAAE,KAAK,IAAI,GAAG,MAAM;CAC9D;CACA,OAAM,QAAQ,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,QAAQ,GAAG,GAAG;CAC7E;CACA,OAAM,iBAAiB,GAAG,UAAU,CAAC,CAAC,CAAC;CACvC;QACM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,QAAQ,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC;GAC1F,CAAA;;;;;;;;;;EC5BD,IAAI,OAAO,GAAGH,cAAA,EAA+B,CAAC;AAC9C;EACA,IAAI,OAAO,GAAG,MAAM,CAAC;AACrB;CACA,CAAc,QAAA,GAAG,UAAU,QAAQ,EAAE;CACrC,GAAE,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC;CACvG,GAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;GAC1B,CAAA;;;;;;;;;;ECPD,IAAI,QAAQ,GAAGA,eAAA,EAAiC,CAAC;AACjD;CACA,CAAA,uBAAc,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE;CAC/C,GAAE,OAAO,QAAQ,KAAK,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;GAC3F,CAAA;;;;;;;;;;ECJD,IAAI,QAAQ,GAAGA,eAAA,EAAiC,CAAC;EACjD,IAAI,2BAA2B,GAAGC,kCAAA,EAAsD,CAAC;AACzF;CACA;CACA;CACA,CAAA,iBAAc,GAAG,UAAU,CAAC,EAAE,OAAO,EAAE;IACrC,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,IAAI,OAAO,EAAE;MAC3C,2BAA2B,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;KACxD;GACF,CAAA;;;;;;;;;;ECTD,IAAI,WAAW,GAAGD,0BAAA,EAA6C,CAAC;AAChE;EACA,IAAI,MAAM,GAAG,KAAK,CAAC;EACnB,IAAI,OAAO,GAAG,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACtC;EACA,IAAI,IAAI,GAAG,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;CAChF;EACA,IAAI,wBAAwB,GAAG,sBAAsB,CAAC;EACtD,IAAI,qBAAqB,GAAG,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChE;CACA,CAAA,eAAc,GAAG,UAAU,KAAK,EAAE,WAAW,EAAE;CAC/C,GAAE,IAAI,qBAAqB,IAAI,OAAO,KAAK,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;CACtF,KAAI,OAAO,WAAW,EAAE,EAAE,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,wBAAwB,EAAE,EAAE,CAAC,CAAC;KAC5E,CAAC,OAAO,KAAK,CAAC;GAChB,CAAA;;;;;;;;;;ECdD,IAAI,KAAK,GAAGA,YAAA,EAA6B,CAAC;EAC1C,IAAI,wBAAwB,GAAGC,+BAAA,EAAkD,CAAC;AAClF;CACA,CAAA,qBAAc,GAAG,CAAC,KAAK,CAAC,YAAY;IAClC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;CACvC;CACA,GAAE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CACxE,GAAE,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;CAC3B,EAAC,CAAC,CAAA;;;;;;;;;;ECTF,IAAI,2BAA2B,GAAGD,kCAAA,EAAsD,CAAC;EACzF,IAAI,eAAe,GAAGC,sBAAA,EAAyC,CAAC;EAChE,IAAI,uBAAuB,GAAGC,4BAAA,EAA+C,CAAC;AAC9E;CACA;CACA,CAAA,IAAI,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC;AAChD;CACA,CAAc,iBAAA,GAAG,UAAU,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE;IACvD,IAAI,uBAAuB,EAAE;MAC3B,IAAI,iBAAiB,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CACvD,UAAS,2BAA2B,CAAC,KAAK,EAAE,OAAO,EAAE,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;KACvF;GACF,CAAA;;;;;;;;;;ECZD,IAAI,UAAU,GAAGF,iBAAA,EAAoC,CAAC;EACtD,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;EACtD,IAAI,2BAA2B,GAAGC,kCAAA,EAAsD,CAAC;EACzF,IAAI,aAAa,GAAGC,0BAAA,EAA8C,CAAC;EACnE,IAAI,cAAc,GAAGE,2BAAA,EAA+C,CAAC;EACrE,IAAI,yBAAyB,GAAGC,gCAAA,EAAmD,CAAC;EACpF,IAAI,aAAa,GAAGC,oBAAA,EAAsC,CAAC;EAC3D,IAAI,iBAAiB,GAAGC,wBAAA,EAA2C,CAAC;EACpE,IAAI,uBAAuB,GAAGoI,8BAAA,EAAiD,CAAC;EAChF,IAAI,iBAAiB,GAAGC,wBAAA,EAA2C,CAAC;EACpE,IAAI,iBAAiB,GAAGC,wBAAA,EAA2C,CAAC;EACpE,IAAI,WAAW,GAAGC,kBAAA,EAAmC,CAAC;EACtD,IAAI,OAAO,GAAGC,aAAA,EAA+B,CAAC;AAC9C;CACA,CAAc,6BAAA,GAAG,UAAU,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE;CAC3E,GAAE,IAAI,iBAAiB,GAAG,iBAAiB,CAAC;IAC1C,IAAI,gBAAgB,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;IAClD,IAAI,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChC,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACnD;CACA,GAAE,IAAI,CAAC,aAAa,EAAE,OAAO;AAC7B;CACA,GAAE,IAAI,sBAAsB,GAAG,aAAa,CAAC,SAAS,CAAC;AACvD;CACA;CACA,GAAE,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO,CAAC,EAAE,OAAO,sBAAsB,CAAC,KAAK,CAAC;AAC/F;CACA,GAAE,IAAI,CAAC,MAAM,EAAE,OAAO,aAAa,CAAC;AACpC;CACA,GAAE,IAAI,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;AACtC;IACE,IAAI,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;CAC7C,KAAI,IAAI,OAAO,GAAG,uBAAuB,CAAC,kBAAkB,GAAG,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;CACjF,KAAI,IAAI,MAAM,GAAG,kBAAkB,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,aAAa,EAAE,CAAC;CACjF,KAAI,IAAI,OAAO,KAAK,SAAS,EAAE,2BAA2B,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;CACvF,KAAI,iBAAiB,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CAC7D,KAAI,IAAI,IAAI,IAAI,aAAa,CAAC,sBAAsB,EAAE,IAAI,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;CAC3G,KAAI,IAAI,SAAS,CAAC,MAAM,GAAG,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;MAChG,OAAO,MAAM,CAAC;CAClB,IAAG,CAAC,CAAC;AACL;CACA,GAAE,YAAY,CAAC,SAAS,GAAG,sBAAsB,CAAC;AAClD;CACA,GAAE,IAAI,UAAU,KAAK,OAAO,EAAE;MAC1B,IAAI,cAAc,EAAE,cAAc,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;CAChE,UAAS,yBAAyB,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;CAC5E,IAAG,MAAM,IAAI,WAAW,IAAI,iBAAiB,IAAI,aAAa,EAAE;MAC5D,aAAa,CAAC,YAAY,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;MAC9D,aAAa,CAAC,YAAY,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;KACjE;AACH;CACA,GAAE,yBAAyB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AACzD;CACA,GAAE,IAAI,CAAC,OAAO,EAAE,IAAI;CACpB;CACA,KAAI,IAAI,sBAAsB,CAAC,IAAI,KAAK,UAAU,EAAE;QAC9C,2BAA2B,CAAC,sBAAsB,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;OACzE;CACL,KAAI,sBAAsB,CAAC,WAAW,GAAG,YAAY,CAAC;CACtD,IAAG,CAAC,OAAO,KAAK,EAAE,eAAe;AACjC;IACE,OAAO,YAAY,CAAC;GACrB,CAAA;;;;;;;;;CC/DD;EACA,IAAI,CAAC,GAAGhJ,cAAA,EAA8B,CAAC;EACvC,IAAI,MAAM,GAAGC,aAAA,EAA8B,CAAC;EAC5C,IAAI,KAAK,GAAGC,oBAAA,EAAsC,CAAC;EACnD,IAAI,6BAA6B,GAAGC,oCAAA,EAAyD,CAAC;AAC9F;EACA,IAAI,YAAY,GAAG,aAAa,CAAC;CACjC,CAAA,IAAI,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AACvC;CACA;CACA,CAAA,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;AACtD;CACA,CAAA,IAAI,6BAA6B,GAAG,UAAU,UAAU,EAAE,OAAO,EAAE;CACnE,GAAE,IAAI,CAAC,GAAG,EAAE,CAAC;CACb,GAAE,CAAC,CAAC,UAAU,CAAC,GAAG,6BAA6B,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC3E,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;CACtE,EAAC,CAAC;AACF;CACA,CAAA,IAAI,kCAAkC,GAAG,UAAU,UAAU,EAAE,OAAO,EAAE;CACxE,GAAE,IAAI,WAAW,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;CAC9C,KAAI,IAAI,CAAC,GAAG,EAAE,CAAC;CACf,KAAI,CAAC,CAAC,UAAU,CAAC,GAAG,6BAA6B,CAAC,YAAY,GAAG,GAAG,GAAG,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;MAChG,CAAC,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;KACzF;CACH,EAAC,CAAC;AACF;CACA;CACA,CAAA,6BAA6B,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE;CACvD,GAAE,OAAO,SAAS,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAC1E,EAAC,CAAC,CAAC;CACH,CAAA,6BAA6B,CAAC,WAAW,EAAE,UAAU,IAAI,EAAE;CAC3D,GAAE,OAAO,SAAS,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAC9E,EAAC,CAAC,CAAC;CACH,CAAA,6BAA6B,CAAC,YAAY,EAAE,UAAU,IAAI,EAAE;CAC5D,GAAE,OAAO,SAAS,UAAU,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAC/E,EAAC,CAAC,CAAC;CACH,CAAA,6BAA6B,CAAC,gBAAgB,EAAE,UAAU,IAAI,EAAE;CAChE,GAAE,OAAO,SAAS,cAAc,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CACnF,EAAC,CAAC,CAAC;CACH,CAAA,6BAA6B,CAAC,aAAa,EAAE,UAAU,IAAI,EAAE;CAC7D,GAAE,OAAO,SAAS,WAAW,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAChF,EAAC,CAAC,CAAC;CACH,CAAA,6BAA6B,CAAC,WAAW,EAAE,UAAU,IAAI,EAAE;CAC3D,GAAE,OAAO,SAAS,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAC9E,EAAC,CAAC,CAAC;CACH,CAAA,6BAA6B,CAAC,UAAU,EAAE,UAAU,IAAI,EAAE;CAC1D,GAAE,OAAO,SAAS,QAAQ,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAC7E,EAAC,CAAC,CAAC;CACH,CAAA,kCAAkC,CAAC,cAAc,EAAE,UAAU,IAAI,EAAE;CACnE,GAAE,OAAO,SAAS,YAAY,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CACjF,EAAC,CAAC,CAAC;CACH,CAAA,kCAAkC,CAAC,WAAW,EAAE,UAAU,IAAI,EAAE;CAChE,GAAE,OAAO,SAAS,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CAC9E,EAAC,CAAC,CAAC;CACH,CAAA,kCAAkC,CAAC,cAAc,EAAE,UAAU,IAAI,EAAE;CACnE,GAAE,OAAO,SAAS,YAAY,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;CACjF,EAAC,CAAC,CAAA;;;;;;CCtDK,MAAM8I,MAAM,GAAG;CACrB;CACAC,EAAAA,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;CACjEC,EAAAA,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAA;CACjE,CAAC,CAAA;CAEM,SAASC,QAAQA,CAAEjE,IAAI,EAAE;CAC/B,EAAA,IAAInE,KAAK,CAACC,OAAO,CAACkE,IAAI,CAAC,EAAE;CACxB,IAAA,OAAOA,IAAI,CAAA;CACZ,GAAA;GAEA,OAAO8D,MAAM,CAAC9D,IAAI,CAAC,CAAA;CACpB,CAAA;;CAEA;CACe,SAASkE,OAAKA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAgB;CAAA,EAAA,IAAdC,OAAO,GAAAtC,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CACvDmC,EAAAA,EAAE,GAAGF,QAAQ,CAACE,EAAE,CAAC,CAAA;CACjBC,EAAAA,EAAE,GAAGH,QAAQ,CAACG,EAAE,CAAC,CAAA;CAEjB,EAAA,IAAI,CAACD,EAAE,IAAI,CAACC,EAAE,EAAE;CACf,IAAA,MAAM,IAAIG,SAAS,CAAE,CAAA,+BAAA,EAAiC,CAACJ,EAAE,GAAG,MAAM,GAAG,EAAG,CAAE,EAAA,CAACA,EAAE,IAAI,CAACC,EAAE,GAAG,GAAG,GAAG,EAAG,CAAE,EAAA,CAACA,EAAE,GAAG,IAAI,GAAG,EAAG,CAAA,CAAC,CAAC,CAAA;CACrH,GAAA;GAEA,IAAID,EAAE,KAAKC,EAAE,EAAE;CACd;CACA,IAAA,OAAOC,GAAG,CAAA;CACX,GAAA;CAEA,EAAA,IAAI3B,GAAG,GAAG;KAACyB,EAAE;KAAEC,EAAE;KAAEC,GAAG;CAAEC,IAAAA,OAAAA;IAAQ,CAAA;CAEhC1B,EAAAA,KAAK,CAACH,GAAG,CAAC,4BAA4B,EAAEC,GAAG,CAAC,CAAA;CAE5C,EAAA,IAAI,CAACA,GAAG,CAAC8B,CAAC,EAAE;CACX,IAAA,IAAI9B,GAAG,CAACyB,EAAE,KAAKL,MAAM,CAACE,GAAG,IAAItB,GAAG,CAAC0B,EAAE,KAAKN,MAAM,CAACC,GAAG,EAAE;CACnDrB,MAAAA,GAAG,CAAC8B,CAAC,GAAG,CACP,CAAE,kBAAkB,EAAE,oBAAoB,EAAE,CAAC,mBAAmB,CAAE,EAClE,CAAE,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,oBAAoB,CAAE,EAClE,CAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,CAAE,CACnE,CAAA;CACF,KAAC,MACI,IAAI9B,GAAG,CAACyB,EAAE,KAAKL,MAAM,CAACC,GAAG,IAAIrB,GAAG,CAAC0B,EAAE,KAAKN,MAAM,CAACE,GAAG,EAAE;CAExDtB,MAAAA,GAAG,CAAC8B,CAAC,GAAG,CACP,CAAE,iBAAiB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAE,EAChE,CAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,CAAE,EACjE,CAAE,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,CAAE,CAClE,CAAA;CACF,KAAA;CACD,GAAA;CAEA5B,EAAAA,KAAK,CAACH,GAAG,CAAC,0BAA0B,EAAEC,GAAG,CAAC,CAAA;GAE1C,IAAIA,GAAG,CAAC8B,CAAC,EAAE;KACV,OAAOhJ,gBAAgB,CAACkH,GAAG,CAAC8B,CAAC,EAAE9B,GAAG,CAAC2B,GAAG,CAAC,CAAA;CACxC,GAAC,MACI;CACJ,IAAA,MAAM,IAAIE,SAAS,CAAC,oEAAoE,CAAC,CAAA;CAC1F,GAAA;CACD;;CCxDA,MAAME,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAA;;CAElE;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAASC,YAAYA,CAAEC,KAAK,EAAEC,MAAM,EAAE7E,IAAI,EAAE8E,MAAM,EAAE;CACnD,EAAA,IAAIC,KAAK,GAAGjI,MAAM,CAACkI,OAAO,CAACJ,KAAK,CAACE,MAAM,CAAC,CAAC/I,GAAG,CAAC,CAAAuB,IAAA,EAAkBlB,CAAC,KAAK;CAAA,IAAA,IAAvB,CAAC6I,EAAE,EAAEC,SAAS,CAAC,GAAA5H,IAAA,CAAA;CAC5D,IAAA,IAAIyD,YAAY,GAAG8D,MAAM,CAAC9D,YAAY,CAAC3E,CAAC,CAAC,CAAA;CACzC,IAAA,IAAImD,GAAG,GAAGuF,MAAM,CAAC1I,CAAC,CAAC,CAAA;KACnB,IAAI+I,YAAY,GAAG5F,GAAG,KAAA,IAAA,IAAHA,GAAG,KAAHA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,GAAG,CAAE3C,IAAI,CAAA;;CAE5B;CACA;CACA,IAAA,IAAIA,IAAI,CAAA;KACR,IAAI2C,GAAG,CAAC1B,IAAI,EAAE;CACbjB,MAAAA,IAAI,GAAGmE,YAAY,CAACqE,IAAI,CAAC3I,CAAC,IAAIgI,SAAS,CAACY,GAAG,CAAC5I,CAAC,CAAC,CAAC,CAAA;CAChD,KAAC,MACI;OACJG,IAAI,GAAGmE,YAAY,CAACqE,IAAI,CAAC3I,CAAC,IAAIA,CAAC,IAAI0I,YAAY,CAAC,CAAA;CACjD,KAAA;;CAEA;KACA,IAAI,CAACvI,IAAI,EAAE;CACV;CACA,MAAA,IAAI0I,SAAS,GAAGJ,SAAS,CAAClF,IAAI,IAAIiF,EAAE,CAAA;CACpC,MAAA,MAAM,IAAIV,SAAS,CAAE,GAAEY,YAAY,KAAA,IAAA,IAAZA,YAAY,KAAZA,KAAAA,CAAAA,GAAAA,YAAY,GAAI5F,GAAG,CAACO,GAAI,CAAA,iBAAA,EAAmBwF,SAAU,CAAMtF,IAAAA,EAAAA,IAAK,IAAG,CAAC,CAAA;CAC5F,KAAA;CAEA,IAAA,IAAIuF,SAAS,GAAG3I,IAAI,CAACqE,KAAK,CAAA;KAE1B,IAAIkE,YAAY,KAAK,cAAc,EAAE;CACpCI,MAAAA,SAAS,KAATA,SAAS,GAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAA;CACrB,KAAA;KAEA,IAAIC,OAAO,GAAGN,SAAS,CAACjE,KAAK,IAAIiE,SAAS,CAACO,QAAQ,CAAA;KAEnD,IAAIF,SAAS,IAAIC,OAAO,EAAE;CACzBV,MAAAA,MAAM,CAAC1I,CAAC,CAAC,GAAGsJ,QAAa,CAACH,SAAS,EAAEC,OAAO,EAAEV,MAAM,CAAC1I,CAAC,CAAC,CAAC,CAAA;CACzD,KAAA;CAEA,IAAA,OAAOQ,IAAI,CAAA;CACZ,GAAC,CAAC,CAAA;CAEF,EAAA,OAAOmI,KAAK,CAAA;CACb,CAAA;;CAGA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,SAASY,KAAKA,CAAEhJ,GAAG,EAAe;CAAA,EAAA,IAAAiJ,OAAA,CAAA;GAAA,IAAb;CAACC,IAAAA,IAAAA;CAAI,GAAC,GAAA7D,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CAC9C,EAAA,IAAIU,GAAG,GAAG;CAAC,IAAA,KAAK,EAAAkD,CAAAA,OAAA,GAAE1E,MAAM,CAACvE,GAAG,CAAC,MAAA,IAAA,IAAAiJ,OAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAXA,OAAA,CAAa/G,IAAI,EAAC;IAAE,CAAA;CACtC+D,EAAAA,KAAK,CAACH,GAAG,CAAC,aAAa,EAAEC,GAAG,CAAC,CAAA;GAE7B,IAAIA,GAAG,CAACoD,KAAK,EAAE;KACd,OAAOpD,GAAG,CAACoD,KAAK,CAAA;CACjB,GAAA;GAEApD,GAAG,CAACqD,MAAM,GAAGL,aAAkB,CAAChD,GAAG,CAAC/F,GAAG,CAAC,CAAA;GAExC,IAAI+F,GAAG,CAACqD,MAAM,EAAE;CACf;CACA,IAAA,IAAI/F,IAAI,GAAG0C,GAAG,CAACqD,MAAM,CAAC/F,IAAI,CAAA;KAE1B,IAAIA,IAAI,KAAK,OAAO,EAAE;CACrB;OACA,IAAIiF,EAAE,GAAGvC,GAAG,CAACqD,MAAM,CAAC5G,IAAI,CAAC6G,KAAK,EAAE,CAAA;CAChC;CACA,MAAA,IAAIC,WAAW,GAAGhB,EAAE,CAACrF,UAAU,CAAC,IAAI,CAAC,GAAGqF,EAAE,CAACiB,SAAS,CAAC,CAAC,CAAC,GAAI,CAAA,EAAA,EAAIjB,EAAG,CAAC,CAAA,CAAA;CACnE,MAAA,IAAIkB,GAAG,GAAG,CAAClB,EAAE,EAAEgB,WAAW,CAAC,CAAA;OAC3B,IAAIpG,KAAK,GAAG6C,GAAG,CAACqD,MAAM,CAAC7F,OAAO,CAACkG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG1D,GAAG,CAACqD,MAAM,CAAC5G,IAAI,CAACkH,GAAG,EAAE,GAAG,CAAC,CAAA;CAE3E,MAAA,KAAK,IAAIzB,KAAK,IAAI0B,UAAU,CAACC,GAAG,EAAE;CACjC,QAAA,IAAIC,SAAS,GAAG5B,KAAK,CAAC6B,SAAS,CAAC,OAAO,CAAC,CAAA;CAExC,QAAA,IAAID,SAAS,EAAE;CAAA,UAAA,IAAAE,cAAA,CAAA;CACd,UAAA,IAAIP,GAAG,CAACQ,QAAQ,CAACH,SAAS,CAACvB,EAAE,CAAC,IAAA,CAAAyB,cAAA,GAAIF,SAAS,CAACL,GAAG,MAAAO,IAAAA,IAAAA,cAAA,KAAbA,KAAAA,CAAAA,IAAAA,cAAA,CAAeE,MAAM,CAAEC,MAAM,IAAKV,GAAG,CAACQ,QAAQ,CAACE,MAAM,CAAC,CAAC,CAACjL,MAAM,EAAE;CACjG;CACA;CACA;CACA,YAAA,MAAMkJ,MAAM,GAAGhI,MAAM,CAACgK,IAAI,CAAClC,KAAK,CAACE,MAAM,CAAC,CAAC/I,GAAG,CAAC,CAACI,CAAC,EAAEC,CAAC,KAAKsG,GAAG,CAACqD,MAAM,CAAC5G,IAAI,CAAC/C,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;CAE/E,YAAA,IAAI2I,KAAK,CAAA;aAET,IAAIyB,SAAS,CAACzF,YAAY,EAAE;eAC3BgE,KAAK,GAAGJ,YAAY,CAACC,KAAK,EAAE4B,SAAS,EAAE,OAAO,EAAE1B,MAAM,CAAC,CAAA;CACxD,aAAA;CAEA,YAAA,IAAIe,IAAI,EAAE;CACT/I,cAAAA,MAAM,CAACiK,MAAM,CAAClB,IAAI,EAAE;CAACmB,gBAAAA,QAAQ,EAAE,OAAO;CAAEjC,gBAAAA,KAAAA;CAAK,eAAC,CAAC,CAAA;CAChD,aAAA;CAEA,YAAA,IAAIyB,SAAS,CAACvB,EAAE,CAACrF,UAAU,CAAC,IAAI,CAAC,IAAI,CAACqF,EAAE,CAACrF,UAAU,CAAC,IAAI,CAAC,EAAE;CAC1DqH,cAAAA,QAAQ,CAAC7D,IAAI,CAAE,CAAEwB,EAAAA,KAAK,CAAC5E,IAAK,CAAA,sEAAA,CAAuE,GACpF,CAAA,mBAAA,EAAqBwG,SAAS,CAACvB,EAAG,CAAqBA,mBAAAA,EAAAA,EAAG,IAAG,CAAC,CAAA;CAC9E,aAAA;CACA,YAAA,IAAIA,EAAE,CAACrF,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC4G,SAAS,CAACvB,EAAE,CAACrF,UAAU,CAAC,IAAI,CAAC,EAAE;CAC1DqH,cAAAA,QAAQ,CAAC7D,IAAI,CAAE,CAAEwB,EAAAA,KAAK,CAAC5E,IAAK,CAAA,oDAAA,CAAqD,GAClE,CAAA,UAAA,EAAYwG,SAAS,CAACvB,EAAG,CAA8BA,4BAAAA,EAAAA,EAAG,IAAG,CAAC,CAAA;CAC9E,aAAA;aAEA,OAAO;eAACiC,OAAO,EAAEtC,KAAK,CAACK,EAAE;eAAEH,MAAM;CAAEjF,cAAAA,KAAAA;cAAM,CAAA;CAC1C,WAAA;CACD,SAAA;CACD,OAAA;;CAEA;OACA,IAAIsH,UAAU,GAAG,EAAE,CAAA;OACnB,IAAIC,UAAU,GAAGnC,EAAE,IAAIqB,UAAU,CAACe,QAAQ,GAAGpC,EAAE,GAAGgB,WAAW,CAAA;CAC7D,MAAA,IAAImB,UAAU,IAAId,UAAU,CAACe,QAAQ,EAAE;CAAA,QAAA,IAAAC,qBAAA,CAAA;CACtC;SACA,IAAIC,KAAK,GAAAD,CAAAA,qBAAA,GAAGhB,UAAU,CAACe,QAAQ,CAACD,UAAU,CAAC,CAACI,OAAO,cAAAF,qBAAA,KAAA,KAAA,CAAA,IAAA,CAAAA,qBAAA,GAAvCA,qBAAA,CAAyCxB,KAAK,MAAA,IAAA,IAAAwB,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA9CA,qBAAA,CAAgDrC,EAAE,CAAA;CAE9D,QAAA,IAAIsC,KAAK,EAAE;WACVJ,UAAU,GAAI,CAAqBI,mBAAAA,EAAAA,KAAM,CAAG,EAAA,CAAA,CAAA;CAC7C,SAAA;CACD,OAAA;OAEA,MAAM,IAAIhD,SAAS,CAAE,CAAqBU,mBAAAA,EAAAA,EAAG,CAAI,GAAA,CAAA,IAAIkC,UAAU,IAAI,mBAAmB,CAAC,CAAC,CAAA;CACzF,KAAC,MACI;CACJ,MAAA,KAAK,IAAIvC,KAAK,IAAI0B,UAAU,CAACC,GAAG,EAAE;CACjC;CACA,QAAA,IAAI1B,MAAM,GAAGD,KAAK,CAAC6B,SAAS,CAACzG,IAAI,CAAC,CAAA;CAClC,QAAA,IAAI6E,MAAM,IAAIA,MAAM,CAACjI,IAAI,KAAK,UAAU,EAAE;WACzC,IAAIiD,KAAK,GAAG,CAAC,CAAA;CAEb,UAAA,IAAIgF,MAAM,CAAC4C,SAAS,IAAI/B,IAAS,CAAChD,GAAG,CAACqD,MAAM,CAAC5G,IAAI,CAAC,CAACU,KAAK,EAAE;aACzDA,KAAK,GAAG6C,GAAG,CAACqD,MAAM,CAAC5G,IAAI,CAACkH,GAAG,EAAE,CAAA;CAC9B,WAAA;CAEA,UAAA,IAAIvB,MAAM,GAAGpC,GAAG,CAACqD,MAAM,CAAC5G,IAAI,CAAA;CAE5B,UAAA,IAAI4F,KAAK,CAAA;WAET,IAAIF,MAAM,CAAC9D,YAAY,EAAE;aACxBgE,KAAK,GAAGJ,YAAY,CAACC,KAAK,EAAEC,MAAM,EAAE7E,IAAI,EAAE8E,MAAM,CAAC,CAAA;CAClD,WAAA;CAEA,UAAA,IAAIe,IAAI,EAAE;CACT/I,YAAAA,MAAM,CAACiK,MAAM,CAAClB,IAAI,EAAE;eAACmB,QAAQ,EAAEnC,MAAM,CAAC7E,IAAI;CAAE+E,cAAAA,KAAAA;CAAK,aAAC,CAAC,CAAA;CACpD,WAAA;WAEA,OAAO;aACNmC,OAAO,EAAEtC,KAAK,CAACK,EAAE;aACjBH,MAAM;CAAEjF,YAAAA,KAAAA;YACR,CAAA;CACF,SAAA;CACD,OAAA;CACD,KAAA;CACD,GAAC,MACI;CACJ;CACA,IAAA,KAAK,IAAI+E,KAAK,IAAI0B,UAAU,CAACC,GAAG,EAAE;CACjC,MAAA,KAAK,IAAIS,QAAQ,IAAIpC,KAAK,CAAC4C,OAAO,EAAE;CACnC,QAAA,IAAI3C,MAAM,GAAGD,KAAK,CAAC4C,OAAO,CAACR,QAAQ,CAAC,CAAA;CAEpC,QAAA,IAAInC,MAAM,CAACjI,IAAI,KAAK,QAAQ,EAAE;CAC7B,UAAA,SAAA;CACD,SAAA;CAEA,QAAA,IAAIiI,MAAM,CAACnF,IAAI,IAAI,CAACmF,MAAM,CAACnF,IAAI,CAACgD,GAAG,CAAC/F,GAAG,CAAC,EAAE;CACzC,UAAA,SAAA;CACD,SAAA;SAEA,IAAImJ,KAAK,GAAGjB,MAAM,CAACc,KAAK,CAACjD,GAAG,CAAC/F,GAAG,CAAC,CAAA;CAEjC,QAAA,IAAImJ,KAAK,EAAE;CAAA,UAAA,IAAA4B,YAAA,CAAA;CACV,UAAA,CAAAA,YAAA,GAAA5B,KAAK,CAACjG,KAAK,MAAA6H,IAAAA,IAAAA,YAAA,KAAAA,KAAAA,CAAAA,GAAAA,YAAA,GAAX5B,KAAK,CAACjG,KAAK,GAAK,CAAC,CAAA;CAEjB,UAAA,IAAIgG,IAAI,EAAE;aACTA,IAAI,CAACmB,QAAQ,GAAGA,QAAQ,CAAA;CACzB,WAAA;CAEA,UAAA,OAAOlB,KAAK,CAAA;CACb,SAAA;CACD,OAAA;CACD,KAAA;CACD,GAAA;;CAGA;CACA,EAAA,MAAM,IAAIvB,SAAS,CAAE,CAAkB5H,gBAAAA,EAAAA,GAAI,gCAA+B,CAAC,CAAA;CAC5E;;CCjMA;CACA;CACA;CACA;CACA;CACe,SAASgL,QAAQA,CAAE7B,KAAK,EAAE;CACxC,EAAA,IAAIjK,KAAK,CAACC,OAAO,CAACgK,KAAK,CAAC,EAAE;CACzB,IAAA,OAAOA,KAAK,CAAC/J,GAAG,CAAC4L,QAAQ,CAAC,CAAA;CAC3B,GAAA;GAEA,IAAI,CAAC7B,KAAK,EAAE;CACX,IAAA,MAAM,IAAIvB,SAAS,CAAC,uBAAuB,CAAC,CAAA;CAC7C,GAAA;CAEA,EAAA,IAAI7H,QAAQ,CAACoJ,KAAK,CAAC,EAAE;CACpBA,IAAAA,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC,CAAA;CACrB,GAAA;;CAEA;GACA,IAAIlB,KAAK,GAAGkB,KAAK,CAAClB,KAAK,IAAIkB,KAAK,CAACoB,OAAO,CAAA;CAExC,EAAA,IAAI,EAAEtC,KAAK,YAAY0B,UAAU,CAAC,EAAE;CACnC;KACAR,KAAK,CAAClB,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CACpC,GAAA;CAEA,EAAA,IAAIkB,KAAK,CAACjG,KAAK,KAAKoC,SAAS,EAAE;KAC9B6D,KAAK,CAACjG,KAAK,GAAG,CAAC,CAAA;CAChB,GAAA;CAEA,EAAA,OAAOiG,KAAK,CAAA;CACb;;CC9BA,MAAM+B,GAAC,GAAG,OAAO,CAAA;;CAEjB;CACA;CACA;CACe,MAAMvB,UAAU,CAAC;GAC/BwB,WAAWA,CAAExD,OAAO,EAAE;KAAA,IAAAyD,eAAA,EAAAzK,IAAA,EAAA0K,cAAA,EAAAC,gBAAA,EAAAC,mBAAA,CAAA;CACrB,IAAA,IAAI,CAACjD,EAAE,GAAGX,OAAO,CAACW,EAAE,CAAA;CACpB,IAAA,IAAI,CAACjF,IAAI,GAAGsE,OAAO,CAACtE,IAAI,CAAA;CACxB,IAAA,IAAI,CAAC0B,IAAI,GAAG4C,OAAO,CAAC5C,IAAI,GAAG4E,UAAU,CAACsB,GAAG,CAACtD,OAAO,CAAC5C,IAAI,CAAC,GAAG,IAAI,CAAA;CAC9D,IAAA,IAAI,CAACyG,OAAO,GAAG7D,OAAO,CAAC6D,OAAO,CAAA;KAE9B,IAAI,IAAI,CAACzG,IAAI,EAAE;CACd,MAAA,IAAI,CAAC0G,QAAQ,GAAG9D,OAAO,CAAC8D,QAAQ,CAAA;CAChC,MAAA,IAAI,CAACC,MAAM,GAAG/D,OAAO,CAAC+D,MAAM,CAAA;CAC7B,KAAA;;CAEA;;CAEA,IAAA,IAAIvD,MAAM,GAAAiD,CAAAA,eAAA,GAAGzD,OAAO,CAACQ,MAAM,MAAA,IAAA,IAAAiD,eAAA,KAAA,KAAA,CAAA,GAAAA,eAAA,GAAI,IAAI,CAACrG,IAAI,CAACoD,MAAM,CAAA;CAE/C,IAAA,KAAK,IAAI9E,IAAI,IAAI8E,MAAM,EAAE;OACxB,IAAI,EAAE,MAAM,IAAIA,MAAM,CAAC9E,IAAI,CAAC,CAAC,EAAE;CAC9B8E,QAAAA,MAAM,CAAC9E,IAAI,CAAC,CAACA,IAAI,GAAGA,IAAI,CAAA;CACzB,OAAA;CACD,KAAA;KACA,IAAI,CAAC8E,MAAM,GAAGA,MAAM,CAAA;;CAEpB;;KAEA,IAAIwD,KAAK,GAAAhL,CAAAA,IAAA,GAAA0K,CAAAA,cAAA,GAAG1D,OAAO,CAACgE,KAAK,MAAA,IAAA,IAAAN,cAAA,KAAA,KAAA,CAAA,GAAAA,cAAA,GAAI,IAAI,CAACtG,IAAI,CAAC4G,KAAK,cAAAhL,IAAA,KAAA,KAAA,CAAA,GAAAA,IAAA,GAAI,KAAK,CAAA;CACrD,IAAA,IAAI,CAACgL,KAAK,GAAGrE,QAAQ,CAACqE,KAAK,CAAC,CAAA;;CAE5B;;CAEA,IAAA,IAAI,CAACd,OAAO,GAAAS,CAAAA,gBAAA,GAAG3D,OAAO,CAACkD,OAAO,MAAA,IAAA,IAAAS,gBAAA,KAAA,KAAA,CAAA,GAAAA,gBAAA,GAAI,EAAE,CAAA;CAEpC,IAAA,KAAK,IAAIjI,IAAI,IAAI,IAAI,CAACwH,OAAO,EAAE;CAC9B,MAAA,IAAI3C,MAAM,GAAG,IAAI,CAAC2C,OAAO,CAACxH,IAAI,CAAC,CAAA;CAC/B6E,MAAAA,MAAM,CAACjI,IAAI,KAAXiI,MAAM,CAACjI,IAAI,GAAK,UAAU,CAAA,CAAA;CAC1BiI,MAAAA,MAAM,CAAC7E,IAAI,KAAX6E,MAAM,CAAC7E,IAAI,GAAKA,IAAI,CAAA,CAAA;CACrB,KAAA;CAEA,IAAA,IAAI,EAAAkI,CAAAA,mBAAA,GAAC,IAAI,CAACV,OAAO,CAAC1B,KAAK,MAAA,IAAA,IAAAoC,mBAAA,KAAA,KAAA,CAAA,IAAlBA,mBAAA,CAAoBjD,EAAE,CAAE,EAAA;CAAA,MAAA,IAAAsD,oBAAA,CAAA;CAC5B,MAAA,IAAI,CAACf,OAAO,CAAC1B,KAAK,GAAG;CACpB,QAAA,IAAA,CAAAyC,oBAAA,GAAG,IAAI,CAACf,OAAO,CAAC1B,KAAK,MAAA,IAAA,IAAAyC,oBAAA,KAAA,KAAA,CAAA,GAAAA,oBAAA,GAAI,EAAE;CAC3BtD,QAAAA,EAAE,EAAEX,OAAO,CAACiD,KAAK,IAAI,IAAI,CAACtC,EAAAA;QAC1B,CAAA;CACF,KAAA;;CAEA;;KAEA,IAAIX,OAAO,CAACkE,UAAU,EAAE;CACvB;CACA,MAAA,IAAI,CAACA,UAAU,GAAGlE,OAAO,CAACkE,UAAU,KAAK,MAAM,GAAG,IAAI,GAAGlC,UAAU,CAACsB,GAAG,CAACtD,OAAO,CAACkE,UAAU,CAAC,CAAA;CAC5F,KAAC,MACI;CACJ;OACA,IAAI,IAAI,CAACC,OAAO,EAAE;CACjB;CACA,QAAA,IAAI,CAACD,UAAU,GAAG,IAAI,CAAC9G,IAAI,CAAA;CAC5B,OAAC,MACI;SACJ,IAAI,CAAC8G,UAAU,GAAI,IAAI,CAAA;CACxB,OAAA;CACD,KAAA;;CAEA;CACA,IAAA,IAAI,IAAI,CAACA,UAAU,CAACE,WAAW,EAAE;CAChC,MAAA,IAAI,CAACC,OAAO,GAAG,CAAC7D,MAAM,EAAER,OAAO,KAAK;CACnC,QAAA,OAAO,IAAI,CAAA;QACX,CAAA;CACF,KAAA;;CAEA;CACA,IAAA,IAAI,CAACsE,QAAQ,GAAGtE,OAAO,CAACsE,QAAQ,CAAA;;CAEhC;CACA9L,IAAAA,MAAM,CAAC+L,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;OACnCpI,KAAK,EAAEqI,OAAO,CAAC,IAAI,CAAC,CAACC,OAAO,EAAE;CAC9BC,MAAAA,QAAQ,EAAE,KAAK;CACfC,MAAAA,UAAU,EAAE,IAAI;CAChBC,MAAAA,YAAY,EAAE,IAAA;CACf,KAAC,CAAC,CAAA;CAEFtG,IAAAA,KAAK,CAACH,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;CACvC,GAAA;GAEAkG,OAAOA,CAAE7D,MAAM,EAAsB;KAAA,IAApB;CAACqE,MAAAA,OAAO,GAAGtB,GAAAA;CAAC,KAAC,GAAA7F,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;KAClC,IAAI,CAAC,IAAI,CAACoH,MAAM,CAAC,IAAI,CAACZ,UAAU,CAAC,EAAE;OAClC1D,MAAM,GAAG,IAAI,CAAClE,EAAE,CAAC,IAAI,CAAC4H,UAAU,EAAE1D,MAAM,CAAC,CAAA;CACzC,MAAA,OAAO,IAAI,CAAC0D,UAAU,CAACG,OAAO,CAAC7D,MAAM,EAAE;CAACqE,QAAAA,OAAAA;CAAO,OAAC,CAAC,CAAA;CAClD,KAAA;KAEA,IAAIjE,SAAS,GAAGpI,MAAM,CAACuM,MAAM,CAAC,IAAI,CAACvE,MAAM,CAAC,CAAA;KAE1C,OAAOA,MAAM,CAACwE,KAAK,CAAC,CAAC7M,CAAC,EAAEL,CAAC,KAAK;CAC7B,MAAA,IAAIyJ,IAAI,GAAGX,SAAS,CAAC9I,CAAC,CAAC,CAAA;OAEvB,IAAIyJ,IAAI,CAACjJ,IAAI,KAAK,OAAO,IAAIiJ,IAAI,CAAC5E,KAAK,EAAE;CACxC,QAAA,IAAItD,MAAM,CAACC,KAAK,CAACnB,CAAC,CAAC,EAAE;CACpB;CACA,UAAA,OAAO,IAAI,CAAA;CACZ,SAAA;SAEA,IAAI,CAAC2E,GAAG,EAAEE,GAAG,CAAC,GAAGuE,IAAI,CAAC5E,KAAK,CAAA;SAC3B,OAAO,CAACG,GAAG,KAAKa,SAAS,IAAIxF,CAAC,IAAI2E,GAAG,GAAG+H,OAAO,MACvC7H,GAAG,KAAKW,SAAS,IAAIxF,CAAC,IAAI6E,GAAG,GAAG6H,OAAO,CAAC,CAAA;CACjD,OAAA;CAEA,MAAA,OAAO,IAAI,CAAA;CACZ,KAAC,CAAC,CAAA;CACH,GAAA;GAEA,IAAIT,WAAWA,GAAI;CAClB,IAAA,OAAO5L,MAAM,CAACuM,MAAM,CAAC,IAAI,CAACvE,MAAM,CAAC,CAACwE,KAAK,CAACC,KAAK,IAAI,EAAE,OAAO,IAAIA,KAAK,CAAC,CAAC,CAAA;CACtE,GAAA;GAEA,IAAIhC,KAAKA,GAAI;CAAA,IAAA,IAAAiC,aAAA,CAAA;KACZ,OAAO,CAAA,CAAAA,aAAA,GAAI,IAAA,CAAChC,OAAO,MAAAgC,IAAAA,IAAAA,aAAA,KAAAA,KAAAA,CAAAA,IAAAA,CAAAA,aAAA,GAAZA,aAAA,CAAc1D,KAAK,MAAA,IAAA,IAAA0D,aAAA,KAAnBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAA,CAAqBvE,EAAE,KAAI,IAAI,CAACA,EAAE,CAAA;CAC1C,GAAA;GAEA,IAAIwD,OAAOA,GAAI;CACd,IAAA,KAAK,IAAIxD,EAAE,IAAI,IAAI,CAACH,MAAM,EAAE;OAC3B,IAAI,IAAI,CAACA,MAAM,CAACG,EAAE,CAAC,CAACrI,IAAI,KAAK,OAAO,EAAE;CACrC,QAAA,OAAO,IAAI,CAAA;CACZ,OAAA;CACD,KAAA;CAEA,IAAA,OAAO,KAAK,CAAA;CACb,GAAA;GAEA6J,SAASA,CAAE5B,MAAM,EAAE;CAClB,IAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;CAC/BA,MAAAA,MAAM,GAAG4E,aAAa,CAAC5E,MAAM,EAAE,IAAI,CAAC,CAAA;CACpC,MAAA,OAAOA,MAAM,CAAA;CACd,KAAA;CAEA,IAAA,IAAIrI,GAAG,CAAA;KACP,IAAIqI,MAAM,KAAK,SAAS,EAAE;CACzB;OACArI,GAAG,GAAGM,MAAM,CAACuM,MAAM,CAAC,IAAI,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;CACrC,KAAC,MACI;CACJhL,MAAAA,GAAG,GAAG,IAAI,CAACgL,OAAO,CAAC3C,MAAM,CAAC,CAAA;CAC3B,KAAA;CAEA,IAAA,IAAIrI,GAAG,EAAE;CACRA,MAAAA,GAAG,GAAGiN,aAAa,CAACjN,GAAG,EAAE,IAAI,CAAC,CAAA;CAC9B,MAAA,OAAOA,GAAG,CAAA;CACX,KAAA;CAEA,IAAA,OAAO,IAAI,CAAA;CACZ,GAAA;;CAEA;CACD;CACA;CACA;CACA;CACA;GACC4M,MAAMA,CAAExE,KAAK,EAAE;KACd,IAAI,CAACA,KAAK,EAAE;CACX,MAAA,OAAO,KAAK,CAAA;CACb,KAAA;CAEA,IAAA,OAAO,IAAI,KAAKA,KAAK,IAAI,IAAI,CAACK,EAAE,KAAKL,KAAK,IAAI,IAAI,CAACK,EAAE,KAAKL,KAAK,CAACK,EAAE,CAAA;CACnE,GAAA;CAEArE,EAAAA,EAAEA,CAAEgE,KAAK,EAAEE,MAAM,EAAE;CAClB,IAAA,IAAI9C,SAAS,CAACpG,MAAM,KAAK,CAAC,EAAE;CAC3B,MAAA,MAAMkK,KAAK,GAAG6B,QAAQ,CAAC/C,KAAK,CAAC,CAAA;CAC7B,MAAA,CAACA,KAAK,EAAEE,MAAM,CAAC,GAAG,CAACgB,KAAK,CAAClB,KAAK,EAAEkB,KAAK,CAAChB,MAAM,CAAC,CAAA;CAC9C,KAAA;CAEAF,IAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAE7B,IAAA,IAAI,IAAI,CAACwE,MAAM,CAACxE,KAAK,CAAC,EAAE;CACvB;CACA,MAAA,OAAOE,MAAM,CAAA;CACd,KAAA;;CAEA;CACAA,IAAAA,MAAM,GAAGA,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAIkB,MAAM,CAACC,KAAK,CAACnB,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,CAAA;;CAEjD;CACA,IAAA,IAAIiN,MAAM,GAAG,IAAI,CAACC,IAAI,CAAA;CACtB,IAAA,IAAIC,SAAS,GAAGhF,KAAK,CAAC+E,IAAI,CAAA;KAE1B,IAAIE,eAAe,EAAEC,oBAAoB,CAAA;CAEzC,IAAA,KAAK,IAAI1N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsN,MAAM,CAAC9N,MAAM,EAAEQ,CAAC,EAAE,EAAE;CACvC,MAAA,IAAIsN,MAAM,CAACtN,CAAC,CAAC,CAACgN,MAAM,CAACQ,SAAS,CAACxN,CAAC,CAAC,CAAC,EAAE;CACnCyN,QAAAA,eAAe,GAAGH,MAAM,CAACtN,CAAC,CAAC,CAAA;CAC3B0N,QAAAA,oBAAoB,GAAG1N,CAAC,CAAA;CACzB,OAAC,MACI;CACJ,QAAA,MAAA;CACD,OAAA;CACD,KAAA;KAEA,IAAI,CAACyN,eAAe,EAAE;CACrB;OACA,MAAM,IAAIE,KAAK,CAAE,CAAA,oCAAA,EAAsC,IAAK,CAAOnF,KAAAA,EAAAA,KAAM,iCAAgC,CAAC,CAAA;CAC3G,KAAA;;CAEA;CACA,IAAA,KAAK,IAAIxI,CAAC,GAAGsN,MAAM,CAAC9N,MAAM,GAAG,CAAC,EAAEQ,CAAC,GAAG0N,oBAAoB,EAAE1N,CAAC,EAAE,EAAE;OAC9D0I,MAAM,GAAG4E,MAAM,CAACtN,CAAC,CAAC,CAACiM,MAAM,CAACvD,MAAM,CAAC,CAAA;CAClC,KAAA;;CAEA;CACA,IAAA,KAAK,IAAI1I,CAAC,GAAG0N,oBAAoB,GAAG,CAAC,EAAE1N,CAAC,GAAGwN,SAAS,CAAChO,MAAM,EAAEQ,CAAC,EAAE,EAAE;OACjE0I,MAAM,GAAG8E,SAAS,CAACxN,CAAC,CAAC,CAACgM,QAAQ,CAACtD,MAAM,CAAC,CAAA;CACvC,KAAA;CAEA,IAAA,OAAOA,MAAM,CAAA;CACd,GAAA;CAEAnE,EAAAA,IAAIA,CAAEiE,KAAK,EAAEE,MAAM,EAAE;CACpB,IAAA,IAAI9C,SAAS,CAACpG,MAAM,KAAK,CAAC,EAAE;CAC3B,MAAA,MAAMkK,KAAK,GAAG6B,QAAQ,CAAC/C,KAAK,CAAC,CAAA;CAC7B,MAAA,CAACA,KAAK,EAAEE,MAAM,CAAC,GAAG,CAACgB,KAAK,CAAClB,KAAK,EAAEkB,KAAK,CAAChB,MAAM,CAAC,CAAA;CAC9C,KAAA;CAEAF,IAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAE7B,IAAA,OAAOA,KAAK,CAAChE,EAAE,CAAC,IAAI,EAAEkE,MAAM,CAAC,CAAA;CAC9B,GAAA;CAEA9H,EAAAA,QAAQA,GAAI;KACX,OAAQ,CAAA,EAAE,IAAI,CAACgD,IAAK,KAAI,IAAI,CAACiF,EAAG,CAAE,CAAA,CAAA,CAAA;CACnC,GAAA;CAEA+E,EAAAA,YAAYA,GAAI;KACf,IAAIxN,GAAG,GAAG,EAAE,CAAA;CAEZ,IAAA,KAAK,IAAIyI,EAAE,IAAI,IAAI,CAACH,MAAM,EAAE;CAAA,MAAA,IAAAmF,UAAA,CAAA;CAC3B,MAAA,IAAIpE,IAAI,GAAG,IAAI,CAACf,MAAM,CAACG,EAAE,CAAC,CAAA;OAC1B,IAAIhE,KAAK,GAAG4E,IAAI,CAAC5E,KAAK,IAAI4E,IAAI,CAACJ,QAAQ,CAAA;CACvCjJ,MAAAA,GAAG,CAACuD,IAAI,CAAA,CAAAkK,UAAA,GAAChJ,KAAK,aAALA,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEG,GAAG,MAAA6I,IAAAA,IAAAA,UAAA,cAAAA,UAAA,GAAI,CAAC,CAAC,CAAA;CAC1B,KAAA;CAEA,IAAA,OAAOzN,GAAG,CAAA;CACX,GAAA;GAEA,OAAO6K,QAAQ,GAAG,EAAE,CAAA;;CAEpB;GACA,WAAWd,GAAGA,GAAI;CACjB,IAAA,OAAO,CAAC,GAAG,IAAI7B,GAAG,CAAC5H,MAAM,CAACuM,MAAM,CAAC/C,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC,CAAA;CACxD,GAAA;CAEA,EAAA,OAAO6C,QAAQA,CAAEjF,EAAE,EAAEL,KAAK,EAAE;CAC3B,IAAA,IAAI5C,SAAS,CAACpG,MAAM,KAAK,CAAC,EAAE;CAC3BgJ,MAAAA,KAAK,GAAG5C,SAAS,CAAC,CAAC,CAAC,CAAA;OACpBiD,EAAE,GAAGL,KAAK,CAACK,EAAE,CAAA;CACd,KAAA;CAEAL,IAAAA,KAAK,GAAG,IAAI,CAACgD,GAAG,CAAChD,KAAK,CAAC,CAAA;CAEvB,IAAA,IAAI,IAAI,CAACyC,QAAQ,CAACpC,EAAE,CAAC,IAAI,IAAI,CAACoC,QAAQ,CAACpC,EAAE,CAAC,KAAKL,KAAK,EAAE;CACrD,MAAA,MAAM,IAAImF,KAAK,CAAE,CAAuC9E,qCAAAA,EAAAA,EAAG,GAAE,CAAC,CAAA;CAC/D,KAAA;CACA,IAAA,IAAI,CAACoC,QAAQ,CAACpC,EAAE,CAAC,GAAGL,KAAK,CAAA;;CAEzB;KACA,IAAI5C,SAAS,CAACpG,MAAM,KAAK,CAAC,IAAIgJ,KAAK,CAACuD,OAAO,EAAE;CAC5C,MAAA,KAAK,IAAIgC,KAAK,IAAIvF,KAAK,CAACuD,OAAO,EAAE;CAChC,QAAA,IAAI,CAAC+B,QAAQ,CAACC,KAAK,EAAEvF,KAAK,CAAC,CAAA;CAC5B,OAAA;CACD,KAAA;CAEA,IAAA,OAAOA,KAAK,CAAA;CACb,GAAA;;CAEA;CACD;CACA;CACA;GACC,OAAOgD,GAAGA,CAAEhD,KAAK,EAAmB;CACnC,IAAA,IAAI,CAACA,KAAK,IAAIA,KAAK,YAAY0B,UAAU,EAAE;CAC1C,MAAA,OAAO1B,KAAK,CAAA;CACb,KAAA;CAEA,IAAA,IAAIwF,OAAO,GAAGxN,IAAI,CAACgI,KAAK,CAAC,CAAA;KAEzB,IAAIwF,OAAO,KAAK,QAAQ,EAAE;CACzB;OACA,IAAI5N,GAAG,GAAG8J,UAAU,CAACe,QAAQ,CAACzC,KAAK,CAACzH,WAAW,EAAE,CAAC,CAAA;OAElD,IAAI,CAACX,GAAG,EAAE;CACT,QAAA,MAAM,IAAI+H,SAAS,CAAE,CAAkCK,gCAAAA,EAAAA,KAAM,GAAE,CAAC,CAAA;CACjE,OAAA;CAEA,MAAA,OAAOpI,GAAG,CAAA;CACX,KAAA;KAAC,KAAA6N,IAAAA,IAAA,GAAArI,SAAA,CAAApG,MAAA,EAhBoB0O,YAAY,OAAAzO,KAAA,CAAAwO,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;CAAZD,MAAAA,YAAY,CAAAC,IAAA,GAAAvI,CAAAA,CAAAA,GAAAA,SAAA,CAAAuI,IAAA,CAAA,CAAA;CAAA,KAAA;KAkBjC,IAAID,YAAY,CAAC1O,MAAM,EAAE;CACxB,MAAA,OAAO0K,UAAU,CAACsB,GAAG,CAAC,GAAG0C,YAAY,CAAC,CAAA;CACvC,KAAA;CAEA,IAAA,MAAM,IAAI/F,SAAS,CAAE,CAAEK,EAAAA,KAAM,6BAA4B,CAAC,CAAA;CAC3D,GAAA;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACC,EAAA,OAAO4F,YAAYA,CAAEC,GAAG,EAAEC,YAAY,EAAE;CACvC,IAAA,IAAIC,SAAS,GAAG/N,IAAI,CAAC6N,GAAG,CAAC,CAAA;KACzB,IAAI7F,KAAK,EAAE2E,KAAK,CAAA;KAEhB,IAAIoB,SAAS,KAAK,QAAQ,EAAE;CAC3B,MAAA,IAAIF,GAAG,CAAC9D,QAAQ,CAAC,GAAG,CAAC,EAAE;CACtB;SACA,CAAC/B,KAAK,EAAE2E,KAAK,CAAC,GAAGkB,GAAG,CAACzJ,KAAK,CAAC,GAAG,CAAC,CAAA;CAChC,OAAC,MACI;CACJ;CACA,QAAA,CAAC4D,KAAK,EAAE2E,KAAK,CAAC,GAAG,GAAGkB,GAAG,CAAC,CAAA;CACzB,OAAA;MACA,MACI,IAAI5O,KAAK,CAACC,OAAO,CAAC2O,GAAG,CAAC,EAAE;CAC5B,MAAA,CAAC7F,KAAK,EAAE2E,KAAK,CAAC,GAAGkB,GAAG,CAAA;CACrB,KAAC,MACI;CACJ;OACA7F,KAAK,GAAG6F,GAAG,CAAC7F,KAAK,CAAA;OACjB2E,KAAK,GAAGkB,GAAG,CAACG,OAAO,CAAA;CACpB,KAAA;CAEAhG,IAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;KAE7B,IAAI,CAACA,KAAK,EAAE;CACXA,MAAAA,KAAK,GAAG8F,YAAY,CAAA;CACrB,KAAA;KAEA,IAAI,CAAC9F,KAAK,EAAE;CACX,MAAA,MAAM,IAAIL,SAAS,CAAE,CAAsCkG,oCAAAA,EAAAA,GAAI,yEAAwE,CAAC,CAAA;CACzI,KAAA;CAEAE,IAAAA,SAAS,GAAG/N,IAAI,CAAC2M,KAAK,CAAC,CAAA;KAEvB,IAAIoB,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,QAAQ,IAAIpB,KAAK,IAAI,CAAC,EAAE;CACnE;CACA,MAAA,IAAI1D,IAAI,GAAG/I,MAAM,CAACkI,OAAO,CAACJ,KAAK,CAACE,MAAM,CAAC,CAACyE,KAAK,CAAC,CAAA;CAE9C,MAAA,IAAI1D,IAAI,EAAE;SACT,OAAO;WAACjB,KAAK;CAAEK,UAAAA,EAAE,EAAEY,IAAI,CAAC,CAAC,CAAC;CAAEgF,UAAAA,KAAK,EAAEtB,KAAK;WAAE,GAAG1D,IAAI,CAAC,CAAC,CAAA;UAAE,CAAA;CACtD,OAAA;CACD,KAAA;CAEAjB,IAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAE7B,IAAA,IAAIkG,eAAe,GAAGvB,KAAK,CAACpM,WAAW,EAAE,CAAA;KAEzC,IAAIf,CAAC,GAAG,CAAC,CAAA;CACT,IAAA,KAAK,IAAI6I,EAAE,IAAIL,KAAK,CAACE,MAAM,EAAE;CAAA,MAAA,IAAAiG,UAAA,CAAA;CAC5B,MAAA,IAAIlF,IAAI,GAAGjB,KAAK,CAACE,MAAM,CAACG,EAAE,CAAC,CAAA;OAE3B,IAAIA,EAAE,CAAC9H,WAAW,EAAE,KAAK2N,eAAe,IAAI,CAAAC,CAAAA,UAAA,GAAAlF,IAAI,CAAC7F,IAAI,MAAA,IAAA,IAAA+K,UAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAATA,UAAA,CAAW5N,WAAW,EAAE,MAAK2N,eAAe,EAAE;SACzF,OAAO;WAAClG,KAAK;WAAEK,EAAE;CAAE4F,UAAAA,KAAK,EAAEzO,CAAC;WAAE,GAAGyJ,IAAAA;UAAK,CAAA;CACtC,OAAA;CAEAzJ,MAAAA,CAAC,EAAE,CAAA;CACJ,KAAA;KAEA,MAAM,IAAImI,SAAS,CAAE,CAAMgF,IAAAA,EAAAA,KAAM,yBAAwB3E,KAAK,CAAC5E,IAAK,CAAA,uBAAA,EAAyBlD,MAAM,CAACgK,IAAI,CAAClC,KAAK,CAACE,MAAM,CAAC,CAACkG,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC,CAAC,CAAA;CACrI,GAAA;CAEA,EAAA,OAAOC,cAAc,GAAG;CACvBrO,IAAAA,IAAI,EAAE,WAAW;CACjBoD,IAAAA,IAAI,EAAE,OAAA;IACN,CAAA;CACF,CAAA;CAEA,SAAS8I,OAAOA,CAAElE,KAAK,EAAE;CACxB,EAAA,IAAIpI,GAAG,GAAG,CAACoI,KAAK,CAAC,CAAA;GAEjB,KAAK,IAAIsG,CAAC,GAAGtG,KAAK,EAAEsG,CAAC,GAAGA,CAAC,CAACxJ,IAAI,GAAG;CAChClF,IAAAA,GAAG,CAACuD,IAAI,CAACmL,CAAC,CAAC,CAAA;CACZ,GAAA;CAEA,EAAA,OAAO1O,GAAG,CAAA;CACX,CAAA;CAEA,SAASiN,aAAaA,CAAE5E,MAAM,EAAiB;GAAA,IAAf;CAACC,IAAAA,MAAAA;CAAM,GAAC,GAAA9C,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;GAC5C,IAAI6C,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAAC9D,YAAY,EAAE;CAC1C8D,IAAAA,MAAM,CAACjI,IAAI,KAAXiI,MAAM,CAACjI,IAAI,GAAK,UAAU,CAAA,CAAA;CAC1BiI,IAAAA,MAAM,CAAC7E,IAAI,KAAX6E,MAAM,CAAC7E,IAAI,GAAK,OAAO,CAAA,CAAA;;CAEvB;KACA6E,MAAM,CAAC9D,YAAY,GAAGF,iBAAiB,CAACgE,MAAM,CAACC,MAAM,CAAC,CAAA;CAEtD,IAAA,IAAIqG,YAAY,GAAGrO,MAAM,CAACkI,OAAO,CAACF,MAAM,CAAC,CAAC/I,GAAG,CAAC,CAAAqP,KAAA,EAAkBhP,CAAC,KAAK;CAAA,MAAA,IAAvB,CAAC6I,EAAE,EAAEC,SAAS,CAAC,GAAAkG,KAAA,CAAA;CAC7D;OACA,IAAIC,UAAU,GAAGxG,MAAM,CAAC9D,YAAY,CAAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;OAE1C,IAAImJ,SAAS,GAAGL,SAAS,CAACjE,KAAK,IAAIiE,SAAS,CAACO,QAAQ,CAAA;CACrD,MAAA,IAAID,OAAO,GAAG6F,UAAU,CAACpK,KAAK;CAAEqK,QAAAA,MAAM,GAAG,EAAE,CAAA;;CAE3C;OACA,IAAID,UAAU,IAAI,cAAc,EAAE;CACjC7F,QAAAA,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;CAClB8F,QAAAA,MAAM,GAAG,GAAG,CAAA;CACb,OAAC,MACI,IAAID,UAAU,IAAI,SAAS,EAAE;CACjCC,QAAAA,MAAM,GAAG,KAAK,CAAA;CACf,OAAA;OAEA,OAAQ;SAAC/F,SAAS;SAAEC,OAAO;CAAE8F,QAAAA,MAAAA;QAAO,CAAA;CACrC,KAAC,CAAC,CAAA;CAEFzG,IAAAA,MAAM,CAAC0G,eAAe,GAAG,CAACzG,MAAM,EAAEvH,SAAS,KAAK;OAC/C,OAAOuH,MAAM,CAAC/I,GAAG,CAAC,CAACU,CAAC,EAAEL,CAAC,KAAK;SAC3B,IAAI;WAACmJ,SAAS;WAAEC,OAAO;CAAE8F,UAAAA,MAAAA;CAAM,SAAC,GAAGH,YAAY,CAAC/O,CAAC,CAAC,CAAA;SAElD,IAAImJ,SAAS,IAAIC,OAAO,EAAE;WACzB/I,CAAC,GAAGiE,QAAQ,CAAC6E,SAAS,EAAEC,OAAO,EAAE/I,CAAC,CAAC,CAAA;CACpC,SAAA;CAEAA,QAAAA,CAAC,GAAGW,eAAe,CAACX,CAAC,EAAE;WAACc,SAAS;CAAEC,UAAAA,IAAI,EAAE8N,MAAAA;CAAM,SAAC,CAAC,CAAA;CAEjD,QAAA,OAAO7O,CAAC,CAAA;CACT,OAAC,CAAC,CAAA;MACF,CAAA;CACF,GAAA;CAEA,EAAA,OAAOoI,MAAM,CAAA;CACd;;ACrbA,eAAe,IAAIyB,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,SAAS;CACbjF,EAAAA,IAAI,EAAE,SAAS;CACf8E,EAAAA,MAAM,EAAE;CACP9I,IAAAA,CAAC,EAAE;CAACgE,MAAAA,IAAI,EAAE,GAAA;MAAI;CACdwL,IAAAA,CAAC,EAAE;CAACxL,MAAAA,IAAI,EAAE,GAAA;MAAI;CACdyL,IAAAA,CAAC,EAAE;CAACzL,MAAAA,IAAI,EAAE,GAAA;CAAG,KAAA;IACb;CACDsI,EAAAA,KAAK,EAAE,KAAK;CACZd,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNK,MAAAA,GAAG,EAAE,CAAC,SAAS,EAAE,KAAK,CAAA;CACvB,KAAA;IACA;GACDgC,OAAO,EAAE,CAAC,KAAK,CAAA;CAChB,CAAC,CAAC;;CCZF;CACA;CACA;CACA;CACe,MAAMuD,aAAa,SAASpF,UAAU,CAAC;CACrD;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;GACCwB,WAAWA,CAAExD,OAAO,EAAE;CAAA,IAAA,IAAAqH,iBAAA,CAAA;CACrB,IAAA,IAAI,CAACrH,OAAO,CAACQ,MAAM,EAAE;OACpBR,OAAO,CAACQ,MAAM,GAAG;CAChB8G,QAAAA,CAAC,EAAE;CACF3K,UAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACbjB,UAAAA,IAAI,EAAE,KAAA;UACN;CACD6L,QAAAA,CAAC,EAAE;CACF5K,UAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACbjB,UAAAA,IAAI,EAAE,OAAA;UACN;CACD8L,QAAAA,CAAC,EAAE;CACF7K,UAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CACbjB,UAAAA,IAAI,EAAE,MAAA;CACP,SAAA;QACA,CAAA;CACF,KAAA;CAEA,IAAA,IAAI,CAACsE,OAAO,CAAC5C,IAAI,EAAE;OAClB4C,OAAO,CAAC5C,IAAI,GAAGqK,OAAO,CAAA;CACvB,KAAA;CAEA,IAAA,IAAIzH,OAAO,CAAC0H,OAAO,IAAI1H,OAAO,CAAC2H,SAAS,EAAE;OAAA,IAAAC,eAAA,EAAAC,iBAAA,CAAA;CACzC,MAAA,CAAAD,eAAA,GAAA5H,OAAO,CAAC+D,MAAM,MAAA6D,IAAAA,IAAAA,eAAA,KAAAA,KAAAA,CAAAA,GAAAA,eAAA,GAAd5H,OAAO,CAAC+D,MAAM,GAAK+D,GAAG,IAAI;SACzB,IAAIC,GAAG,GAAG7Q,gBAAgB,CAAC8I,OAAO,CAAC0H,OAAO,EAAEI,GAAG,CAAC,CAAA;SAEhD,IAAI,IAAI,CAAC9D,KAAK,KAAK,IAAI,CAAC5G,IAAI,CAAC4G,KAAK,EAAE;CACnC;CACA+D,UAAAA,GAAG,GAAGnI,OAAK,CAAC,IAAI,CAACoE,KAAK,EAAE,IAAI,CAAC5G,IAAI,CAAC4G,KAAK,EAAE+D,GAAG,CAAC,CAAA;CAC9C,SAAA;CAEA,QAAA,OAAOA,GAAG,CAAA;QACV,CAAA;CAED,MAAA,CAAAF,iBAAA,GAAA7H,OAAO,CAAC8D,QAAQ,MAAA+D,IAAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,GAAAA,iBAAA,GAAhB7H,OAAO,CAAC8D,QAAQ,GAAKiE,GAAG,IAAI;CAC3BA,QAAAA,GAAG,GAAGnI,OAAK,CAAC,IAAI,CAACxC,IAAI,CAAC4G,KAAK,EAAE,IAAI,CAACA,KAAK,EAAE+D,GAAG,CAAC,CAAA;CAC7C,QAAA,OAAO7Q,gBAAgB,CAAC8I,OAAO,CAAC2H,SAAS,EAAEI,GAAG,CAAC,CAAA;QAC/C,CAAA;CACF,KAAA;CAEA,IAAA,CAAAV,iBAAA,GAAArH,OAAO,CAACsE,QAAQ,MAAA+C,IAAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,GAAAA,iBAAA,GAAhBrH,OAAO,CAACsE,QAAQ,GAAK,SAAS,CAAA;KAE9B,KAAK,CAACtE,OAAO,CAAC,CAAA;CACf,GAAA;CACD;;CC5DA;CACA;CACA;CACA;CACA;CACA;CACe,SAASgI,MAAMA,CAAExG,KAAK,EAAElB,KAAK,EAAE;CAC7CkB,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;GAEvB,IAAI,CAAClB,KAAK,IAAIkB,KAAK,CAAClB,KAAK,CAACwE,MAAM,CAACxE,KAAK,CAAC,EAAE;CACxC;CACA,IAAA,OAAOkB,KAAK,CAAChB,MAAM,CAACrF,KAAK,EAAE,CAAA;CAC5B,GAAA;CAEAmF,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAC7B,EAAA,OAAOA,KAAK,CAACjE,IAAI,CAACmF,KAAK,CAAC,CAAA;CACzB;;CCfe,SAAS8B,GAAGA,CAAE9B,KAAK,EAAEyG,IAAI,EAAE;CACzCzG,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;GAEvB,IAAI;KAAClB,KAAK;CAAEiG,IAAAA,KAAAA;IAAM,GAAGvE,UAAU,CAACkE,YAAY,CAAC+B,IAAI,EAAEzG,KAAK,CAAClB,KAAK,CAAC,CAAA;CAC/D,EAAA,IAAIE,MAAM,GAAGwH,MAAM,CAACxG,KAAK,EAAElB,KAAK,CAAC,CAAA;GACjC,OAAOE,MAAM,CAAC+F,KAAK,CAAC,CAAA;CACrB;;CCPe,SAAS2B,MAAMA,CAAE1G,KAAK,EAAElB,KAAK,EAAEE,MAAM,EAAE;CACrDgB,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;CAEvBlB,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAC7BkB,EAAAA,KAAK,CAAChB,MAAM,GAAGF,KAAK,CAAChE,EAAE,CAACkF,KAAK,CAAClB,KAAK,EAAEE,MAAM,CAAC,CAAA;CAC5C,EAAA,OAAOgB,KAAK,CAAA;CACb,CAAA;CAEA0G,MAAM,CAACC,OAAO,GAAG,OAAO;;CCJxB;CACe,SAASC,GAAGA,CAAE5G,KAAK,EAAEyG,IAAI,EAAE9L,KAAK,EAAE;CAChDqF,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;CAEvB,EAAA,IAAI9D,SAAS,CAACpG,MAAM,KAAK,CAAC,IAAIgB,IAAI,CAACoF,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;CAC9D;CACA,IAAA,IAAI2K,MAAM,GAAG3K,SAAS,CAAC,CAAC,CAAC,CAAA;CACzB,IAAA,KAAK,IAAI/F,CAAC,IAAI0Q,MAAM,EAAE;OACrBD,GAAG,CAAC5G,KAAK,EAAE7J,CAAC,EAAE0Q,MAAM,CAAC1Q,CAAC,CAAC,CAAC,CAAA;CACzB,KAAA;CACD,GAAC,MACI;CACJ,IAAA,IAAI,OAAOwE,KAAK,KAAK,UAAU,EAAE;OAChCA,KAAK,GAAGA,KAAK,CAACmH,GAAG,CAAC9B,KAAK,EAAEyG,IAAI,CAAC,CAAC,CAAA;CAChC,KAAA;KAEA,IAAI;OAAC3H,KAAK;CAAEiG,MAAAA,KAAAA;MAAM,GAAGvE,UAAU,CAACkE,YAAY,CAAC+B,IAAI,EAAEzG,KAAK,CAAClB,KAAK,CAAC,CAAA;CAC/D,IAAA,IAAIE,MAAM,GAAGwH,MAAM,CAACxG,KAAK,EAAElB,KAAK,CAAC,CAAA;CACjCE,IAAAA,MAAM,CAAC+F,KAAK,CAAC,GAAGpK,KAAK,CAAA;CACrB+L,IAAAA,MAAM,CAAC1G,KAAK,EAAElB,KAAK,EAAEE,MAAM,CAAC,CAAA;CAC7B,GAAA;CAEA,EAAA,OAAOgB,KAAK,CAAA;CACb,CAAA;CAEA4G,GAAG,CAACD,OAAO,GAAG,OAAO;;AC5BrB,eAAe,IAAInG,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,SAAS;CACbjF,EAAAA,IAAI,EAAE,SAAS;CACfsI,EAAAA,KAAK,EAAE,KAAK;CACZ5G,EAAAA,IAAI,EAAEqK,OAAO;CACb3D,EAAAA,QAAQ,EAAEtD,MAAM,IAAIZ,OAAK,CAAC6H,OAAO,CAACzD,KAAK,EAAE,KAAK,EAAExD,MAAM,CAAC;GACvDuD,MAAM,EAAEvD,MAAM,IAAIZ,OAAK,CAAC,KAAK,EAAE6H,OAAO,CAACzD,KAAK,EAAExD,MAAM,CAAA;CACrD,CAAC,CAAC;;CCPF;CACA,MAAM+C,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAM+E,IAAE,GAAG,EAAE,GAAG,GAAG,CAAA;CACnB,MAAMC,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;;CAErB,IAAIvE,OAAK,GAAGxE,MAAM,CAACC,GAAG,CAAA;AAEtB,WAAe,IAAIuC,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACD+M,IAAAA,CAAC,EAAE;CACFtH,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;MACpB;CACDqG,IAAAA,CAAC,EAAE;CACFrG,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;CACrB,KAAA;IACA;CAED;CACA;UACA6C,OAAK;CAEL5G,EAAAA,IAAI,EAAEsL,OAAO;CACb;CACA;GACA5E,QAAQA,CAAE/D,GAAG,EAAE;CACd;CACA,IAAA,IAAIgI,GAAG,GAAGhI,GAAG,CAACtI,GAAG,CAAC,CAAC0E,KAAK,EAAErE,CAAC,KAAKqE,KAAK,GAAG6H,OAAK,CAAClM,CAAC,CAAC,CAAC,CAAA;;CAEjD;KACA,IAAI6Q,CAAC,GAAGZ,GAAG,CAACtQ,GAAG,CAAC0E,KAAK,IAAIA,KAAK,GAAGoH,GAAC,GAAG5J,IAAI,CAACiP,IAAI,CAACzM,KAAK,CAAC,GAAG,CAACoM,GAAC,GAAGpM,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC,CAAA;KAE/E,OAAO,CACL,GAAG,GAAGwM,CAAC,CAAC,CAAC,CAAC,GAAI,EAAE;CAAI;KACrB,GAAG,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE;KACrB,GAAG,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAA;IACD;CACD;CACA;CACA;GACA5E,MAAMA,CAAE8E,GAAG,EAAE;CACZ;KACA,IAAIF,CAAC,GAAG,EAAE,CAAA;CACVA,IAAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAACE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;CAC1BF,IAAAA,CAAC,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGF,CAAC,CAAC,CAAC,CAAC,CAAA;CAC1BA,IAAAA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;;CAE1B;KACA,IAAId,GAAG,GAAG,CACTY,CAAC,CAAC,CAAC,CAAC,GAAKL,IAAE,GAAG3O,IAAI,CAACmP,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAkB,CAAC,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIJ,GAAC,EACtEM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAIlP,IAAI,CAACmP,GAAG,CAAC,CAACD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGN,GAAC,EAC3DI,CAAC,CAAC,CAAC,CAAC,GAAKL,IAAE,GAAG3O,IAAI,CAACmP,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAkB,CAAC,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIJ,GAAC,CACtE,CAAA;;CAED;CACA,IAAA,OAAOR,GAAG,CAACtQ,GAAG,CAAC,CAAC0E,KAAK,EAAErE,CAAC,KAAKqE,KAAK,GAAG6H,OAAK,CAAClM,CAAC,CAAC,CAAC,CAAA;IAC9C;CAEDoL,EAAAA,OAAO,EAAE;CACR,IAAA,KAAK,EAAE;CACN1C,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAA;CACrG,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCzEK,SAASuI,SAASA,CAAEC,KAAK,EAAE;CACjC,EAAA,OAAO,CAAEA,KAAK,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,CAAA;CACnC,CAAA;CAEO,SAASC,MAAMA,CAAEC,GAAG,EAAEC,MAAM,EAAE;GACpC,IAAID,GAAG,KAAK,KAAK,EAAE;CAClB,IAAA,OAAOC,MAAM,CAAA;CACd,GAAA;GAEA,IAAI,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGF,MAAM,CAAC1R,GAAG,CAACsR,SAAS,CAAC,CAAA;CAEpC,EAAA,IAAIO,SAAS,GAAGD,EAAE,GAAGD,EAAE,CAAA;GAEvB,IAAIF,GAAG,KAAK,YAAY,EAAE;KACzB,IAAII,SAAS,GAAG,CAAC,EAAE;CAClBD,MAAAA,EAAE,IAAI,GAAG,CAAA;CACV,KAAA;CACD,GAAC,MACI,IAAIH,GAAG,KAAK,YAAY,EAAE;KAC9B,IAAII,SAAS,GAAG,CAAC,EAAE;CAClBF,MAAAA,EAAE,IAAI,GAAG,CAAA;CACV,KAAA;CACD,GAAC,MACI,IAAIF,GAAG,KAAK,QAAQ,EAAE;KAC1B,IAAI,CAAC,GAAG,GAAGI,SAAS,IAAIA,SAAS,GAAG,GAAG,EAAE;OACxC,IAAIA,SAAS,GAAG,CAAC,EAAE;CAClBF,QAAAA,EAAE,IAAI,GAAG,CAAA;CACV,OAAC,MACI;CACJC,QAAAA,EAAE,IAAI,GAAG,CAAA;CACV,OAAA;CACD,KAAA;CACD,GAAC,MACI,IAAIH,GAAG,KAAK,SAAS,EAAE;KAC3B,IAAII,SAAS,GAAG,GAAG,EAAE;CACpBF,MAAAA,EAAE,IAAI,GAAG,CAAA;CACV,KAAC,MACI,IAAIE,SAAS,GAAG,CAAC,GAAG,EAAE;CAC1BD,MAAAA,EAAE,IAAI,GAAG,CAAA;CACV,KAAA;CACD,GAAA;CAEA,EAAA,OAAO,CAACD,EAAE,EAAEC,EAAE,CAAC,CAAA;CAChB;;ACvCA,WAAe,IAAIrH,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACDvD,IAAAA,CAAC,EAAE;CACFgJ,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,QAAA;MACN;CACD6N,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAEyL,GAAG;GACT/E,QAAQA,CAAE+E,GAAG,EAAE;CACd;KACA,IAAI,CAACW,CAAC,EAAEf,CAAC,EAAEjB,CAAC,CAAC,GAAGqB,GAAG,CAAA;CACnB,IAAA,IAAIY,GAAG,CAAA;KACP,MAAMlG,CAAC,GAAG,IAAI,CAAA;CAEd,IAAA,IAAI5J,IAAI,CAACE,GAAG,CAAC4O,CAAC,CAAC,GAAGlF,CAAC,IAAI5J,IAAI,CAACE,GAAG,CAAC2N,CAAC,CAAC,GAAGjE,CAAC,EAAE;CACvCkG,MAAAA,GAAG,GAAGpO,GAAG,CAAA;CACV,KAAC,MACI;CACJoO,MAAAA,GAAG,GAAG9P,IAAI,CAAC+P,KAAK,CAAClC,CAAC,EAAEiB,CAAC,CAAC,GAAG,GAAG,GAAG9O,IAAI,CAACS,EAAE,CAAA;CACvC,KAAA;CAEA,IAAA,OAAO,CACNoP,CAAC;CAAE;KACH7P,IAAI,CAACgQ,IAAI,CAAClB,CAAC,IAAI,CAAC,GAAGjB,CAAC,IAAI,CAAC,CAAC;CAAE;KAC5BoC,SAAc,CAACH,GAAG,CAAC;MACnB,CAAA;IACD;GACD1F,MAAMA,CAAE8F,GAAG,EAAE;CACZ;KACA,IAAI,CAACC,SAAS,EAAEC,MAAM,EAAEC,GAAG,CAAC,GAAGH,GAAG,CAAA;CAClC;KACA,IAAIE,MAAM,GAAG,CAAC,EAAE;CACfA,MAAAA,MAAM,GAAG,CAAC,CAAA;CACX,KAAA;CACA;CACA,IAAA,IAAIzQ,KAAK,CAAC0Q,GAAG,CAAC,EAAE;CACfA,MAAAA,GAAG,GAAG,CAAC,CAAA;CACR,KAAA;CACA,IAAA,OAAO,CACNF,SAAS;CAAE;CACXC,IAAAA,MAAM,GAAGpQ,IAAI,CAACsQ,GAAG,CAACD,GAAG,GAAGrQ,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;CAAE;CACxC2P,IAAAA,MAAM,GAAGpQ,IAAI,CAACuQ,GAAG,CAACF,GAAG,GAAGrQ,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;MACtC,CAAA;IACD;CAED8I,EAAAA,OAAO,EAAE;CACR,IAAA,KAAK,EAAE;CACN1C,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,oBAAoB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CC7DF;CACA;CACA;CACA;CACA;CACA;CACA;;CAEA,MAAM2J,OAAO,GAAG,EAAE,IAAI,CAAC,CAAA;CACvB,MAAMC,GAAC,GAAGzQ,IAAI,CAACS,EAAE,CAAA;CACjB,MAAMiQ,GAAG,GAAG,GAAG,GAAGD,GAAC,CAAA;CACnB,MAAME,KAAG,GAAGF,GAAC,GAAG,GAAG,CAAA;CAEnB,SAASG,IAAIA,CAAE7S,CAAC,EAAE;CACjB;;CAEA,EAAA,MAAM8S,EAAE,GAAG9S,CAAC,GAAGA,CAAC,CAAA;GAChB,MAAM+S,EAAE,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG9S,CAAC,CAAA;CAE3B,EAAA,OAAO+S,EAAE,CAAA;CACV,CAAA;CAEe,mBAAUjJ,EAAAA,KAAK,EAAEkJ,MAAM,EAAiC;GAAA,IAA/B;CAACC,IAAAA,EAAE,GAAG,CAAC;CAAEC,IAAAA,EAAE,GAAG,CAAC;CAAEC,IAAAA,EAAE,GAAG,CAAA;CAAC,GAAC,GAAAnN,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CACpE,EAAA,CAAC8D,KAAK,EAAEkJ,MAAM,CAAC,GAAGrH,QAAQ,CAAC,CAAC7B,KAAK,EAAEkJ,MAAM,CAAC,CAAC,CAAA;;CAE3C;CACA;CACA;;CAEA;CACA;CACA;CACA;CACA;CACA;;CAEA,EAAA,IAAI,CAACI,EAAE,EAAE1B,EAAE,EAAE2B,EAAE,CAAC,GAAGC,GAAG,CAAC3O,IAAI,CAACmF,KAAK,CAAC,CAAA;CAClC,EAAA,IAAIyJ,EAAE,GAAGC,GAAG,CAAC7O,IAAI,CAAC2O,GAAG,EAAE,CAACF,EAAE,EAAE1B,EAAE,EAAE2B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CACvC,EAAA,IAAI,CAACI,EAAE,EAAE9B,EAAE,EAAE+B,EAAE,CAAC,GAAGJ,GAAG,CAAC3O,IAAI,CAACqO,MAAM,CAAC,CAAA;CACnC,EAAA,IAAIW,EAAE,GAAGH,GAAG,CAAC7O,IAAI,CAAC2O,GAAG,EAAE,CAACG,EAAE,EAAE9B,EAAE,EAAE+B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;;CAEvC;CACA;CACA;;GAEA,IAAIH,EAAE,GAAG,CAAC,EAAE;CACXA,IAAAA,EAAE,GAAG,CAAC,CAAA;CACP,GAAA;GACA,IAAII,EAAE,GAAG,CAAC,EAAE;CACXA,IAAAA,EAAE,GAAG,CAAC,CAAA;CACP,GAAA;GAEA,IAAIC,IAAI,GAAG,CAACL,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC;;CAEzB;CACA;CACA,EAAA,IAAIE,EAAE,GAAGhB,IAAI,CAACe,IAAI,CAAC,CAAA;CAEnB,EAAA,IAAIE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG7R,IAAI,CAACgQ,IAAI,CAAC4B,EAAE,IAAIA,EAAE,GAAGpB,OAAO,CAAC,CAAC,CAAC,CAAA;;CAElD;CACA;CACA,EAAA,IAAIsB,MAAM,GAAG,CAAC,CAAC,GAAGD,CAAC,IAAIpC,EAAE,CAAA;CACzB,EAAA,IAAIsC,MAAM,GAAG,CAAC,CAAC,GAAGF,CAAC,IAAInC,EAAE,CAAA;;CAEzB;CACA,EAAA,IAAIsC,MAAM,GAAGhS,IAAI,CAACgQ,IAAI,CAAC8B,MAAM,IAAI,CAAC,GAAGV,EAAE,IAAI,CAAC,CAAC,CAAA;CAC7C,EAAA,IAAIa,MAAM,GAAGjS,IAAI,CAACgQ,IAAI,CAAC+B,MAAM,IAAI,CAAC,GAAGN,EAAE,IAAI,CAAC,CAAC,CAAA;;CAE7C;CACA;;CAEA,EAAA,IAAIS,EAAE,GAAIJ,MAAM,KAAK,CAAC,IAAIV,EAAE,KAAK,CAAC,GAAI,CAAC,GAAGpR,IAAI,CAAC+P,KAAK,CAACqB,EAAE,EAAEU,MAAM,CAAC,CAAA;CAChE,EAAA,IAAIK,EAAE,GAAIJ,MAAM,KAAK,CAAC,IAAIN,EAAE,KAAK,CAAC,GAAI,CAAC,GAAGzR,IAAI,CAAC+P,KAAK,CAAC0B,EAAE,EAAEM,MAAM,CAAC,CAAA;GAEhE,IAAIG,EAAE,GAAG,CAAC,EAAE;KACXA,EAAE,IAAI,CAAC,GAAGzB,GAAC,CAAA;CACZ,GAAA;GACA,IAAI0B,EAAE,GAAG,CAAC,EAAE;KACXA,EAAE,IAAI,CAAC,GAAG1B,GAAC,CAAA;CACZ,GAAA;CAEAyB,EAAAA,EAAE,IAAIxB,GAAG,CAAA;CACTyB,EAAAA,EAAE,IAAIzB,GAAG,CAAA;;CAET;CACA,EAAA,IAAI0B,EAAE,GAAGZ,EAAE,GAAGL,EAAE,CAAA;CAChB,EAAA,IAAIkB,EAAE,GAAGJ,MAAM,GAAGD,MAAM,CAAA;;CAExB;CACA,EAAA,IAAIM,KAAK,GAAGH,EAAE,GAAGD,EAAE,CAAA;CACnB,EAAA,IAAIK,IAAI,GAAGL,EAAE,GAAGC,EAAE,CAAA;CAClB,EAAA,IAAIK,IAAI,GAAGxS,IAAI,CAACE,GAAG,CAACoS,KAAK,CAAC,CAAA;CAC1B,EAAA,IAAIG,EAAE,CAAA;CAEN,EAAA,IAAIT,MAAM,GAAGC,MAAM,KAAK,CAAC,EAAE;CAC1BQ,IAAAA,EAAE,GAAG,CAAC,CAAA;CACP,GAAC,MACI,IAAID,IAAI,IAAI,GAAG,EAAE;CACrBC,IAAAA,EAAE,GAAGH,KAAK,CAAA;CACX,GAAC,MACI,IAAIA,KAAK,GAAG,GAAG,EAAE;KACrBG,EAAE,GAAGH,KAAK,GAAG,GAAG,CAAA;CACjB,GAAC,MACI,IAAIA,KAAK,GAAG,CAAC,GAAG,EAAE;KACtBG,EAAE,GAAGH,KAAK,GAAG,GAAG,CAAA;CACjB,GAAC,MACI;CACJtJ,IAAAA,QAAQ,CAAC7D,IAAI,CAAC,8BAA8B,CAAC,CAAA;CAC9C,GAAA;;CAEA;GACA,IAAIuN,EAAE,GAAG,CAAC,GAAG1S,IAAI,CAACgQ,IAAI,CAACiC,MAAM,GAAGD,MAAM,CAAC,GAAGhS,IAAI,CAACuQ,GAAG,CAACkC,EAAE,GAAG9B,KAAG,GAAG,CAAC,CAAC,CAAA;;CAEhE;CACA,EAAA,IAAIgC,KAAK,GAAG,CAACxB,EAAE,GAAGK,EAAE,IAAI,CAAC,CAAA;CACzB,EAAA,IAAIoB,KAAK,GAAG,CAACZ,MAAM,GAAGC,MAAM,IAAI,CAAC,CAAA;CACjC,EAAA,IAAIY,MAAM,GAAGjC,IAAI,CAACgC,KAAK,CAAC,CAAA;;CAExB;CACA;CACA;CACA,EAAA,IAAIE,KAAK,CAAA;CACT,EAAA,IAAId,MAAM,GAAGC,MAAM,KAAK,CAAC,EAAE;KAC1Ba,KAAK,GAAGP,IAAI,CAAC;CACd,GAAC,MACI,IAAIC,IAAI,IAAI,GAAG,EAAE;KACrBM,KAAK,GAAGP,IAAI,GAAG,CAAC,CAAA;CACjB,GAAC,MACI,IAAIA,IAAI,GAAG,GAAG,EAAE;CACpBO,IAAAA,KAAK,GAAG,CAACP,IAAI,GAAG,GAAG,IAAI,CAAC,CAAA;CACzB,GAAC,MACI;CACJO,IAAAA,KAAK,GAAG,CAACP,IAAI,GAAG,GAAG,IAAI,CAAC,CAAA;CACzB,GAAA;;CAEA;CACA;;CAEA;CACA;CACA,EAAA,IAAIQ,GAAG,GAAG,CAACJ,KAAK,GAAG,EAAE,KAAK,CAAC,CAAA;CAC3B,EAAA,IAAIK,EAAE,GAAG,CAAC,GAAK,KAAK,GAAGD,GAAG,GAAI/S,IAAI,CAACgQ,IAAI,CAAC,EAAE,GAAG+C,GAAG,CAAE,CAAA;;CAElD;CACA,EAAA,IAAIE,EAAE,GAAG,CAAC,GAAG,KAAK,GAAGL,KAAK,CAAA;;CAE1B;GACA,IAAIM,CAAC,GAAG,CAAC,CAAA;CACTA,EAAAA,CAAC,IAAK,IAAI,GAAGlT,IAAI,CAACsQ,GAAG,CAAC,CAAMwC,KAAK,GAAG,EAAE,IAAKnC,KAAG,CAAE,CAAA;CAChDuC,EAAAA,CAAC,IAAK,IAAI,GAAGlT,IAAI,CAACsQ,GAAG,CAAG,CAAC,GAAGwC,KAAK,GAAUnC,KAAG,CAAE,CAAA;CAChDuC,EAAAA,CAAC,IAAK,IAAI,GAAGlT,IAAI,CAACsQ,GAAG,CAAC,CAAE,CAAC,GAAGwC,KAAK,GAAI,CAAC,IAAKnC,KAAG,CAAE,CAAA;CAChDuC,EAAAA,CAAC,IAAK,IAAI,GAAGlT,IAAI,CAACsQ,GAAG,CAAC,CAAE,CAAC,GAAGwC,KAAK,GAAI,EAAE,IAAInC,KAAG,CAAE,CAAA;;CAEhD;CACA;GACA,IAAIwC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAGP,KAAK,GAAGM,CAAC,CAAA;;CAE9B;CACA;CACA;CACA;GACA,IAAIE,EAAE,GAAG,EAAE,GAAGpT,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAAC,GAAI,CAAC,CAACoP,KAAK,GAAG,GAAG,IAAI,EAAE,KAAK,CAAE,CAAC,CAAA;CACxD,EAAA,IAAIO,EAAE,GAAG,CAAC,GAAGrT,IAAI,CAACgQ,IAAI,CAAC6C,MAAM,IAAIA,MAAM,GAAGrC,OAAO,CAAC,CAAC,CAAA;CACnD,EAAA,IAAI8C,EAAE,GAAG,CAAC,CAAC,GAAGtT,IAAI,CAACuQ,GAAG,CAAC,CAAC,GAAG6C,EAAE,GAAGzC,KAAG,CAAC,GAAG0C,EAAE,CAAA;;CAEzC;GACA,IAAIE,EAAE,GAAG,CAACnB,EAAE,IAAIpB,EAAE,GAAGgC,EAAE,CAAC,KAAK,CAAC,CAAA;GAC9BO,EAAE,IAAI,CAAClB,EAAE,IAAIpB,EAAE,GAAGgC,EAAE,CAAC,KAAK,CAAC,CAAA;GAC3BM,EAAE,IAAI,CAACb,EAAE,IAAIxB,EAAE,GAAGiC,EAAE,CAAC,KAAK,CAAC,CAAA;CAC3BI,EAAAA,EAAE,IAAID,EAAE,IAAIjB,EAAE,IAAIpB,EAAE,GAAGgC,EAAE,CAAC,CAAC,IAAIP,EAAE,IAAIxB,EAAE,GAAGiC,EAAE,CAAC,CAAC,CAAA;CAC9C,EAAA,OAAOnT,IAAI,CAACgQ,IAAI,CAACuD,EAAE,CAAC,CAAA;CACpB;CACD;;CC9KA;CACA;CACA,MAAMC,YAAU,GAAG,CAClB,CAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EAC/D,CAAE,kBAAkB,EAAE,kBAAkB,EAAG,kBAAkB,CAAE,EAC/D,CAAE,kBAAkB,EAAE,kBAAkB,EAAG,kBAAkB,CAAE,CAC/D,CAAA;CACD;CACA,MAAMC,YAAU,GAAG,CAClB,CAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAG,kBAAkB,CAAE,EACjE,CAAE,CAAC,kBAAkB,EAAG,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EACjE,CAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAG,kBAAkB,CAAE,CACjE,CAAA;CACD,MAAMC,UAAU,GAAG,CAClB,CAAE,kBAAkB,EAAG,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EAChE,CAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAG,kBAAkB,CAAE,EAChE,CAAE,kBAAkB,EAAG,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,CAChE,CAAA;CACD;CACA,MAAMC,UAAU,GAAG,CAClB,CAAE,kBAAkB,EAAG,kBAAkB,EAAG,kBAAkB,CAAE,EAChE,CAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EAChE,CAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,CAChE,CAAA;AAED,aAAe,IAAItL,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,OAAO;CACXjF,EAAAA,IAAI,EAAE,OAAO;CACb8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAChBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACD+M,IAAAA,CAAC,EAAE;CACFtH,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;MACpB;CACDqG,IAAAA,CAAC,EAAE;CACFrG,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;CACrB,KAAA;IACA;CAED;CACA6C,EAAAA,KAAK,EAAE,KAAK;CACZ5G,EAAAA,IAAI,EAAEqK,OAAO;GACb3D,QAAQA,CAAE/D,GAAG,EAAE;CACd;CACA,IAAA,IAAIwN,GAAG,GAAGrW,gBAAgB,CAACiW,YAAU,EAAEpN,GAAG,CAAC,CAAA;;CAE3C;CACA,IAAA,IAAIyN,IAAI,GAAGD,GAAG,CAAC9V,GAAG,CAACsF,GAAG,IAAIpD,IAAI,CAACiP,IAAI,CAAC7L,GAAG,CAAC,CAAC,CAAA;CAEzC,IAAA,OAAO7F,gBAAgB,CAACmW,UAAU,EAAEG,IAAI,CAAC,CAAA;IAEzC;GACDzJ,MAAMA,CAAE0J,KAAK,EAAE;CACd;CACA,IAAA,IAAID,IAAI,GAAGtW,gBAAgB,CAACoW,UAAU,EAAEG,KAAK,CAAC,CAAA;;CAE9C;KACA,IAAIF,GAAG,GAAGC,IAAI,CAAC/V,GAAG,CAACsF,GAAG,IAAIA,GAAG,IAAI,CAAC,CAAC,CAAA;CAEnC,IAAA,OAAO7F,gBAAgB,CAACkW,YAAU,EAAEG,GAAG,CAAC,CAAA;IACxC;CAEDrK,EAAAA,OAAO,EAAE;CACR,IAAA,OAAO,EAAE;CACR1C,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAA;CACrG,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCzEF;CACA;;CAKe,iBAAUgB,EAAAA,KAAK,EAAEkJ,MAAM,EAAE;CACvC,EAAA,CAAClJ,KAAK,EAAEkJ,MAAM,CAAC,GAAGrH,QAAQ,CAAC,CAAC7B,KAAK,EAAEkJ,MAAM,CAAC,CAAC,CAAA;;CAE3C;CACA;CACA;CACA,EAAA,IAAI,CAACI,EAAE,EAAE1B,EAAE,EAAE2B,EAAE,CAAC,GAAG2C,KAAK,CAACrR,IAAI,CAACmF,KAAK,CAAC,CAAA;CACpC,EAAA,IAAI,CAAC2J,EAAE,EAAE9B,EAAE,EAAE+B,EAAE,CAAC,GAAGsC,KAAK,CAACrR,IAAI,CAACqO,MAAM,CAAC,CAAA;CACrC,EAAA,IAAIqB,EAAE,GAAGjB,EAAE,GAAGK,EAAE,CAAA;CAChB,EAAA,IAAIwC,EAAE,GAAGvE,EAAE,GAAGC,EAAE,CAAA;CAChB,EAAA,IAAIuE,EAAE,GAAG7C,EAAE,GAAGK,EAAE,CAAA;CAChB,EAAA,OAAOzR,IAAI,CAACgQ,IAAI,CAACoC,EAAE,IAAI,CAAC,GAAG4B,EAAE,IAAI,CAAC,GAAGC,EAAE,IAAI,CAAC,CAAC,CAAA;CAC9C;;CCfA,MAAMrK,GAAC,GAAG,OAAO,CAAA;;CAEjB;CACA;CACA;CACA;CACe,SAASc,OAAOA,CAAE7C,KAAK,EAAElB,KAAK,EAAsB;GAAA,IAApB;CAACuE,IAAAA,OAAO,GAAGtB,GAAAA;CAAC,GAAC,GAAA7F,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CAChE8D,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;GAEvB,IAAI,CAAClB,KAAK,EAAE;KACXA,KAAK,GAAGkB,KAAK,CAAClB,KAAK,CAAA;CACpB,GAAA;CAEAA,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAC7B,EAAA,IAAIE,MAAM,GAAGgB,KAAK,CAAChB,MAAM,CAAA;CAEzB,EAAA,IAAIF,KAAK,KAAKkB,KAAK,CAAClB,KAAK,EAAE;CAC1BE,IAAAA,MAAM,GAAGF,KAAK,CAACjE,IAAI,CAACmF,KAAK,CAAC,CAAA;CAC3B,GAAA;CAEA,EAAA,OAAOlB,KAAK,CAAC+D,OAAO,CAAC7D,MAAM,EAAE;CAACqE,IAAAA,OAAAA;CAAO,GAAC,CAAC,CAAA;CACxC;;CCxBe,SAASgJ,KAAKA,CAAErM,KAAK,EAAE;GACrC,OAAO;KACNlB,KAAK,EAAEkB,KAAK,CAAClB,KAAK;CAClBE,IAAAA,MAAM,EAAEgB,KAAK,CAAChB,MAAM,CAACrF,KAAK,EAAE;KAC5BI,KAAK,EAAEiG,KAAK,CAACjG,KAAAA;IACb,CAAA;CACF;;CCJA;CACA;CACA;CACe,SAASuS,QAAQA,CAAEC,MAAM,EAAEC,MAAM,EAAiB;CAAA,EAAA,IAAf1N,KAAK,GAAA5C,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;CAC9D4C,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;;CAE7B;CACA,EAAA,IAAI2N,OAAO,GAAG3N,KAAK,CAACjE,IAAI,CAAC0R,MAAM,CAAC,CAAA;CAChC,EAAA,IAAIG,OAAO,GAAG5N,KAAK,CAACjE,IAAI,CAAC2R,MAAM,CAAC,CAAA;CAEhC,EAAA,OAAOrU,IAAI,CAACgQ,IAAI,CAACsE,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,EAAEvW,CAAC,KAAK;CAC/C,IAAA,IAAIwW,EAAE,GAAGJ,OAAO,CAACpW,CAAC,CAAC,CAAA;KACnB,IAAIwB,KAAK,CAAC+U,EAAE,CAAC,IAAI/U,KAAK,CAACgV,EAAE,CAAC,EAAE;CAC3B,MAAA,OAAOF,GAAG,CAAA;CACX,KAAA;CAEA,IAAA,OAAOA,GAAG,GAAG,CAACE,EAAE,GAAGD,EAAE,KAAK,CAAC,CAAA;IAC3B,EAAE,CAAC,CAAC,CAAC,CAAA;CACP;;CCjBe,SAASE,QAAQA,CAAE/M,KAAK,EAAEkJ,MAAM,EAAE;CAChD;CACA,EAAA,OAAOoD,QAAQ,CAACtM,KAAK,EAAEkJ,MAAM,EAAE,KAAK,CAAC,CAAA;CACtC;;CCFA;CACA;;CAEA;CACA;CACA;CACA;CACA;CACA,MAAMN,CAAC,GAAGzQ,IAAI,CAACS,EAAE,CAAA;CACjB,MAAMkQ,GAAG,GAAGF,CAAC,GAAG,GAAG,CAAA;CAEJ,kBAAU5I,EAAAA,KAAK,EAAEkJ,MAAM,EAAuB;GAAA,IAArB;CAAClC,IAAAA,CAAC,GAAG,CAAC;CAAErQ,IAAAA,CAAC,GAAG,CAAA;CAAC,GAAC,GAAAuF,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CAC1D,EAAA,CAAC8D,KAAK,EAAEkJ,MAAM,CAAC,GAAGrH,QAAQ,CAAC,CAAC7B,KAAK,EAAEkJ,MAAM,CAAC,CAAC,CAAA;;CAE3C;CACA;CACA;;CAEA;CACA;CACA;;CAEA,EAAA,IAAI,CAACI,EAAE,EAAE1B,EAAE,EAAE2B,EAAE,CAAC,GAAGC,GAAG,CAAC3O,IAAI,CAACmF,KAAK,CAAC,CAAA;CAClC,EAAA,IAAI,GAAGyJ,EAAE,EAAEuD,EAAE,CAAC,GAAGtD,GAAG,CAAC7O,IAAI,CAAC2O,GAAG,EAAE,CAACF,EAAE,EAAE1B,EAAE,EAAE2B,EAAE,CAAC,CAAC,CAAA;CAC5C,EAAA,IAAI,CAACI,EAAE,EAAE9B,EAAE,EAAE+B,EAAE,CAAC,GAAGJ,GAAG,CAAC3O,IAAI,CAACqO,MAAM,CAAC,CAAA;CACnC,EAAA,IAAIW,EAAE,GAAGH,GAAG,CAAC7O,IAAI,CAAC2O,GAAG,EAAE,CAACG,EAAE,EAAE9B,EAAE,EAAE+B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;;CAEvC;CACA;CACA;CACA;CACA;;CAEA;CACA;CACA;;GAEA,IAAIH,EAAE,GAAG,CAAC,EAAE;CACXA,IAAAA,EAAE,GAAG,CAAC,CAAA;CACP,GAAA;GACA,IAAII,EAAE,GAAG,CAAC,EAAE;CACXA,IAAAA,EAAE,GAAG,CAAC,CAAA;CACP,GAAA;;CAEA;;CAEA;CACA;CACA,EAAA,IAAIU,EAAE,GAAGjB,EAAE,GAAGK,EAAE,CAAA;CAChB,EAAA,IAAIa,EAAE,GAAGf,EAAE,GAAGI,EAAE,CAAA;CAEhB,EAAA,IAAIsC,EAAE,GAAGvE,EAAE,GAAGC,EAAE,CAAA;CAChB,EAAA,IAAIuE,EAAE,GAAG7C,EAAE,GAAGK,EAAE,CAAA;;CAEhB;;CAEA,EAAA,IAAIqD,EAAE,GAAId,EAAE,IAAI,CAAC,GAAKC,EAAE,IAAI,CAAE,GAAI5B,EAAE,IAAI,CAAE,CAAA;CAC1C;CACA;CACA;;CAEA;CACA;;CAEA;CACA;CACA;CACA;;CAEA;;CAEA;CACA;;CAEA;CACA,EAAA,IAAIW,EAAE,GAAG,KAAK,CAAC;GACf,IAAI7B,EAAE,IAAI,EAAE,EAAE;CAAE;KACf6B,EAAE,GAAI,QAAQ,GAAG7B,EAAE,IAAK,CAAC,GAAG,OAAO,GAAGA,EAAE,CAAC,CAAA;CAC1C,GAAA;;CAEA;CACA,EAAA,IAAI8B,EAAE,GAAK,MAAM,GAAG3B,EAAE,IAAK,CAAC,GAAG,MAAM,GAAGA,EAAE,CAAC,GAAI,KAAK,CAAA;;CAEpD;CACA,EAAA,IAAI4B,CAAC,CAAA;CACL,EAAA,IAAIxT,MAAM,CAACC,KAAK,CAACkV,EAAE,CAAC,EAAE;CACrBA,IAAAA,EAAE,GAAG,CAAC,CAAA;CACP,GAAA;CAEA,EAAA,IAAIA,EAAE,IAAI,GAAG,IAAIA,EAAE,IAAI,GAAG,EAAE;KAC3B3B,CAAC,GAAG,IAAI,GAAGlT,IAAI,CAACE,GAAG,CAAC,GAAG,GAAGF,IAAI,CAACsQ,GAAG,CAAC,CAACuE,EAAE,GAAG,GAAG,IAAIlE,GAAG,CAAC,CAAC,CAAA;CACtD,GAAC,MACI;KACJuC,CAAC,GAAG,IAAI,GAAGlT,IAAI,CAACE,GAAG,CAAC,GAAG,GAAGF,IAAI,CAACsQ,GAAG,CAAC,CAACuE,EAAE,GAAG,EAAE,IAAIlE,GAAG,CAAC,CAAC,CAAA;CACrD,GAAA;CACA;;CAEA;GACA,IAAIoE,EAAE,GAAG/U,IAAI,CAACmP,GAAG,CAACmC,EAAE,EAAE,CAAC,CAAC,CAAA;CACxB,EAAA,IAAI0D,CAAC,GAAGhV,IAAI,CAACgQ,IAAI,CAAC+E,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;GACnC,IAAI5B,EAAE,GAAGF,EAAE,IAAK+B,CAAC,GAAG9B,CAAC,GAAI,CAAC,GAAG8B,CAAC,CAAC,CAAA;;CAE/B;GACA,IAAIzB,EAAE,GAAG,CAACnB,EAAE,IAAIvD,CAAC,GAAGmE,EAAE,CAAC,KAAK,CAAC,CAAA;GAC7BO,EAAE,IAAI,CAAClB,EAAE,IAAI7T,CAAC,GAAGyU,EAAE,CAAC,KAAK,CAAC,CAAA;CAC1BM,EAAAA,EAAE,IAAKuB,EAAE,GAAI3B,EAAE,IAAI,CAAG,CAAA;CACtB;CACA,EAAA,OAAOnT,IAAI,CAACgQ,IAAI,CAACuD,EAAE,CAAC,CAAA;CACpB;CACD;;CC9GA,MAAM0B,IAAE,GAAG,GAAG,CAAC;;AAEf,mBAAe,IAAI5M,UAAU,CAAC;CAC9B;CACA;CACA;CACA;CACCrB,EAAAA,EAAE,EAAE,aAAa;CACjBsC,EAAAA,KAAK,EAAE,eAAe;CACtBvH,EAAAA,IAAI,EAAE,kBAAkB;CACxB8E,EAAAA,MAAM,EAAE;CACP9I,IAAAA,CAAC,EAAE;CACFyJ,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;CACrBzF,MAAAA,IAAI,EAAE,IAAA;MACN;CACDwL,IAAAA,CAAC,EAAE;CACF/F,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACpBzF,MAAAA,IAAI,EAAE,IAAA;MACN;CACDyL,IAAAA,CAAC,EAAE;CACFhG,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;CACtBzF,MAAAA,IAAI,EAAE,IAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAEqK,OAAO;GACb3D,QAAQA,CAAE/D,GAAG,EAAE;CACd;CACA;CACA;CACA,IAAA,OAAOA,GAAG,CAACtI,GAAG,CAAEoX,CAAC,IAAIlV,IAAI,CAACqD,GAAG,CAAC6R,CAAC,GAAGD,IAAE,EAAE,CAAC,CAAC,CAAC,CAAA;IACzC;GACD7K,MAAMA,CAAE+K,MAAM,EAAE;CACf;CACA,IAAA,OAAOA,MAAM,CAACrX,GAAG,CAACoX,CAAC,IAAIlV,IAAI,CAACqD,GAAG,CAAC6R,CAAC,GAAGD,IAAE,EAAE,CAAC,CAAC,CAAC,CAAA;CAC5C,GAAA;CACD,CAAC,CAAC;;CCnCF,MAAMpH,GAAC,GAAG,IAAI,CAAA;CACd,MAAMD,CAAC,GAAG,IAAI,CAAA;CACd,MAAMxO,GAAC,GAAG,IAAI,GAAI,CAAC,IAAI,EAAG,CAAA;CAC1B,MAAMgW,MAAI,GAAI,CAAC,IAAI,EAAE,GAAI,IAAI,CAAA;CAC7B,MAAMV,IAAE,GAAG,IAAI,GAAI,CAAC,IAAI,EAAG,CAAA;CAC3B,MAAMC,IAAE,GAAG,IAAI,GAAI,CAAC,IAAI,CAAE,CAAA;CAC1B,MAAMU,IAAE,GAAG,IAAI,GAAI,CAAC,IAAI,CAAE,CAAA;CAC1B,MAAMrX,CAAC,GAAG,GAAG,GAAG,IAAI,GAAI,CAAC,IAAI,CAAE,CAAA;CAC/B,MAAMsX,IAAI,GAAI,CAAC,IAAI,CAAC,IAAK,GAAG,GAAG,IAAI,CAAC,CAAA;CACpC,MAAM1R,CAAC,GAAG,CAAC,IAAI,CAAA;CACf,MAAM2R,EAAE,GAAG,sBAAsB,CAAA;CAEjC,MAAMC,WAAW,GAAG,CACnB,CAAG,UAAU,EAAE,QAAQ,EAAG,SAAS,CAAE,EACrC,CAAE,CAAC,SAAS,EAAG,QAAQ,EAAG,SAAS,CAAE,EACrC,CAAE,CAAC,SAAS,EAAG,QAAQ,EAAG,SAAS,CAAE,CACrC,CAAA;CACD;CACA,MAAMC,WAAW,GAAG,CACnB,CAAG,kBAAkB,EAAG,CAAC,kBAAkB,EAAG,iBAAiB,CAAI,EACnE,CAAG,mBAAmB,EAAG,kBAAkB,EAAE,CAAC,mBAAmB,CAAE,EACnE,CAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAG,kBAAkB,CAAG,CACnE,CAAA;CACD,MAAMC,WAAW,GAAG,CACnB,CAAG,GAAG,EAAQ,GAAG,EAAQ,CAAC,CAAS,EACnC,CAAG,QAAQ,EAAE,CAAC,QAAQ,EAAG,QAAQ,CAAE,EACnC,CAAG,QAAQ,EAAG,QAAQ,EAAE,CAAC,QAAQ,CAAE,CACnC,CAAA;CACD;CACA,MAAMC,WAAW,GAAG,CACnB,CAAE,CAAC,EAAoB,kBAAkB,EAAI,mBAAmB,CAAE,EAClE,CAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAG,CAAC,mBAAmB,CAAE,EAClE,CAAE,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,CAAG,CAClE,CAAA;AAED,cAAe,IAAItN,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,QAAQ;CACZjF,EAAAA,IAAI,EAAE,QAAQ;CACd8E,EAAAA,MAAM,EAAE;CACP+O,IAAAA,EAAE,EAAE;CACHpO,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAChBzF,MAAAA,IAAI,EAAE,IAAA;MACN;CACD8T,IAAAA,EAAE,EAAE;CACHrO,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;MACpB;CACDsO,IAAAA,EAAE,EAAE;CACHtO,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;CACrB,KAAA;IACA;CAED/D,EAAAA,IAAI,EAAEsS,WAAW;GACjB5L,QAAQA,CAAE/D,GAAG,EAAE;CACd;CACA;CACA;CACA;;KAEA,IAAI,CAAE4P,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAE,GAAG9P,GAAG,CAAA;;CAExB;KACA,IAAI+P,EAAE,GAAItI,GAAC,GAAGmI,EAAE,GAAK,CAACnI,GAAC,GAAG,CAAC,IAAIqI,EAAG,CAAA;KAClC,IAAIE,EAAE,GAAIxI,CAAC,GAAGqI,EAAE,GAAK,CAACrI,CAAC,GAAG,CAAC,IAAIoI,EAAG,CAAA;;CAElC;CACA,IAAA,IAAIpC,GAAG,GAAGrW,gBAAgB,CAACiY,WAAW,EAAE,CAAEW,EAAE,EAAEC,EAAE,EAAEF,EAAE,CAAE,CAAC,CAAA;;CAEvD;KACA,IAAIG,KAAK,GAAGzC,GAAG,CAAC9V,GAAG,CAAE,UAAUsF,GAAG,EAAE;OACnC,IAAIkT,GAAG,GAAG5B,IAAE,GAAIC,IAAE,GAAI,CAACvR,GAAG,GAAG,KAAK,KAAKhE,GAAG,CAAA;OAC1C,IAAImX,KAAK,GAAG,CAAC,GAAIlB,IAAE,GAAI,CAACjS,GAAG,GAAG,KAAK,KAAKhE,GAAG,CAAA;CAE3C,MAAA,OAAO,CAACkX,GAAG,GAAGC,KAAK,KAAMvY,CAAC,CAAA;CAC3B,KAAC,CAAC,CAAA;;CAEF;CACA,IAAA,IAAI,CAAEwY,EAAE,EAAEX,EAAE,EAAEC,EAAE,CAAC,GAAGvY,gBAAgB,CAACmY,WAAW,EAAEW,KAAK,CAAC,CAAA;CACxD;;CAEA,IAAA,IAAII,EAAE,GAAI,CAAC,CAAC,GAAG7S,CAAC,IAAI4S,EAAE,IAAK,CAAC,GAAI5S,CAAC,GAAG4S,EAAG,CAAC,GAAGjB,EAAE,CAAA;CAC7C,IAAA,OAAO,CAACkB,EAAE,EAAEZ,EAAE,EAAEC,EAAE,CAAC,CAAA;IACnB;GACD1L,MAAMA,CAAEsM,MAAM,EAAE;KACf,IAAI,CAACD,EAAE,EAAEZ,EAAE,EAAEC,EAAE,CAAC,GAAGY,MAAM,CAAA;CACzB,IAAA,IAAIF,EAAE,GAAG,CAACC,EAAE,GAAGlB,EAAE,KAAK,CAAC,GAAG3R,CAAC,GAAGA,CAAC,IAAI6S,EAAE,GAAGlB,EAAE,CAAC,CAAC,CAAA;;CAE5C;CACA,IAAA,IAAIc,KAAK,GAAG9Y,gBAAgB,CAACoY,WAAW,EAAE,CAAEa,EAAE,EAAEX,EAAE,EAAEC,EAAE,CAAE,CAAC,CAAA;;CAEzD;KACA,IAAIlC,GAAG,GAAGyC,KAAK,CAACvY,GAAG,CAAC,UAAUsF,GAAG,EAAE;CAClC,MAAA,IAAIkT,GAAG,GAAI5B,IAAE,GAAItR,GAAG,IAAIkS,IAAM,CAAA;OAC9B,IAAIiB,KAAK,GAAIlB,IAAE,GAAIjS,GAAG,IAAIkS,IAAK,GAAIX,IAAE,CAAA;OACrC,IAAI5W,CAAC,GAAG,KAAK,GAAI,CAACuY,GAAG,GAAGC,KAAK,KAAKnB,MAAK,CAAA;OAEvC,OAAQrX,CAAC,CAAE;CACZ,KAAC,CAAC,CAAA;;CAEF;CACA,IAAA,IAAI,CAAEoY,EAAE,EAAEC,EAAE,EAAEF,EAAE,CAAE,GAAG3Y,gBAAgB,CAACkY,WAAW,EAAE7B,GAAG,CAAC,CAAA;;CAEvD;CACA,IAAA,IAAIoC,EAAE,GAAG,CAACG,EAAE,GAAI,CAACtI,GAAC,GAAG,CAAC,IAAIqI,EAAG,IAAIrI,GAAC,CAAA;CAClC,IAAA,IAAIoI,EAAE,GAAG,CAACG,EAAE,GAAI,CAACxI,CAAC,GAAG,CAAC,IAAIoI,EAAG,IAAIpI,CAAC,CAAA;CAClC,IAAA,OAAO,CAAEoI,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAE,CAAA;IACrB;CAED3M,EAAAA,OAAO,EAAE;CACR;CACA,IAAA,OAAO,EAAE;CACR1C,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAA;CACrG,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;ACjHF,cAAe,IAAIwB,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,QAAQ;CACZjF,EAAAA,IAAI,EAAE,QAAQ;CACd8E,EAAAA,MAAM,EAAE;CACP+O,IAAAA,EAAE,EAAE;CACHpO,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAChBzF,MAAAA,IAAI,EAAE,IAAA;MACN;CACD4U,IAAAA,EAAE,EAAE;CACHnP,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAChBzF,MAAAA,IAAI,EAAE,QAAA;MACN;CACD6U,IAAAA,EAAE,EAAE;CACHpP,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAEiT,MAAM;GACZvM,QAAQA,CAAE0M,MAAM,EAAE;CACjB;KACA,IAAI,CAACJ,EAAE,EAAEZ,EAAE,EAAEC,EAAE,CAAC,GAAGe,MAAM,CAAA;CACzB,IAAA,IAAI/G,GAAG,CAAA;CACP,IAAA,MAAMlG,CAAC,GAAG,MAAM,CAAC;;CAEjB,IAAA,IAAI5J,IAAI,CAACE,GAAG,CAAC2V,EAAE,CAAC,GAAGjM,CAAC,IAAI5J,IAAI,CAACE,GAAG,CAAC4V,EAAE,CAAC,GAAGlM,CAAC,EAAE;CACzCkG,MAAAA,GAAG,GAAGpO,GAAG,CAAA;CACV,KAAC,MACI;CACJoO,MAAAA,GAAG,GAAG9P,IAAI,CAAC+P,KAAK,CAAC+F,EAAE,EAAED,EAAE,CAAC,GAAG,GAAG,GAAG7V,IAAI,CAACS,EAAE,CAAA;CACzC,KAAA;CAEA,IAAA,OAAO,CACNgW,EAAE;CAAE;KACJzW,IAAI,CAACgQ,IAAI,CAAC6F,EAAE,IAAI,CAAC,GAAGC,EAAE,IAAI,CAAC,CAAC;CAAE;KAC9B7F,SAAc,CAACH,GAAG,CAAC;MACnB,CAAA;IACD;GACD1F,MAAMA,CAAE0M,MAAM,EAAE;CACf;CACA;CACA,IAAA,OAAO,CACNA,MAAM,CAAC,CAAC,CAAC;CAAE;CACXA,IAAAA,MAAM,CAAC,CAAC,CAAC,GAAG9W,IAAI,CAACsQ,GAAG,CAACwG,MAAM,CAAC,CAAC,CAAC,GAAG9W,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;CAAE;CACjDqW,IAAAA,MAAM,CAAC,CAAC,CAAC,GAAG9W,IAAI,CAACuQ,GAAG,CAACuG,MAAM,CAAC,CAAC,CAAC,GAAG9W,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;MAC/C,CAAA;CACF,GAAA;CACD,CAAC,CAAC;;CCjDF;CACA;;CAEA;CACA;CACA;;CAEe,iBAAUoH,EAAAA,KAAK,EAAEkJ,MAAM,EAAE;CACvC,EAAA,CAAClJ,KAAK,EAAEkJ,MAAM,CAAC,GAAGrH,QAAQ,CAAC,CAAC7B,KAAK,EAAEkJ,MAAM,CAAC,CAAC,CAAA;;CAE3C;CACA;CACA;CACA,EAAA,IAAI,CAACgG,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC,GAAGH,MAAM,CAACpU,IAAI,CAACmF,KAAK,CAAC,CAAA;CACxC,EAAA,IAAI,CAACqP,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC,GAAGN,MAAM,CAACpU,IAAI,CAACqO,MAAM,CAAC,CAAA;;CAEzC;CACA;CACA,EAAA,IAAIsG,EAAE,GAAGN,GAAG,GAAGG,GAAG,CAAA;CAClB,EAAA,IAAI7E,EAAE,GAAG2E,GAAG,GAAGG,GAAG,CAAA;;CAElB;CACA,EAAA,IAAKzX,MAAM,CAACC,KAAK,CAACsX,GAAG,CAAC,IAAMvX,MAAM,CAACC,KAAK,CAACyX,GAAG,CAAE,EAAE;CAC/C;CACAH,IAAAA,GAAG,GAAG,CAAC,CAAA;CACPG,IAAAA,GAAG,GAAG,CAAC,CAAA;IACP,MACI,IAAI1X,MAAM,CAACC,KAAK,CAACsX,GAAG,CAAC,EAAE;CAC3B;CACAA,IAAAA,GAAG,GAAGG,GAAG,CAAA;IACT,MACI,IAAI1X,MAAM,CAACC,KAAK,CAACyX,GAAG,CAAC,EAAE;CAC3BA,IAAAA,GAAG,GAAGH,GAAG,CAAA;CACV,GAAA;CAEA,EAAA,IAAIxE,EAAE,GAAGwE,GAAG,GAAGG,GAAG,CAAA;GAClB,IAAI1E,EAAE,GAAG,CAAC,GAAG1S,IAAI,CAACgQ,IAAI,CAACgH,GAAG,GAAGG,GAAG,CAAC,GAAGnX,IAAI,CAACuQ,GAAG,CAAEkC,EAAE,GAAG,CAAC,IAAKzS,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC,CAAC,CAAA;CAExE,EAAA,OAAOT,IAAI,CAACgQ,IAAI,CAACqH,EAAE,IAAI,CAAC,GAAGhF,EAAE,IAAI,CAAC,GAAGK,EAAE,IAAI,CAAC,CAAC,CAAA;CAC9C;;CCtCA,MAAMgC,IAAE,GAAG,IAAI,GAAG,IAAI,CAAA;CACtB,MAAMC,IAAE,GAAG,IAAI,GAAG,GAAG,CAAA;CACrB,MAAMU,IAAE,GAAG,IAAI,GAAG,GAAG,CAAA;CACrB,MAAMiC,IAAE,GAAG,IAAI,GAAG,KAAK,CAAA;CACvB,MAAMC,EAAE,GAAG,IAAI,GAAG,EAAE,CAAA;CACpB,MAAMC,GAAG,GAAG,KAAK,GAAG,IAAI,CAAA;CACxB,MAAMC,GAAG,GAAG,EAAE,GAAG,IAAI,CAAA;;CAErB;CACA;CACA,MAAMjE,UAAU,GAAG,CAClB,CAAG,kBAAkB,EAAG,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EACjE,CAAE,CAAC,kBAAkB,EAAG,kBAAkB,EAAG,kBAAkB,CAAE,EACjE,CAAG,kBAAkB,EAAG,kBAAkB,EAAG,kBAAkB,CAAE,CACjE,CAAA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAMkE,UAAU,GAAG,CAClB,CAAG,IAAI,GAAG,IAAI,EAAI,IAAI,GAAG,IAAI,EAAQ,CAAC,CAAO,EAC7C,CAAG,IAAI,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,EAAG,IAAI,GAAG,IAAI,CAAE,EAC7C,CAAE,KAAK,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,EAAG,CAAC,GAAG,GAAG,IAAI,CAAE,CAC7C,CAAA;;CAED;CACA,MAAMC,UAAU,GAAG,CAClB,CAAE,kBAAkB,EAAG,kBAAkB,EAAG,kBAAkB,CAAE,EAChE,CAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EAChE,CAAE,kBAAkB,EAAG,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,CAChE,CAAA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA,MAAMlE,UAAU,GAAG,CAClB,CAAG,kBAAkB,EAAE,CAAC,kBAAkB,EAAG,kBAAkB,CAAE,EACjE,CAAG,kBAAkB,EAAG,kBAAkB,EAAE,CAAC,kBAAkB,CAAE,EACjE,CAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAG,kBAAkB,CAAE,CACjE,CAAA;;CAED;CACA;CACA;CACA;CACA;CACA;CACA;AACA,aAAe,IAAIpL,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,OAAO;CACXjF,EAAAA,IAAI,EAAE,OAAO;CACb;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA8E,EAAAA,MAAM,EAAE;CACP1I,IAAAA,CAAC,EAAE;CACFqJ,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAAE;CAClBzF,MAAAA,IAAI,EAAE,GAAA;MACN;CACD6V,IAAAA,EAAE,EAAE;CACHpQ,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CAAE;CACvBzF,MAAAA,IAAI,EAAE,IAAA;MACN;CACD8V,IAAAA,EAAE,EAAE;CACHrQ,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;CACrBzF,MAAAA,IAAI,EAAE,IAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAEsS,WAAW;GACjB5L,QAAQA,CAAE/D,GAAG,EAAE;CACd;CACA,IAAA,IAAIwN,GAAG,GAAGrW,gBAAgB,CAACiW,UAAU,EAAEpN,GAAG,CAAC,CAAA;KAE3C,OAAO0R,UAAU,CAAClE,GAAG,CAAC,CAAA;IACtB;GACDxJ,MAAMA,CAAE2N,KAAK,EAAE;CACd,IAAA,IAAInE,GAAG,GAAGoE,UAAU,CAACD,KAAK,CAAC,CAAA;CAE3B,IAAA,OAAOxa,gBAAgB,CAACkW,UAAU,EAAEG,GAAG,CAAC,CAAA;CACzC,GAAA;CACD,CAAC,CAAC,CAAA;CAEF,SAASkE,UAAUA,CAAElE,GAAG,EAAE;CACzB;CACA;GACA,IAAIyC,KAAK,GAAGzC,GAAG,CAAC9V,GAAG,CAAE,UAAUsF,GAAG,EAAE;KACnC,IAAIkT,GAAG,GAAG5B,IAAE,GAAIC,IAAE,GAAI,CAACvR,GAAG,GAAG,KAAK,KAAKkU,IAAI,CAAA;KAC3C,IAAIf,KAAK,GAAG,CAAC,GAAIlB,IAAE,GAAI,CAACjS,GAAG,GAAG,KAAK,KAAKkU,IAAI,CAAA;CAE5C,IAAA,OAAO,CAAChB,GAAG,GAAGC,KAAK,KAAMgB,EAAE,CAAA;CAC5B,GAAC,CAAC,CAAA;;CAEF;CACA,EAAA,OAAOha,gBAAgB,CAACma,UAAU,EAAErB,KAAK,CAAC,CAAA;CAC3C,CAAA;CAEA,SAAS2B,UAAUA,CAAED,KAAK,EAAE;CAC3B,EAAA,IAAI1B,KAAK,GAAG9Y,gBAAgB,CAACoa,UAAU,EAAEI,KAAK,CAAC,CAAA;;CAE/C;GACA,IAAInE,GAAG,GAAGyC,KAAK,CAACvY,GAAG,CAAE,UAAUsF,GAAG,EAAE;CACnC,IAAA,IAAIkT,GAAG,GAAItW,IAAI,CAACqD,GAAG,CAAED,GAAG,IAAIqU,GAAG,GAAI/C,IAAE,EAAE,CAAC,CAAC,CAAA;KACzC,IAAI6B,KAAK,GAAI5B,IAAE,GAAIU,IAAE,GAAIjS,GAAG,IAAIqU,GAAM,CAAA;CACtC,IAAA,OAAO,KAAK,GAAI,CAACnB,GAAG,GAAGC,KAAK,KAAKiB,GAAI,CAAA;CACtC,GAAC,CAAC,CAAA;CAEF,EAAA,OAAO5D,GAAG,CAAA;CACX;;CCjIA;CACA;CACA;;CAEe,kBAAU/L,EAAAA,KAAK,EAAEkJ,MAAM,EAAE;CACvC,EAAA,CAAClJ,KAAK,EAAEkJ,MAAM,CAAC,GAAGrH,QAAQ,CAAC,CAAC7B,KAAK,EAAEkJ,MAAM,CAAC,CAAC,CAAA;;CAE3C;CACA;CACA;CACA;;CAEA,EAAA,IAAI,CAAEkH,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAE,GAAGC,KAAK,CAAC1V,IAAI,CAACmF,KAAK,CAAC,CAAA;CACtC,EAAA,IAAI,CAAEwQ,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAE,GAAGH,KAAK,CAAC1V,IAAI,CAACqO,MAAM,CAAC,CAAA;;CAEvC;CACA;CACA;;CAEA,EAAA,OAAO,GAAG,GAAG/Q,IAAI,CAACgQ,IAAI,CAAC,CAACiI,EAAE,GAAGI,EAAE,KAAK,CAAC,GAAI,IAAI,GAAG,CAACH,EAAE,GAAGI,EAAE,KAAK,CAAE,GAAG,CAACH,EAAE,GAAGI,EAAE,KAAK,CAAC,CAAC,CAAA;CAClF;;CCjBA,MAAMlO,OAAK,GAAGxE,MAAM,CAACE,GAAG,CAAA;CACxB,MAAMyS,WAAW,GAAG,IAAI,CAAA;CACxB,MAAMC,cAAc,GAAG,CAAC,GAAGD,WAAW,CAAA;CACtC,MAAME,GAAG,GAAG,CAAC,GAAG1Y,IAAI,CAACS,EAAE,CAAA;CAEvB,MAAMkY,KAAK,GAAG,CACb,CAAG,QAAQ,EAAG,QAAQ,EAAE,CAAC,QAAQ,CAAE,EACnC,CAAE,CAAC,QAAQ,EAAG,QAAQ,EAAG,QAAQ,CAAE,EACnC,CAAE,CAAC,QAAQ,EAAG,QAAQ,EAAG,QAAQ,CAAE,CACnC,CAAA;CAED,MAAMC,QAAQ,GAAG,CAChB,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,EAC9D,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,oBAAoB,CAAC,EAChE,CAAC,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CACjE,CAAA;CAED,MAAMtB,EAAE,GAAG,CACV,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACrB,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EACvB,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CACxB,CAAA;CAED,MAAMuB,WAAW,GAAG;CACnBC,EAAAA,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC;CACvBC,EAAAA,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;CACrBC,EAAAA,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAA;CACrB,CAAC,CAAA;CAED,MAAMC,UAAU,GAAG;CAClB;GACArJ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;GACzCsJ,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;GAC5BC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAA;CACpC,CAAC,CAAA;CAED,MAAMC,OAAO,GAAG,GAAG,GAAGpZ,IAAI,CAACS,EAAE,CAAA;CAC7B,MAAM4Y,SAAO,GAAGrZ,IAAI,CAACS,EAAE,GAAG,GAAG,CAAA;CAEtB,SAASwF,OAAKA,CAAEY,MAAM,EAAEyS,EAAE,EAAE;CAClC,EAAA,MAAMC,IAAI,GAAG1S,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAI;CAC5B,IAAA,MAAMT,CAAC,GAAGyF,IAAI,CAAC8V,EAAE,GAAGtZ,IAAI,CAACE,GAAG,CAAC1B,CAAC,CAAC,GAAG,IAAI,EAAEga,WAAW,CAAC,CAAA;CACpD,IAAA,OAAO,GAAG,GAAGlV,QAAQ,CAACvF,CAAC,EAAES,CAAC,CAAC,IAAIT,CAAC,GAAG,KAAK,CAAC,CAAA;CAC1C,GAAC,CAAC,CAAA;CACF,EAAA,OAAOwb,IAAI,CAAA;CACZ,CAAA;CAEO,SAASC,OAAOA,CAAEC,OAAO,EAAEH,EAAE,EAAE;GACrC,MAAMI,QAAQ,GAAG,GAAG,GAAGJ,EAAE,GAAI,KAAK,IAAIb,cAAe,CAAA;CACrD,EAAA,OAAOgB,OAAO,CAAC3b,GAAG,CAACU,CAAC,IAAI;CACvB,IAAA,MAAMmb,IAAI,GAAG3Z,IAAI,CAACE,GAAG,CAAC1B,CAAC,CAAC,CAAA;CACxB,IAAA,OAAO8E,QAAQ,CAACoW,QAAQ,GAAGlW,IAAI,CAACmW,IAAI,IAAI,GAAG,GAAGA,IAAI,CAAC,EAAElB,cAAc,CAAC,EAAEja,CAAC,CAAC,CAAA;CACzE,GAAC,CAAC,CAAA;CACH,CAAA;CAEO,SAASob,aAAaA,CAAEhK,CAAC,EAAE;CACjC,EAAA,IAAIiK,EAAE,GAAGzK,SAAS,CAACQ,CAAC,CAAC,CAAA;GACrB,IAAIiK,EAAE,IAAIZ,UAAU,CAACrJ,CAAC,CAAC,CAAC,CAAC,EAAE;CAC1BiK,IAAAA,EAAE,IAAI,GAAG,CAAA;CACV,GAAA;GAEA,MAAM1b,CAAC,GAAG0F,UAAU,CAACoV,UAAU,CAACrJ,CAAC,EAAEiK,EAAE,CAAC,GAAG,CAAC,CAAA;CAC1C,EAAA,MAAM,CAAC5V,EAAE,EAAE6V,GAAG,CAAC,GAAGb,UAAU,CAACrJ,CAAC,CAACpO,KAAK,CAACrD,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAAA;CAC9C,EAAA,MAAM,CAAC4b,EAAE,EAAEC,GAAG,CAAC,GAAGf,UAAU,CAACC,CAAC,CAAC1X,KAAK,CAACrD,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAAA;CAC9C,EAAA,MAAM8b,EAAE,GAAGhB,UAAU,CAACE,CAAC,CAAChb,CAAC,CAAC,CAAA;CAE1B,EAAA,MAAM+b,CAAC,GAAG,CAACL,EAAE,GAAG5V,EAAE,IAAI8V,EAAE,CAAA;CACxB,EAAA,OAAOE,EAAE,GAAI,GAAG,GAAGC,CAAC,IAAKA,CAAC,GAAG,CAACJ,GAAG,GAAGD,EAAE,IAAIG,GAAG,CAAC,CAAA;CAC/C,CAAA;CAEO,SAASG,gBAAgBA,CAAEhB,CAAC,EAAE;GACpC,IAAIiB,EAAE,GAAI,CAACjB,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAI,CAAA;GAChC,MAAMhb,CAAC,GAAG6B,IAAI,CAACI,KAAK,CAAC,IAAI,GAAGga,EAAE,CAAC,CAAA;GAC/BA,EAAE,GAAGA,EAAE,GAAG,GAAG,CAAA;CACb,EAAA,MAAM,CAACnW,EAAE,EAAE6V,GAAG,CAAC,GAAGb,UAAU,CAACrJ,CAAC,CAACpO,KAAK,CAACrD,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAAA;CAC9C,EAAA,MAAM,CAAC4b,EAAE,EAAEC,GAAG,CAAC,GAAGf,UAAU,CAACC,CAAC,CAAC1X,KAAK,CAACrD,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAAA;CAE9C,EAAA,OAAOiR,SAAS,CACf,CAACgL,EAAE,IAAIJ,GAAG,GAAG/V,EAAE,GAAG8V,EAAE,GAAGD,GAAG,CAAC,GAAG,GAAG,GAAG7V,EAAE,GAAG+V,GAAG,KAC3CI,EAAE,IAAIJ,GAAG,GAAGD,EAAE,CAAC,GAAG,GAAG,GAAGC,GAAG,CAC7B,CAAC,CAAA;CACF,CAAA;CAEO,SAASK,WAAWA,CAC1BC,QAAQ,EACRC,iBAAiB,EACjBC,mBAAmB,EACnBC,QAAQ,EACRC,WAAW,EACV;GAED,MAAMjW,GAAG,GAAG,EAAE,CAAA;GAEdA,GAAG,CAACiW,WAAW,GAAGA,WAAW,CAAA;GAC7BjW,GAAG,CAAC6V,QAAQ,GAAGA,QAAQ,CAAA;GACvB7V,GAAG,CAACgW,QAAQ,GAAGA,QAAQ,CAAA;CACvB,EAAA,MAAME,IAAI,GAAGL,QAAQ,CAACxc,GAAG,CAACU,CAAC,IAAI;KAC9B,OAAOA,CAAC,GAAG,GAAG,CAAA;CACf,GAAC,CAAC,CAAA;;CAEF;GACAiG,GAAG,CAACmW,EAAE,GAAGL,iBAAiB,CAAA;CAC1B;GACA9V,GAAG,CAACoW,EAAE,GAAGL,mBAAmB,CAAA;CAC5B;CACA,EAAA,MAAMM,EAAE,GAAGH,IAAI,CAAC,CAAC,CAAC,CAAA;;CAElB;CACA,EAAA,MAAMI,IAAI,GAAGxd,gBAAgB,CAACob,KAAK,EAAEgC,IAAI,CAAC,CAAA;;CAE1C;CACAF,EAAAA,QAAQ,GAAG5B,WAAW,CAACpU,GAAG,CAACgW,QAAQ,CAAC,CAAA;CACpC,EAAA,MAAMzL,CAAC,GAAGyL,QAAQ,CAAC,CAAC,CAAC,CAAA;CACrBhW,EAAAA,GAAG,CAACjG,CAAC,GAAGic,QAAQ,CAAC,CAAC,CAAC,CAAA;CACnBhW,EAAAA,GAAG,CAACuW,EAAE,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAAA;GAEpB,MAAMQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGxW,GAAG,CAACmW,EAAE,GAAG,CAAC,CAAC,CAAA;CAC9B,EAAA,MAAMM,EAAE,GAAGD,CAAC,IAAI,CAAC,CAAA;;CAEjB;CACAxW,EAAAA,GAAG,CAAC6U,EAAE,GAAI4B,EAAE,GAAGzW,GAAG,CAACmW,EAAE,GAAG,GAAG,IAAI,CAAC,GAAGM,EAAE,CAAC,IAAI,CAAC,GAAGA,EAAE,CAAC,GAAGlb,IAAI,CAACiP,IAAI,CAAC,CAAC,GAAGxK,GAAG,CAACmW,EAAE,CAAE,CAAA;CAC1EnW,EAAAA,GAAG,CAAC0W,MAAM,GAAG1W,GAAG,CAAC6U,EAAE,IAAI,IAAI,CAAA;CAE3B7U,EAAAA,GAAG,CAACrF,CAAC,GAAGqF,GAAG,CAACoW,EAAE,GAAGC,EAAE,CAAA;CACnBrW,EAAAA,GAAG,CAAC+I,CAAC,GAAG,IAAI,GAAGxN,IAAI,CAACgQ,IAAI,CAACvL,GAAG,CAACrF,CAAC,CAAC,CAAA;GAC/BqF,GAAG,CAAC2W,GAAG,GAAG,KAAK,GAAI3W,GAAG,CAACrF,CAAC,IAAI,CAAC,GAAI,CAAA;CACjCqF,EAAAA,GAAG,CAAC4W,GAAG,GAAG5W,GAAG,CAAC2W,GAAG,CAAA;;CAEjB;CACA;CACA,EAAA,MAAMxX,CAAC,GAAI8W,WAAW,GACrB,CAAC,GACD1a,IAAI,CAACqD,GAAG,CACPrD,IAAI,CAACmD,GAAG,CAAC6L,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGhP,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAACe,GAAG,CAACmW,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9D,CACD,CAAC,CAAA;GACFnW,GAAG,CAAC6W,IAAI,GAAGP,IAAI,CAACjd,GAAG,CAACU,CAAC,IAAI;KACxB,OAAO4D,WAAW,CAAC,CAAC,EAAE0Y,EAAE,GAAGtc,CAAC,EAAEoF,CAAC,CAAC,CAAA;CACjC,GAAC,CAAC,CAAA;GACFa,GAAG,CAAC8W,OAAO,GAAG9W,GAAG,CAAC6W,IAAI,CAACxd,GAAG,CAACU,CAAC,IAAI;KAC/B,OAAO,CAAC,GAAGA,CAAC,CAAA;CACb,GAAC,CAAC,CAAA;;CAEF;GACA,MAAMgd,KAAK,GAAGT,IAAI,CAACjd,GAAG,CAAC,CAACU,CAAC,EAAEL,CAAC,KAAK;CAChC,IAAA,OAAOK,CAAC,GAAGiG,GAAG,CAAC6W,IAAI,CAACnd,CAAC,CAAC,CAAA;CACvB,GAAC,CAAC,CAAA;GACF,MAAMsd,KAAK,GAAGxV,OAAK,CAACuV,KAAK,EAAE/W,GAAG,CAAC6U,EAAE,CAAC,CAAA;GAClC7U,GAAG,CAACiX,EAAE,GAAGjX,GAAG,CAAC2W,GAAG,IAAI,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;;CAE9D;;CAEA,EAAA,OAAOhX,GAAG,CAAA;CACX,CAAA;;CAEA;CACA,MAAMkX,mBAAiB,GAAGtB,WAAW,CACpChQ,OAAK,EACL,EAAE,GAAGrK,IAAI,CAACS,EAAE,GAAG,GAAG,EAAE,EAAE,EACtB,SAAS,EACT,KACD,CAAC,CAAA;CAEM,SAASmb,SAASA,CAAEC,KAAK,EAAEpX,GAAG,EAAE;CAEtC;CACA;CACA,EAAA,IAAI,EAAGoX,KAAK,CAACC,CAAC,KAAK9X,SAAS,GAAK6X,KAAK,CAACE,CAAC,KAAK/X,SAAU,CAAC,EAAE;CACzD,IAAA,MAAM,IAAI8H,KAAK,CAAC,kDAAkD,CAAC,CAAA;CACpE,GAAA;CAEA,EAAA,IAAI,EAAG+P,KAAK,CAACG,CAAC,KAAKhY,SAAS,GAAK6X,KAAK,CAACtV,CAAC,KAAKvC,SAAU,GAAI6X,KAAK,CAAC5O,CAAC,KAAKjJ,SAAU,CAAC,EAAE;CACnF,IAAA,MAAM,IAAI8H,KAAK,CAAC,uDAAuD,CAAC,CAAA;CACzE,GAAA;;CAEA;CACA,EAAA,IAAI,EAAG+P,KAAK,CAACjM,CAAC,KAAK5L,SAAS,GAAK6X,KAAK,CAAC1C,CAAC,KAAKnV,SAAU,CAAC,EAAE;CACzD,IAAA,MAAM,IAAI8H,KAAK,CAAC,kDAAkD,CAAC,CAAA;CACpE,GAAA;;CAEA;GACA,IAAI+P,KAAK,CAACC,CAAC,KAAK,GAAG,IAAID,KAAK,CAACE,CAAC,KAAK,GAAG,EAAE;CACvC,IAAA,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;CACvB,GAAA;;CAEA;GACA,IAAIE,IAAI,GAAG,GAAG,CAAA;CACd,EAAA,IAAIJ,KAAK,CAACjM,CAAC,KAAK5L,SAAS,EAAE;KAC1BiY,IAAI,GAAG7M,SAAS,CAACyM,KAAK,CAACjM,CAAC,CAAC,GAAGyJ,SAAO,CAAA;CACpC,GAAC,MACI;KACJ4C,IAAI,GAAG9B,gBAAgB,CAAC0B,KAAK,CAAC1C,CAAC,CAAC,GAAGE,SAAO,CAAA;CAC3C,GAAA;CAEA,EAAA,MAAM6C,IAAI,GAAGlc,IAAI,CAACsQ,GAAG,CAAC2L,IAAI,CAAC,CAAA;CAC3B,EAAA,MAAME,IAAI,GAAGnc,IAAI,CAACuQ,GAAG,CAAC0L,IAAI,CAAC,CAAA;;CAE3B;GACA,IAAIG,KAAK,GAAG,GAAG,CAAA;CACf,EAAA,IAAIP,KAAK,CAACC,CAAC,KAAK9X,SAAS,EAAE;CAC1BoY,IAAAA,KAAK,GAAG5Y,IAAI,CAACqY,KAAK,CAACC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAA;CACnC,GAAC,MACI,IAAID,KAAK,CAACE,CAAC,KAAK/X,SAAS,EAAE;KAC/BoY,KAAK,GAAG,IAAI,GAAG3X,GAAG,CAACjG,CAAC,GAAGqd,KAAK,CAACE,CAAC,IAAI,CAACtX,GAAG,CAACiX,EAAE,GAAG,CAAC,IAAIjX,GAAG,CAAC0W,MAAM,CAAC,CAAA;CAC7D,GAAA;;CAEA;GACA,IAAIvZ,KAAK,GAAG,GAAG,CAAA;CACf,EAAA,IAAIia,KAAK,CAACG,CAAC,KAAKhY,SAAS,EAAE;CAC1BpC,IAAAA,KAAK,GAAGia,KAAK,CAACG,CAAC,GAAGI,KAAK,CAAA;CACxB,GAAC,MACI,IAAIP,KAAK,CAACtV,CAAC,KAAKvC,SAAS,EAAE;KAC/BpC,KAAK,GAAIia,KAAK,CAACtV,CAAC,GAAG9B,GAAG,CAAC0W,MAAM,GAAIiB,KAAK,CAAA;CACvC,GAAC,MACI,IAAIP,KAAK,CAAC5O,CAAC,KAAKjJ,SAAS,EAAE;CAC/BpC,IAAAA,KAAK,GAAG,MAAM,GAAIia,KAAK,CAAC5O,CAAC,IAAI,CAAE,IAAIxI,GAAG,CAACiX,EAAE,GAAG,CAAC,CAAC,GAAGjX,GAAG,CAACjG,CAAC,CAAA;CACvD,GAAA;CACA,EAAA,MAAM0b,CAAC,GAAG1W,IAAI,CACb5B,KAAK,GAAG5B,IAAI,CAACmP,GAAG,CAAC,IAAI,GAAGnP,IAAI,CAACmP,GAAG,CAAC,IAAI,EAAE1K,GAAG,CAACrF,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EACrD,EAAE,GAAG,CACN,CAAC,CAAA;;CAED;CACA,EAAA,MAAMid,EAAE,GAAG,IAAI,IAAIrc,IAAI,CAACsQ,GAAG,CAAC2L,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA;;CAE5C;CACA,EAAA,MAAMze,CAAC,GAAGiH,GAAG,CAACiX,EAAE,GAAGlY,IAAI,CAAC4Y,KAAK,EAAE,CAAC,GAAG3X,GAAG,CAACjG,CAAC,GAAGiG,GAAG,CAAC+I,CAAC,CAAC,CAAA;;CAEjD;CACA,EAAA,MAAM8O,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG7X,GAAG,CAACuW,EAAE,GAAGvW,GAAG,CAAC4W,GAAG,GAAGgB,EAAE,CAAA;CAC3C,EAAA,MAAME,EAAE,GAAG/e,CAAC,GAAGiH,GAAG,CAAC2W,GAAG,CAAA;GACtB,MAAMzN,CAAC,GACN,EAAE,IAAI4O,EAAE,GAAG,KAAK,CAAC,GACjB5Y,IAAI,CAACuW,CAAC,EAAE,EAAE,GAAGoC,EAAE,GAAGpC,CAAC,IAAI,EAAE,GAAGgC,IAAI,GAAG,GAAG,GAAGC,IAAI,CAAC,CAC9C,CAAA;CACD,EAAA,MAAMrN,CAAC,GAAGnB,CAAC,GAAGuO,IAAI,CAAA;CAClB,EAAA,MAAMrO,CAAC,GAAGF,CAAC,GAAGwO,IAAI,CAAA;;CAElB;GACA,MAAMK,KAAK,GAAGhD,OAAO,CACpBjc,gBAAgB,CAAC+Z,EAAE,EAAE,CAACiF,EAAE,EAAEzN,CAAC,EAAEjB,CAAC,CAAC,CAAC,CAAC/P,GAAG,CAACU,CAAC,IAAI;CACzC,IAAA,OAAOA,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;CACpB,GAAC,CAAC,EACFiG,GAAG,CAAC6U,EACL,CAAC,CAAA;CACD,EAAA,OAAO/b,gBAAgB,CACtBqb,QAAQ,EACR4D,KAAK,CAAC1e,GAAG,CAAC,CAACU,CAAC,EAAEL,CAAC,KAAK;CACnB,IAAA,OAAOK,CAAC,GAAGiG,GAAG,CAAC8W,OAAO,CAACpd,CAAC,CAAC,CAAA;CAC1B,GAAC,CACF,CAAC,CAACL,GAAG,CAACU,CAAC,IAAI;KACV,OAAOA,CAAC,GAAG,GAAG,CAAA;CACf,GAAC,CAAC,CAAA;CACH,CAAA;CAGO,SAASie,OAAOA,CAAEC,MAAM,EAAEjY,GAAG,EAAE;CACrC;CACA,EAAA,MAAMkY,MAAM,GAAGD,MAAM,CAAC5e,GAAG,CAACU,CAAC,IAAI;KAC9B,OAAOA,CAAC,GAAG,GAAG,CAAA;CACf,GAAC,CAAC,CAAA;CACF,EAAA,MAAMoe,IAAI,GAAG3W,OAAK,CACjB1I,gBAAgB,CAACob,KAAK,EAAEgE,MAAM,CAAC,CAAC7e,GAAG,CAAC,CAACU,CAAC,EAAEL,CAAC,KAAK;CAC7C,IAAA,OAAOK,CAAC,GAAGiG,GAAG,CAAC6W,IAAI,CAACnd,CAAC,CAAC,CAAA;CACvB,GAAC,CAAC,EACFsG,GAAG,CAAC6U,EACL,CAAC,CAAA;;CAED;GACA,MAAMxK,CAAC,GAAG8N,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;GAClD,MAAM/O,CAAC,GAAG,CAAC+O,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;CAC/C,EAAA,MAAMX,IAAI,GAAG,CAAEjc,IAAI,CAAC+P,KAAK,CAAClC,CAAC,EAAEiB,CAAC,CAAC,GAAG4J,GAAG,GAAIA,GAAG,IAAIA,GAAG,CAAA;;CAEnD;CACA,EAAA,MAAM2D,EAAE,GAAG,IAAI,IAAIrc,IAAI,CAACsQ,GAAG,CAAC2L,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA;GAE5C,MAAM/B,CAAC,GACN,GAAG,GAAG,EAAE,GAAGzV,GAAG,CAACuW,EAAE,GAAGvW,GAAG,CAAC4W,GAAG,GAC3B1X,IAAI,CACH0Y,EAAE,GAAGrc,IAAI,CAACgQ,IAAI,CAAClB,CAAC,IAAI,CAAC,GAAGjB,CAAC,IAAI,CAAC,CAAC,EAC/B+O,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,KACtC,CACA,CAAA;GACD,MAAMhb,KAAK,GAAG4B,IAAI,CAAC0W,CAAC,EAAE,GAAG,CAAC,GAAGla,IAAI,CAACmP,GAAG,CAAC,IAAI,GAAGnP,IAAI,CAACmP,GAAG,CAAC,IAAI,EAAE1K,GAAG,CAACrF,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;;CAEzE;GACA,MAAM5B,CAAC,GAAGiH,GAAG,CAAC2W,GAAG,IAAI,CAAC,GAAGwB,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;CAE5D,EAAA,MAAMR,KAAK,GAAG5Y,IAAI,CAAChG,CAAC,GAAGiH,GAAG,CAACiX,EAAE,EAAE,GAAG,GAAGjX,GAAG,CAACjG,CAAC,GAAGiG,GAAG,CAAC+I,CAAC,CAAC,CAAA;;CAEnD;GACA,MAAMsO,CAAC,GAAG,GAAG,GAAGtY,IAAI,CAAC4Y,KAAK,EAAE,CAAC,CAAC,CAAA;;CAE9B;CACA,EAAA,MAAML,CAAC,GAAI,CAAC,GAAGtX,GAAG,CAACjG,CAAC,GAAG4d,KAAK,IAAI3X,GAAG,CAACiX,EAAE,GAAG,CAAC,CAAC,GAAGjX,GAAG,CAAC0W,MAAO,CAAA;;CAEzD;CACA,EAAA,MAAMa,CAAC,GAAGpa,KAAK,GAAGwa,KAAK,CAAA;;CAEvB;CACA,EAAA,MAAM7V,CAAC,GAAGyV,CAAC,GAAGvX,GAAG,CAAC0W,MAAM,CAAA;;CAExB;CACA,EAAA,MAAMvL,CAAC,GAAGR,SAAS,CAAC6M,IAAI,GAAG7C,OAAO,CAAC,CAAA;;CAEnC;CACA,EAAA,MAAMD,CAAC,GAAGS,aAAa,CAAChK,CAAC,CAAC,CAAA;;CAE1B;GACA,MAAM3C,CAAC,GAAG,EAAE,GAAGzJ,IAAI,CAACiB,GAAG,CAACjG,CAAC,GAAGoD,KAAK,IAAI6C,GAAG,CAACiX,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;;CAExD;;GAEA,OAAO;CAACI,IAAAA,CAAC,EAAEA,CAAC;CAAEE,IAAAA,CAAC,EAAEA,CAAC;CAAEpM,IAAAA,CAAC,EAAEA,CAAC;CAAE3C,IAAAA,CAAC,EAAEA,CAAC;CAAE8O,IAAAA,CAAC,EAAEA,CAAC;CAAExV,IAAAA,CAAC,EAAEA,CAAC;CAAE4S,IAAAA,CAAC,EAAEA,CAAAA;IAAE,CAAA;CAClD,CAAA;;CAGA;CACA;CACA;CACA;CACA;CACA;AACA,aAAe,IAAI9Q,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,WAAW;CACfsC,EAAAA,KAAK,EAAE,aAAa;CACpBvH,EAAAA,IAAI,EAAE,WAAW;CACjB8E,EAAAA,MAAM,EAAE;CACPgW,IAAAA,CAAC,EAAE;CACFrV,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,GAAA;MACN;CACDrE,IAAAA,CAAC,EAAE;CACF8J,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACpBzF,MAAAA,IAAI,EAAE,cAAA;MACN;CACD6N,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAEqZ,OAAO;GAEb3S,QAAQA,CAAEiE,GAAG,EAAE;CACd,IAAA,MAAMyN,KAAK,GAAGY,OAAO,CAACrO,GAAG,EAAEuN,mBAAiB,CAAC,CAAA;CAC7C,IAAA,OAAO,CAACE,KAAK,CAACC,CAAC,EAAED,KAAK,CAACtV,CAAC,EAAEsV,KAAK,CAACjM,CAAC,CAAC,CAAA;IAClC;GACDxF,MAAMA,CAAEyR,KAAK,EAAE;CACd,IAAA,OAAOD,SAAS,CACf;CAACE,MAAAA,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC;CAAEtV,MAAAA,CAAC,EAAEsV,KAAK,CAAC,CAAC,CAAC;OAAEjM,CAAC,EAAEiM,KAAK,CAAC,CAAC,CAAA;MAAE,EACvCF,mBACD,CAAC,CAAA;CACF,GAAA;CACD,CAAC,CAAC;;CCnWF,MAAMtR,OAAK,GAAGxE,MAAM,CAACE,GAAG,CAAA;CACxB,MAAM6D,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAMgF,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;;CAErB,SAASmO,OAAOA,CAAExP,CAAC,EAAE;CACpB;;GAEA,MAAMyP,EAAE,GAAIzP,CAAC,GAAG3D,GAAC,GAAI5J,IAAI,CAACiP,IAAI,CAAC1B,CAAC,CAAC,GAAG,CAACqB,GAAC,GAAGrB,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;CACtD,EAAA,OAAQ,KAAK,GAAGyP,EAAE,GAAI,IAAI,CAAA;CAC3B,CAAA;CAEA,SAASC,SAASA,CAAEC,KAAK,EAAE;CAC1B;;GAEA,OAAQA,KAAK,GAAG,CAAC,GAAKld,IAAI,CAACmP,GAAG,CAAC,CAAC+N,KAAK,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAGA,KAAK,GAAGtO,GAAC,CAAA;CAClE,CAAA;CAEA,SAASuO,OAAOA,CAAEtW,MAAM,EAAEpC,GAAG,EAAE;CAC9B;CACA;CACA;CACA;CACA;CACA;CACA;;GAEA,IAAI,CAACmL,CAAC,EAAEpR,CAAC,EAAE0b,CAAC,CAAC,GAAGrT,MAAM,CAAA;GACtB,IAAIuH,GAAG,GAAG,EAAE,CAAA;GACZ,IAAIyO,CAAC,GAAG,CAAC,CAAA;;CAET;GACA,IAAI3C,CAAC,KAAK,CAAC,EAAE;CACZ,IAAA,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;CACvB,GAAA;;CAEA;CACA,EAAA,IAAI3M,CAAC,GAAG0P,SAAS,CAAC/C,CAAC,CAAC,CAAA;;CAEpB;CACA;GACA,IAAIA,CAAC,GAAG,CAAC,EAAE;KACV2C,CAAC,GAAG,mBAAmB,GAAG3C,CAAC,IAAI,CAAC,GAAG,iBAAiB,GAAGA,CAAC,GAAG,kBAAkB,CAAA;CAC9E,GAAC,MACI;KACJ2C,CAAC,GAAG,qBAAqB,GAAG3C,CAAC,IAAI,CAAC,GAAG,mBAAmB,GAAGA,CAAC,GAAG,kBAAkB,CAAA;CAClF,GAAA;;CAEA;CACA;CACA;CACA;CACA;GACA,MAAMkD,SAAS,GAAG,KAAK,CAAA;GACvB,MAAMC,YAAY,GAAG,EAAE,CAAA;GAEvB,IAAIC,OAAO,GAAG,CAAC,CAAA;GACf,IAAIpb,IAAI,GAAGqb,QAAQ,CAAA;;CAGnB;GACA,OAAOD,OAAO,IAAID,YAAY,EAAE;KAC/BjP,GAAG,GAAGwN,SAAS,CAAC;CAACE,MAAAA,CAAC,EAAEe,CAAC;CAAEb,MAAAA,CAAC,EAAExd,CAAC;CAAEoR,MAAAA,CAAC,EAAEA,CAAAA;MAAE,EAAEnL,GAAG,CAAC,CAAA;;CAExC;CACA;CACA,IAAA,MAAM+Y,KAAK,GAAGxd,IAAI,CAACE,GAAG,CAACkO,GAAG,CAAC,CAAC,CAAC,GAAGb,CAAC,CAAC,CAAA;KAClC,IAAIiQ,KAAK,GAAGtb,IAAI,EAAE;OACjB,IAAIsb,KAAK,IAAIJ,SAAS,EAAE;CACvB,QAAA,OAAOhP,GAAG,CAAA;CACX,OAAA;CAEAlM,MAAAA,IAAI,GAAGsb,KAAK,CAAA;CACb,KAAA;;CAEA;CACA;CACA;CACA;CACA;CACAX,IAAAA,CAAC,GAAGA,CAAC,GAAG,CAACzO,GAAG,CAAC,CAAC,CAAC,GAAGb,CAAC,IAAIsP,CAAC,IAAI,CAAC,GAAGzO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;CAEvCkP,IAAAA,OAAO,IAAI,CAAC,CAAA;CACb,GAAA;;CAEA;CACA;CACA,EAAA,OAAO1B,SAAS,CAAC;CAACE,IAAAA,CAAC,EAAEe,CAAC;CAAEb,IAAAA,CAAC,EAAExd,CAAC;CAAEoR,IAAAA,CAAC,EAAEA,CAAAA;IAAE,EAAEnL,GAAG,CAAC,CAAA;CAC1C,CAAA;CAEA,SAASgZ,KAAKA,CAAErP,GAAG,EAAE3J,GAAG,EAAE;CACzB;;GAEA,MAAMyV,CAAC,GAAG6C,OAAO,CAAC3O,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;GACzB,IAAI8L,CAAC,KAAK,GAAG,EAAE;CACd,IAAA,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;CACvB,GAAA;CACA,EAAA,MAAM2B,KAAK,GAAGY,OAAO,CAACrO,GAAG,EAAEuN,iBAAiB,CAAC,CAAA;CAC7C,EAAA,OAAO,CAACvM,SAAS,CAACyM,KAAK,CAACjM,CAAC,CAAC,EAAEiM,KAAK,CAACG,CAAC,EAAE9B,CAAC,CAAC,CAAA;CACxC,CAAA;;CAEA;CACO,MAAMyB,iBAAiB,GAAGtB,WAAW,CAC3ChQ,OAAK,EAAE,GAAG,GAAGrK,IAAI,CAACS,EAAE,GAAGwc,SAAS,CAAC,IAAI,CAAC,EACtCA,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,EACrB,SAAS,EACT,KACD,CAAC,CAAA;;CAED;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA,WAAe,IAAI5U,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACP+I,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;MACN;CACDvD,IAAAA,CAAC,EAAE;CACFgJ,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,cAAA;MACN;CACDmY,IAAAA,CAAC,EAAE;CACF1S,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,MAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAEqZ,OAAO;GAEb3S,QAAQA,CAAEiE,GAAG,EAAE;CACd,IAAA,OAAOqP,KAAK,CAACrP,GAAsB,CAAC,CAAA;IACpC;GACDhE,MAAMA,CAAEsT,GAAG,EAAE;CACZ,IAAA,OAAOP,OAAO,CAACO,GAAG,EAAE/B,iBAAiB,CAAC,CAAA;IACtC;CACDpS,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNb,MAAAA,EAAE,EAAE,OAAO;CACXH,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCvJF,MAAMwS,OAAO,GAAGrZ,IAAI,CAACS,EAAE,GAAG,GAAG,CAAA;CAC7B,MAAMkd,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;;CAEtC;CACA;CACA;CACA;CACA;CACA,SAASC,YAAYA,CAAE/W,MAAM,EAAE;CAC9B;CACA;CACA;CACA;CACA;CACA,EAAA,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;KAClBA,MAAM,GAAG6W,GAAG,CAACvT,QAAQ,CAACuT,GAAG,CAACtT,MAAM,CAACvD,MAAM,CAAC,CAAC,CAAA;CAC1C,GAAA;;CAEA;CACA;CACA;CACA,EAAA,MAAMN,CAAC,GAAGvG,IAAI,CAAC6d,GAAG,CAAC7d,IAAI,CAACqD,GAAG,CAAC,CAAC,GAAGsa,QAAQ,CAAC,CAAC,CAAC,GAAG9W,MAAM,CAAC,CAAC,CAAC,GAAG8U,iBAAiB,CAACR,MAAM,EAAE,GAAG,CAAC,CAAC,GAAGwC,QAAQ,CAAC,CAAC,CAAC,CAAA;CACvG,EAAA,MAAMG,IAAI,GAAGjX,MAAM,CAAC,CAAC,CAAC,GAAGwS,OAAO,CAAA;GAChC,MAAMvK,CAAC,GAAGvI,CAAC,GAAGvG,IAAI,CAACsQ,GAAG,CAACwN,IAAI,CAAC,CAAA;GAC5B,MAAMjQ,CAAC,GAAGtH,CAAC,GAAGvG,IAAI,CAACuQ,GAAG,CAACuN,IAAI,CAAC,CAAA;GAE5B,OAAO,CAACjX,MAAM,CAAC,CAAC,CAAC,EAAEiI,CAAC,EAAEjB,CAAC,CAAC,CAAA;CACzB,CAAA;;CAGA;CACA;CACA;CACA;CACA;CACA;CACe,kBAAUhG,EAAAA,KAAK,EAAEkJ,MAAM,EAAE;CACvC,EAAA,CAAClJ,KAAK,EAAEkJ,MAAM,CAAC,GAAGrH,QAAQ,CAAC,CAAC7B,KAAK,EAAEkJ,MAAM,CAAC,CAAC,CAAA;CAE3C,EAAA,IAAI,CAAEgN,EAAE,EAAEtO,EAAE,EAAE2B,EAAE,CAAE,GAAGwM,YAAY,CAACF,GAAG,CAAChb,IAAI,CAACmF,KAAK,CAAC,CAAC,CAAA;CAClD,EAAA,IAAI,CAAEmW,EAAE,EAAEtO,EAAE,EAAE+B,EAAE,CAAE,GAAGmM,YAAY,CAACF,GAAG,CAAChb,IAAI,CAACqO,MAAM,CAAC,CAAC,CAAA;;CAEnD;CACA;GACA,OAAO/Q,IAAI,CAACgQ,IAAI,CAAC,CAAC+N,EAAE,GAAGC,EAAE,KAAK,CAAC,GAAG,CAACvO,EAAE,GAAGC,EAAE,KAAK,CAAC,GAAG,CAAC0B,EAAE,GAAGK,EAAE,KAAK,CAAC,CAAC,CAAA;CACnE;;AChCA,qBAAe;GACdmD,QAAQ;GACRqJ,SAAS;GACTC,UAAU;GACVC,QAAQ;GACRC,SAAS;GACTC,QAAQ;CACRC,EAAAA,SAAAA;CACD,CAAC;;CCXD;CACA;CACA;CACA;CACA;CACA,SAASC,WAAWA,CAAEC,GAAG,EAAE;CAC1B;;GAEA,MAAMC,KAAK,GAAI,CAACD,GAAG,GAAI,CAAC,GAAGxe,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACse,GAAG,CAAC,CAAC,CAAC,CAAA;CAChE;CACA,EAAA,OAAOxe,IAAI,CAACqD,GAAG,CAACqb,UAAU,CAAE,CAAA,EAAA,EAAID,KAAK,GAAG,CAAE,CAAA,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;CACpD,CAAA;CAEA,MAAME,UAAU,GAAG;CAClB,EAAA,KAAK,EAAE;CACNC,IAAAA,MAAM,EAAE,OAAO;CACfJ,IAAAA,GAAG,EAAE,CAAC;CACNK,IAAAA,YAAY,EAAE,KAAK;CACnBC,IAAAA,eAAe,EAAE,EAAC;IAClB;CACD,EAAA,WAAW,EAAE;CACZF,IAAAA,MAAM,EAAE,OAAO;CACfJ,IAAAA,GAAG,EAAE,CAAC;CACNK,IAAAA,YAAY,EAAE,KAAK;CACnBC,IAAAA,eAAe,EAAE;CAAEC,MAAAA,OAAO,EAAE,OAAO;CAAE5b,MAAAA,GAAG,EAAE,CAAC;CAAEE,MAAAA,GAAG,EAAE,GAAA;CAAI,KAAA;CACvD,GAAA;CACD,CAAC,CAAA;;CAED;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAEe,SAAS2b,OAAOA,CAC9BnX,KAAK,EAQJ;GAAA,IAPD;KACC+W,MAAM,GAAG5V,QAAQ,CAACpE,aAAa;CAC/B+B,IAAAA,KAAK,GAAG3C,SAAS;CACjB6a,IAAAA,YAAY,GAAG,EAAE;CACjBL,IAAAA,GAAG,GAAG,CAAC;CACPM,IAAAA,eAAe,GAAG,EAAC;CACpB,GAAC,GAAA/a,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CAEN8D,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;GAEvB,IAAIJ,QAAa,CAAC1D,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;CAChC4C,IAAAA,KAAK,GAAG5C,SAAS,CAAC,CAAC,CAAC,CAAA;CACrB,GAAC,MACI,IAAI,CAAC4C,KAAK,EAAE;KAChBA,KAAK,GAAGkB,KAAK,CAAClB,KAAK,CAAA;CACpB,GAAA;CAEAA,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;;CAE7B;CACA;CACA;CACA;;CAEA,EAAA,IAAI+D,OAAO,CAAC7C,KAAK,EAAElB,KAAK,EAAE;CAAEuE,IAAAA,OAAO,EAAE,CAAA;CAAE,GAAC,CAAC,EAAE;CAC1C,IAAA,OAAOrD,KAAK,CAAA;CACb,GAAA;CAEA,EAAA,IAAIoX,UAAU,CAAA;GACd,IAAIL,MAAM,KAAK,KAAK,EAAE;CACrBK,IAAAA,UAAU,GAAGC,UAAU,CAACrX,KAAK,EAAE;CAAElB,MAAAA,KAAAA;CAAM,KAAC,CAAC,CAAA;CAC1C,GAAC,MACI;KACJ,IAAIiY,MAAM,KAAK,MAAM,IAAI,CAAClU,OAAO,CAAC7C,KAAK,EAAElB,KAAK,CAAC,EAAE;CAEhD,MAAA,IAAI9H,MAAM,CAACC,SAAS,CAACqgB,cAAc,CAACngB,IAAI,CAAC2f,UAAU,EAAEC,MAAM,CAAC,EAAE;SAC7D,CAAC;WAACA,MAAM;WAAEJ,GAAG;WAAEK,YAAY;CAAEC,UAAAA,eAAAA;CAAe,SAAC,GAAGH,UAAU,CAACC,MAAM,CAAC,EAAA;CACnE,OAAA;;CAEA;OACA,IAAIQ,EAAE,GAAGlB,UAAU,CAAA;OACnB,IAAIW,YAAY,KAAK,EAAE,EAAE;CACxB,QAAA,KAAK,IAAInhB,CAAC,IAAI2hB,aAAa,EAAE;CAC5B,UAAA,IAAI,QAAQ,GAAGR,YAAY,CAAC3f,WAAW,EAAE,KAAKxB,CAAC,CAACwB,WAAW,EAAE,EAAE;CAC9DkgB,YAAAA,EAAE,GAAGC,aAAa,CAAC3hB,CAAC,CAAC,CAAA;CACrB,YAAA,MAAA;CACD,WAAA;CACD,SAAA;CACD,OAAA;OAEA,IAAI4hB,OAAO,GAAGN,OAAO,CAACrc,EAAE,CAACkF,KAAK,EAAElB,KAAK,CAAC,EAAE;CAAEiY,QAAAA,MAAM,EAAE,MAAM;CAAEjY,QAAAA,KAAAA;CAAM,OAAC,CAAC,CAAA;OAClE,IAAIyY,EAAE,CAACvX,KAAK,EAAEyX,OAAO,CAAC,GAAGd,GAAG,EAAE;CAE7B;SACA,IAAI3f,MAAM,CAACgK,IAAI,CAACiW,eAAe,CAAC,CAACnhB,MAAM,KAAK,CAAC,EAAE;WAC9C,IAAI4hB,WAAW,GAAGlX,UAAU,CAACkE,YAAY,CAACuS,eAAe,CAACC,OAAO,CAAC,CAAA;CAClE,UAAA,IAAIA,OAAO,GAAGpV,GAAG,CAAChH,EAAE,CAACkF,KAAK,EAAE0X,WAAW,CAAC5Y,KAAK,CAAC,EAAE4Y,WAAW,CAACvY,EAAE,CAAC,CAAA;CAC/D,UAAA,IAAIS,MAAW,CAACsX,OAAO,CAAC,EAAE;CACzBA,YAAAA,OAAO,GAAG,CAAC,CAAA;CACZ,WAAA;CACA,UAAA,IAAIA,OAAO,IAAID,eAAe,CAACzb,GAAG,EAAE;CACnC,YAAA,OAAOV,EAAE,CAAC;CAAEgE,cAAAA,KAAK,EAAE,SAAS;eAAEE,MAAM,EAAEhB,MAAM,CAAC,KAAK,CAAA;CAAE,aAAC,EAAEgC,KAAK,CAAClB,KAAK,CAAC,CAAA;CACpE,WAAC,MACI,IAAIoY,OAAO,IAAID,eAAe,CAAC3b,GAAG,EAAE;CACxC,YAAA,OAAOR,EAAE,CAAC;CAAEgE,cAAAA,KAAK,EAAE,SAAS;CAAEE,cAAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;CAAE,aAAC,EAAEgB,KAAK,CAAClB,KAAK,CAAC,CAAA;CAChE,WAAA;CACD,SAAA;;CAEA;CACA,QAAA,IAAIM,SAAS,GAAGoB,UAAU,CAACkE,YAAY,CAACqS,MAAM,CAAC,CAAA;CAC/C,QAAA,IAAIY,QAAQ,GAAGvY,SAAS,CAACN,KAAK,CAAA;CAC9B,QAAA,IAAIgG,OAAO,GAAG1F,SAAS,CAACD,EAAE,CAAA;CAE1B,QAAA,IAAIyY,WAAW,GAAG9c,EAAE,CAACkF,KAAK,EAAE2X,QAAQ,CAAC,CAAA;CACrC;SACAC,WAAW,CAAC5Y,MAAM,CAACtC,OAAO,CAAC,CAAC/F,CAAC,EAAEL,CAAC,KAAK;CACpC,UAAA,IAAIsJ,MAAW,CAACjJ,CAAC,CAAC,EAAE;CACnBihB,YAAAA,WAAW,CAAC5Y,MAAM,CAAC1I,CAAC,CAAC,GAAG,CAAC,CAAA;CAC1B,WAAA;CACD,SAAC,CAAC,CAAA;SACF,IAAIuhB,MAAM,GAAGzY,SAAS,CAACjE,KAAK,IAAIiE,SAAS,CAACO,QAAQ,CAAA;CAClD,QAAA,IAAIrE,GAAG,GAAGuc,MAAM,CAAC,CAAC,CAAC,CAAA;CACnB,QAAA,IAAI9V,CAAC,GAAG2U,WAAW,CAACC,GAAG,CAAC,CAAA;SACxB,IAAImB,GAAG,GAAGxc,GAAG,CAAA;CACb,QAAA,IAAIyc,IAAI,GAAGjW,GAAG,CAAC8V,WAAW,EAAE9S,OAAO,CAAC,CAAA;CAEpC,QAAA,OAAOiT,IAAI,GAAGD,GAAG,GAAG/V,CAAC,EAAE;CACtB,UAAA,IAAI0V,OAAO,GAAGpL,KAAK,CAACuL,WAAW,CAAC,CAAA;CAChCH,UAAAA,OAAO,GAAGN,OAAO,CAACM,OAAO,EAAE;aAAE3Y,KAAK;CAAEiY,YAAAA,MAAM,EAAE,MAAA;CAAO,WAAC,CAAC,CAAA;CACrD,UAAA,IAAI/Z,MAAM,GAAGua,EAAE,CAACK,WAAW,EAAEH,OAAO,CAAC,CAAA;CAErC,UAAA,IAAIza,MAAM,GAAG2Z,GAAG,GAAG5U,CAAC,EAAE;CACrB+V,YAAAA,GAAG,GAAGhW,GAAG,CAAC8V,WAAW,EAAE9S,OAAO,CAAC,CAAA;CAChC,WAAC,MACI;CACJiT,YAAAA,IAAI,GAAGjW,GAAG,CAAC8V,WAAW,EAAE9S,OAAO,CAAC,CAAA;CACjC,WAAA;WAEA8B,GAAG,CAACgR,WAAW,EAAE9S,OAAO,EAAE,CAACgT,GAAG,GAAGC,IAAI,IAAI,CAAC,CAAC,CAAA;CAC5C,SAAA;CAEAX,QAAAA,UAAU,GAAGtc,EAAE,CAAC8c,WAAW,EAAE9Y,KAAK,CAAC,CAAA;CACpC,OAAC,MACI;CACJsY,QAAAA,UAAU,GAAGK,OAAO,CAAA;CACrB,OAAA;CACD,KAAC,MACI;CACJL,MAAAA,UAAU,GAAGtc,EAAE,CAACkF,KAAK,EAAElB,KAAK,CAAC,CAAA;CAC9B,KAAA;KAEA,IAAIiY,MAAM,KAAK,MAAM;CACpB;CAAA,OACG,CAAClU,OAAO,CAACuU,UAAU,EAAEtY,KAAK,EAAE;CAAEuE,MAAAA,OAAO,EAAE,CAAA;CAAE,KAAC,CAAC,EAC7C;OACD,IAAIwU,MAAM,GAAG7gB,MAAM,CAACuM,MAAM,CAACzE,KAAK,CAACE,MAAM,CAAC,CAAC/I,GAAG,CAACU,CAAC,IAAIA,CAAC,CAACwE,KAAK,IAAI,EAAE,CAAC,CAAA;CAEhEic,MAAAA,UAAU,CAACpY,MAAM,GAAGoY,UAAU,CAACpY,MAAM,CAAC/I,GAAG,CAAC,CAACU,CAAC,EAAEL,CAAC,KAAK;SACnD,IAAI,CAACgF,GAAG,EAAEE,GAAG,CAAC,GAAGqc,MAAM,CAACvhB,CAAC,CAAC,CAAA;SAE1B,IAAIgF,GAAG,KAAKa,SAAS,EAAE;WACtBxF,CAAC,GAAGwB,IAAI,CAACqD,GAAG,CAACF,GAAG,EAAE3E,CAAC,CAAC,CAAA;CACrB,SAAA;SAEA,IAAI6E,GAAG,KAAKW,SAAS,EAAE;WACtBxF,CAAC,GAAGwB,IAAI,CAACmD,GAAG,CAAC3E,CAAC,EAAE6E,GAAG,CAAC,CAAA;CACrB,SAAA;CAEA,QAAA,OAAO7E,CAAC,CAAA;CACT,OAAC,CAAC,CAAA;CACH,KAAA;CACD,GAAA;CAEA,EAAA,IAAImI,KAAK,KAAKkB,KAAK,CAAClB,KAAK,EAAE;KAC1BsY,UAAU,GAAGtc,EAAE,CAACsc,UAAU,EAAEpX,KAAK,CAAClB,KAAK,CAAC,CAAA;CACzC,GAAA;CAEAkB,EAAAA,KAAK,CAAChB,MAAM,GAAGoY,UAAU,CAACpY,MAAM,CAAA;CAChC,EAAA,OAAOgB,KAAK,CAAA;CACb,CAAA;CAEAmX,OAAO,CAACxQ,OAAO,GAAG,OAAO,CAAA;;CAEzB;CACA;CACA;CACA,MAAMqR,MAAM,GAAG;CACdC,EAAAA,KAAK,EAAE;CAAEnZ,IAAAA,KAAK,EAAEoN,KAAK;CAAElN,IAAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;IAAG;CAC1CkZ,EAAAA,KAAK,EAAE;CAAEpZ,IAAAA,KAAK,EAAEoN,KAAK;CAAElN,IAAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;CAAE,GAAA;CAC1C,CAAC,CAAA;;CAED;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAASqY,UAAUA,CAAEc,MAAM,EAAgB;GAAA,IAAd;CAACrZ,IAAAA,KAAAA;CAAK,GAAC,GAAA5C,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;GAC/C,MAAMkc,GAAG,GAAG,IAAI,CAAA;GAChB,MAAMrW,CAAC,GAAG,MAAM,CAAA;CAEhBoW,EAAAA,MAAM,GAAGtW,QAAQ,CAACsW,MAAM,CAAC,CAAA;GAEzB,IAAI,CAACrZ,KAAK,EAAE;KACXA,KAAK,GAAGqZ,MAAM,CAACrZ,KAAK,CAAA;CACrB,GAAA;CAEAA,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAC7B,EAAA,MAAMuZ,UAAU,GAAG7X,UAAU,CAACsB,GAAG,CAAC,OAAO,CAAC,CAAA;GAE1C,IAAIhD,KAAK,CAAC8D,WAAW,EAAE;CACtB,IAAA,OAAO9H,EAAE,CAACqd,MAAM,EAAErZ,KAAK,CAAC,CAAA;CACzB,GAAA;CAEA,EAAA,MAAMwZ,YAAY,GAAGxd,EAAE,CAACqd,MAAM,EAAEE,UAAU,CAAC,CAAA;CAC3C,EAAA,IAAIrQ,CAAC,GAAGsQ,YAAY,CAACtZ,MAAM,CAAC,CAAC,CAAC,CAAA;;CAE9B;GACA,IAAIgJ,CAAC,IAAI,CAAC,EAAE;KACX,MAAMxF,KAAK,GAAG1H,EAAE,CAACkd,MAAM,CAACC,KAAK,EAAEnZ,KAAK,CAAC,CAAA;CACrC0D,IAAAA,KAAK,CAACzI,KAAK,GAAGoe,MAAM,CAACpe,KAAK,CAAA;CAC1B,IAAA,OAAOe,EAAE,CAAC0H,KAAK,EAAE1D,KAAK,CAAC,CAAA;CACxB,GAAA;GACA,IAAIkJ,CAAC,IAAI,CAAC,EAAE;KACX,MAAMuQ,KAAK,GAAGzd,EAAE,CAACkd,MAAM,CAACE,KAAK,EAAEpZ,KAAK,CAAC,CAAA;CACrCyZ,IAAAA,KAAK,CAACxe,KAAK,GAAGoe,MAAM,CAACpe,KAAK,CAAA;CAC1B,IAAA,OAAOe,EAAE,CAACyd,KAAK,EAAEzZ,KAAK,CAAC,CAAA;CACxB,GAAA;CAEA,EAAA,IAAI+D,OAAO,CAACyV,YAAY,EAAExZ,KAAK,EAAE;CAACuE,IAAAA,OAAO,EAAE,CAAA;CAAC,GAAC,CAAC,EAAE;CAC/C,IAAA,OAAOvI,EAAE,CAACwd,YAAY,EAAExZ,KAAK,CAAC,CAAA;CAC/B,GAAA;GAEA,SAAS0Z,IAAIA,CAAEC,MAAM,EAAE;CACtB,IAAA,MAAMC,SAAS,GAAG5d,EAAE,CAAC2d,MAAM,EAAE3Z,KAAK,CAAC,CAAA;KACnC,MAAM6Z,WAAW,GAAG3hB,MAAM,CAACuM,MAAM,CAACzE,KAAK,CAACE,MAAM,CAAC,CAAA;CAC/C0Z,IAAAA,SAAS,CAAC1Z,MAAM,GAAG0Z,SAAS,CAAC1Z,MAAM,CAAC/I,GAAG,CAAC,CAACwN,KAAK,EAAEsB,KAAK,KAAK;CACzD,MAAA,IAAI,OAAO,IAAI4T,WAAW,CAAC5T,KAAK,CAAC,EAAE;SAClC,MAAM,CAACzJ,GAAG,EAAEE,GAAG,CAAC,GAAImd,WAAW,CAAC5T,KAAK,CAAC,CAAC5J,KAAK,CAAA;SAC5C,OAAOyE,KAAU,CAACtE,GAAG,EAAEmI,KAAK,EAAEjI,GAAG,CAAC,CAAA;CACnC,OAAA;CACA,MAAA,OAAOiI,KAAK,CAAA;CACb,KAAC,CAAC,CAAA;CACF,IAAA,OAAOiV,SAAS,CAAA;CACjB,GAAA;GACA,IAAIpd,GAAG,GAAG,CAAC,CAAA;CACX,EAAA,IAAIE,GAAG,GAAG8c,YAAY,CAACtZ,MAAM,CAAC,CAAC,CAAC,CAAA;GAChC,IAAI4Z,WAAW,GAAG,IAAI,CAAA;CACtB,EAAA,IAAIC,OAAO,GAAGxM,KAAK,CAACiM,YAAY,CAAC,CAAA;CACjC,EAAA,IAAIb,OAAO,GAAGe,IAAI,CAACK,OAAO,CAAC,CAAA;CAE3B,EAAA,IAAIC,CAAC,GAAGtC,QAAQ,CAACiB,OAAO,EAAEoB,OAAO,CAAC,CAAA;GAClC,IAAIC,CAAC,GAAGV,GAAG,EAAE;CACZ,IAAA,OAAOX,OAAO,CAAA;CACf,GAAA;CAEA,EAAA,OAAQjc,GAAG,GAAGF,GAAG,GAAIyG,CAAC,EAAE;CACvB,IAAA,MAAMgX,MAAM,GAAG,CAACzd,GAAG,GAAGE,GAAG,IAAI,CAAC,CAAA;CAC9Bqd,IAAAA,OAAO,CAAC7Z,MAAM,CAAC,CAAC,CAAC,GAAG+Z,MAAM,CAAA;CAC1B,IAAA,IAAIH,WAAW,IAAI/V,OAAO,CAACgW,OAAO,EAAE/Z,KAAK,EAAE;CAACuE,MAAAA,OAAO,EAAE,CAAA;CAAC,KAAC,CAAC,EAAE;CACzD/H,MAAAA,GAAG,GAAGyd,MAAM,CAAA;CACb,KAAC,MACI;CACJtB,MAAAA,OAAO,GAAGe,IAAI,CAACK,OAAO,CAAC,CAAA;CACvBC,MAAAA,CAAC,GAAGtC,QAAQ,CAACiB,OAAO,EAAEoB,OAAO,CAAC,CAAA;OAC9B,IAAIC,CAAC,GAAGV,GAAG,EAAE;CACZ,QAAA,IAAKA,GAAG,GAAGU,CAAC,GAAG/W,CAAC,EAAG;CAClB,UAAA,MAAA;CACD,SAAC,MACI;CACJ6W,UAAAA,WAAW,GAAG,KAAK,CAAA;CACnBtd,UAAAA,GAAG,GAAGyd,MAAM,CAAA;CACb,SAAA;CACD,OAAC,MACI;CACJvd,QAAAA,GAAG,GAAGud,MAAM,CAAA;CACb,OAAA;CACD,KAAA;CACD,GAAA;CACA,EAAA,OAAOtB,OAAO,CAAA;CACf;;CCjTA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,SAAS3c,EAAEA,CAAEkF,KAAK,EAAElB,KAAK,EAAkB;GAAA,IAAhB;CAAC+D,IAAAA,OAAAA;CAAO,GAAC,GAAA3G,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CACvD8D,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;CACvBlB,EAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAE7B,EAAA,IAAIE,MAAM,GAAGF,KAAK,CAACjE,IAAI,CAACmF,KAAK,CAAC,CAAA;CAC9B,EAAA,IAAItJ,GAAG,GAAG;KAACoI,KAAK;KAAEE,MAAM;KAAEjF,KAAK,EAAEiG,KAAK,CAACjG,KAAAA;IAAM,CAAA;CAE7C,EAAA,IAAI8I,OAAO,EAAE;CACZnM,IAAAA,GAAG,GAAGygB,OAAO,CAACzgB,GAAG,EAAEmM,OAAO,KAAK,IAAI,GAAG1G,SAAS,GAAG0G,OAAO,CAAC,CAAA;CAC3D,GAAA;CAEA,EAAA,OAAOnM,GAAG,CAAA;CACX,CAAA;CAEAoE,EAAE,CAAC6L,OAAO,GAAG,OAAO;;;;;;;;;;ECxBpB,IAAI,WAAW,GAAG5R,kBAAA,EAAqC,CAAC;AACxD;EACA,IAAI,UAAU,GAAG,SAAS,CAAC;AAC3B;CACA,CAAA,qBAAc,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;IAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,UAAU,CAAC,yBAAyB,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;GAC9G,CAAA;;;;;;;;;ECND,IAAI,CAAC,GAAGA,cAAA,EAA8B,CAAC;EACvC,IAAI,QAAQ,GAAGC,eAAA,EAAiC,CAAC;EACjD,IAAI,iBAAiB,GAAGC,wBAAA,EAA4C,CAAC;EACrE,IAAI,cAAc,GAAGC,qBAAA,EAAwC,CAAC;EAC9D,IAAI,qBAAqB,GAAGE,4BAAA,EAAgD,CAAC;EAC7E,IAAI,wBAAwB,GAAGC,+BAAA,EAAoD,CAAC;AACpF;CACA;EACA,IAAI,gBAAgB,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC3C;CACA;EACA,IAAI,8BAA8B,GAAG,YAAY;CACjD,GAAE,IAAI;CACN;CACA,KAAI,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;KACpE,CAAC,OAAO,KAAK,EAAE;CAClB,KAAI,OAAO,KAAK,YAAY,SAAS,CAAC;KACnC;CACH,EAAC,CAAC;AACF;CACA,CAAA,IAAI,MAAM,GAAG,gBAAgB,IAAI,CAAC,8BAA8B,EAAE,CAAC;AACnE;CACA;CACA;CACA,CAAA,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;CAC9D;CACA,GAAE,OAAO,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;CAClC,KAAI,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC3B,KAAI,IAAI,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;CACnC,KAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;MAChC,IAAI,QAAQ,EAAE;CAClB,OAAM,wBAAwB,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC;CAC/C,OAAM,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,OAAO,CAAC,EAAE,EAAE;CAClB,SAAQ,IAAI,EAAE,GAAG,CAAC,GAAG,QAAQ,CAAC;CAC9B,SAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACjC,cAAa,qBAAqB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;SACnC;CACP,OAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;UACjC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SACrB;OACF,CAAC,OAAO,cAAc,CAAC,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC;KAC5C;CACH,EAAC,CAAC,CAAA;;;;;;CCpCF;CACA;CACA;CACA;CACA;CACA;CACe,SAAS2jB,SAASA,CAAEhZ,KAAK,EAKhC;GAAA,IAAAxI,IAAA,EAAAyhB,qBAAA,CAAA;GAAA,IALkC;KACzCxhB,SAAS,GAAG0J,QAAQ,CAAC1J,SAAS;CAC9BsH,IAAAA,MAAM,GAAG,SAAS;CAClB8D,aAAAA,SAAO,GAAG,IAAI;KACd,GAAGqW,aAAAA;CACJ,GAAC,GAAAhd,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CACL,EAAA,IAAIxF,GAAG,CAAA;CAEPsJ,EAAAA,KAAK,GAAG6B,QAAQ,CAAC7B,KAAK,CAAC,CAAA;GAEvB,IAAIkB,QAAQ,GAAGnC,MAAM,CAAA;CACrBA,EAAAA,MAAM,GAAAvH,CAAAA,IAAA,GAAAyhB,CAAAA,qBAAA,GAAGjZ,KAAK,CAAClB,KAAK,CAAC6B,SAAS,CAAC5B,MAAM,CAAC,cAAAka,qBAAA,KAAA,KAAA,CAAA,GAAAA,qBAAA,GAC5BjZ,KAAK,CAAClB,KAAK,CAAC6B,SAAS,CAAC,SAAS,CAAC,MAAA,IAAA,IAAAnJ,IAAA,KAAAA,KAAAA,CAAAA,GAAAA,IAAA,GAChCgJ,UAAU,CAAC2E,cAAc,CAAA;;CAEnC;CACA;CACA;;GAEA,IAAInG,MAAM,GAAGgB,KAAK,CAAChB,MAAM,CAACrF,KAAK,EAAE,CAAC;;CAElCkJ,EAAAA,SAAO,KAAPA,SAAO,GAAK9D,MAAM,CAACoY,OAAO,CAAA,CAAA;CAE1B,EAAA,IAAItU,SAAO,IAAI,CAACsW,OAAY,CAACnZ,KAAK,CAAC,EAAE;CACpC;CACAhB,IAAAA,MAAM,GAAGmY,OAAO,CAAC9K,KAAK,CAACrM,KAAK,CAAC,EAAE6C,SAAO,KAAK,IAAI,GAAG1G,SAAS,GAAG0G,SAAO,CAAC,CAAC7D,MAAM,CAAA;CAC9E,GAAA;CAEA,EAAA,IAAID,MAAM,CAACjI,IAAI,KAAK,QAAQ,EAAE;KAC7BoiB,aAAa,CAACzhB,SAAS,GAAGA,SAAS,CAAA;KAEnC,IAAIsH,MAAM,CAACia,SAAS,EAAE;CACrBtiB,MAAAA,GAAG,GAAGqI,MAAM,CAACia,SAAS,CAACha,MAAM,EAAEgB,KAAK,CAACjG,KAAK,EAAEmf,aAAa,CAAC,CAAA;CAC3D,KAAC,MACI;CACJ,MAAA,MAAM,IAAIza,SAAS,CAAE,CAASyC,OAAAA,EAAAA,QAAS,0DAAyD,CAAC,CAAA;CAClG,KAAA;CACD,GAAC,MACI;CACJ;CACA,IAAA,IAAIhH,IAAI,GAAG6E,MAAM,CAAC7E,IAAI,IAAI,OAAO,CAAA;KAEjC,IAAI6E,MAAM,CAAC0G,eAAe,EAAE;OAC3BzG,MAAM,GAAGD,MAAM,CAAC0G,eAAe,CAACzG,MAAM,EAAEvH,SAAS,CAAC,CAAA;CACnD,KAAC,MACI;OACJ,IAAIA,SAAS,KAAK,IAAI,EAAE;CACvBuH,QAAAA,MAAM,GAAGA,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAI;CACxB,UAAA,OAAOiJ,eAAoB,CAACjJ,CAAC,EAAE;CAACc,YAAAA,SAAAA;CAAS,WAAC,CAAC,CAAA;CAC5C,SAAC,CAAC,CAAA;CACH,OAAA;CACD,KAAA;CAEA,IAAA,IAAI4B,IAAI,GAAG,CAAC,GAAG2F,MAAM,CAAC,CAAA;KAEtB,IAAI9E,IAAI,KAAK,OAAO,EAAE;CAAA,MAAA,IAAAkf,WAAA,CAAA;CACrB;OACA,IAAI3X,KAAK,GAAG1C,MAAM,CAACI,EAAE,KAAAia,CAAAA,WAAA,GAAIra,MAAM,CAACsB,GAAG,cAAA+Y,WAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAVA,WAAA,CAAa,CAAC,CAAC,KAAIpZ,KAAK,CAAClB,KAAK,CAACK,EAAE,CAAA;CAC1D9F,MAAAA,IAAI,CAACggB,OAAO,CAAC5X,KAAK,CAAC,CAAA;CACpB,KAAA;CAEA,IAAA,IAAI1H,KAAK,GAAGiG,KAAK,CAACjG,KAAK,CAAA;KACvB,IAAItC,SAAS,KAAK,IAAI,EAAE;CACvBsC,MAAAA,KAAK,GAAG6F,eAAoB,CAAC7F,KAAK,EAAE;CAACtC,QAAAA,SAAAA;CAAS,OAAC,CAAC,CAAA;CACjD,KAAA;KAEA,IAAI6hB,QAAQ,GAAGtZ,KAAK,CAACjG,KAAK,IAAI,CAAC,IAAIgF,MAAM,CAACwa,OAAO,GAAG,EAAE,GAAI,CAAA,EAAExa,MAAM,CAACya,MAAM,GAAG,GAAG,GAAG,IAAK,CAAGzf,CAAAA,EAAAA,KAAM,CAAC,CAAA,CAAA;CACjGrD,IAAAA,GAAG,GAAI,CAAEwD,EAAAA,IAAK,IAAGb,IAAI,CAAC6L,IAAI,CAACnG,MAAM,CAACya,MAAM,GAAG,IAAI,GAAG,GAAG,CAAE,CAAA,EAAEF,QAAS,CAAE,CAAA,CAAA,CAAA;CACrE,GAAA;CAEA,EAAA,OAAO5iB,GAAG,CAAA;CACX;;CCnFA;CACA;CACA;CACA;CACA,MAAMwP,SAAO,GAAG,CACf,CAAE,kBAAkB,EAAE,mBAAmB,EAAG,kBAAkB,CAAG,EACjE,CAAE,kBAAkB,EAAE,kBAAkB,EAAI,mBAAmB,CAAE,EACjE,CAAE,iBAAiB,EAAG,oBAAoB,EAAE,iBAAiB,CAAI,CACjE,CAAA;;CAED;CACA,MAAMC,WAAS,GAAG,CACjB,CAAG,iBAAiB,EAAG,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAG,EAChE,CAAE,CAAC,iBAAiB,EAAI,iBAAiB,EAAG,kBAAkB,CAAE,EAChE,CAAG,iBAAiB,EAAG,CAAC,iBAAiB,EAAG,iBAAiB,CAAG,CAChE,CAAA;AAED,qBAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,gBAAgB;CACpBsC,EAAAA,KAAK,EAAE,kBAAkB;CACzBvH,EAAAA,IAAI,EAAE,iBAAiB;CACvBsI,EAAAA,KAAK,EAAE,KAAK;YACZ0D,SAAO;CACPC,aAAAA,WAAAA;CACD,CAAC,CAAC;;CCxBF;;CAEA,MAAMsT,CAAC,GAAG,gBAAgB,CAAA;CAC1B,MAAMC,CAAC,GAAG,iBAAiB,CAAA;AAE3B,eAAe,IAAI9T,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,SAAS;CACbjF,EAAAA,IAAI,EAAE,UAAU;CAChB0B,EAAAA,IAAI,EAAE+d,aAAa;CACnB;GACApX,MAAMA,CAAEqX,GAAG,EAAE;CACZ,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;CAC7B,MAAA,IAAIA,GAAG,GAAGme,CAAC,GAAG,GAAG,EAAE;SAClB,OAAOne,GAAG,GAAG,GAAG,CAAA;CACjB,OAAA;CAEA,MAAA,OAAOpD,IAAI,CAACmP,GAAG,CAAC,CAAC/L,GAAG,GAAGke,CAAC,GAAG,CAAC,IAAIA,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;CAC7C,KAAC,CAAC,CAAA;IACF;GACDnX,QAAQA,CAAEsX,GAAG,EAAE;CACd,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;OAC7B,IAAIA,GAAG,IAAIme,CAAC,EAAE;CACb,QAAA,OAAOD,CAAC,GAAGthB,IAAI,CAACmP,GAAG,CAAC/L,GAAG,EAAE,IAAI,CAAC,IAAIke,CAAC,GAAG,CAAC,CAAC,CAAA;CACzC,OAAA;OAEA,OAAO,GAAG,GAAGle,GAAG,CAAA;CACjB,KAAC,CAAC,CAAA;CACH,GAAA;CACD,CAAC,CAAC;;CC5BF,MAAM2K,SAAO,GAAG,CACf,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,kBAAkB,CAAC,EAC7D,CAAC,kBAAkB,EAAE,kBAAkB,EAAG,iBAAiB,CAAC,EAC5D,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAC5D,CAAA;CAED,MAAMC,WAAS,GAAG,CACjB,CAAE,iBAAiB,EAAI,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,CAAC,EACjE,CAAC,CAAC,kBAAkB,EAAI,kBAAkB,EAAG,oBAAoB,CAAC,EAClE,CAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAChE,CAAA;AAED,gBAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,WAAW;CACfsC,EAAAA,KAAK,EAAE,qBAAqB;CAC5BvH,EAAAA,IAAI,EAAE,WAAW;CACjBsI,EAAAA,KAAK,EAAE,KAAK;YACZ0D,SAAO;CACPC,aAAAA,WAAAA;CACD,CAAC,CAAC;;CCnBF;CACA;CACA;;CAEA;CACA;CACA;CACA,MAAMD,SAAO,GAAG,CACf,CAAE,mBAAmB,EAAE,iBAAiB,EAAI,kBAAkB,CAAG,EACjE,CAAE,mBAAmB,EAAE,iBAAiB,EAAI,mBAAmB,CAAE,EACjE,CAAE,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,CAAG,CACjE,CAAA;;CAED;CACA;CACO,MAAMC,WAAS,GAAG,CACxB,CAAG,kBAAkB,EAAG,CAAC,iBAAiB,EAAI,CAAC,kBAAkB,CAAG,EACpE,CAAE,CAAC,kBAAkB,EAAI,kBAAkB,EAAI,mBAAmB,CAAE,EACpE,CAAG,mBAAmB,EAAE,CAAC,mBAAmB,EAAG,kBAAkB,CAAG,CACpE,CAAA;AAED,kBAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,aAAa;CACjBjF,EAAAA,IAAI,EAAE,aAAa;CACnBsI,EAAAA,KAAK,EAAE,KAAK;YACZ0D,SAAO;CACPC,aAAAA,WAAAA;CACD,CAAC,CAAC;;CC7BF;CACA;CACA;CACA;;CAEA;CACA;CACA;AACA,gBAAe;GACd,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACtC,EAAA,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,EAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;GACjB,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;GACvC,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC1B,EAAA,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC1C,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnC,EAAA,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;GAClB,gBAAgB,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,EAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACjB,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACxC,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC7C,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC/B,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GAC7C,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACjC,EAAA,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACnD,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACrC,EAAA,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC1C,EAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;GACjB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;GAC7B,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACrC,EAAA,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACjD,EAAA,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC7C,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAC9B,EAAA,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC9C,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACxC,EAAA,gBAAgB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GACjD,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAC/B,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC9C,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC5B,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,EAAA,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,EAAA,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,EAAA,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC/C,EAAA,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GAC/C,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC1C,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;GACvC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACpC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAChC,EAAA,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,EAAA,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC5C,YAAY,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACtC,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACxC,EAAA,aAAa,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC9C,EAAA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACpB,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC9C,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;GACvC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CACzB,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC7C,EAAA,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACzC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;GAC1B,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC;CACvC,EAAA,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACzC,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;GACrC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACpC,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GAC5C,QAAQ,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;GAClC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1B,EAAA,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,EAAA,UAAU,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC7C,eAAe,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC1C,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;GACtC,cAAc,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC/C,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC9B,EAAA,sBAAsB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzD,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC9C,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACtC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACxC,EAAA,eAAe,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,EAAA,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,EAAA,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,EAAA,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,EAAA,gBAAgB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACnD,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAChC,EAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACjB,EAAA,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC5C,EAAA,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,EAAA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;GACpB,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3B,EAAA,kBAAkB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACrD,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/B,EAAA,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAChD,EAAA,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,EAAA,gBAAgB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,EAAA,iBAAiB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACpD,mBAAmB,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,iBAAiB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,EAAA,iBAAiB,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACnD,EAAA,cAAc,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC/C,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;GACtC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACtC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACrC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACxC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACzB,EAAA,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC5C,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;CAClC,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GAC7C,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;GAC3B,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC;CAC7B,EAAA,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,EAAA,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAClD,EAAA,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAClD,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACvC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACtC,EAAA,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GACxC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjC,EAAA,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACzC,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC/C,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACnC,EAAA,eAAe,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjD,EAAA,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAChB,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,EAAA,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC9C,EAAA,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CAC9C,EAAA,UAAU,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;GAC3C,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACrC,EAAA,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACzC,EAAA,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,EAAA,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC5C,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC9C,EAAA,WAAW,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC9C,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACjC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAChC,EAAA,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,EAAA,KAAK,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GACxC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CACjC,EAAA,SAAS,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;GAC5C,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC;CACjC,EAAA,WAAW,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC7C,EAAA,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC3C,EAAA,OAAO,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC1C,EAAA,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAClB,EAAA,YAAY,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/C,EAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnB,EAAA,aAAa,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAA;CAC/C,CAAC;;CCzJD,IAAIlL,YAAY,GAAGlF,KAAK,CAAC,CAAC,CAAC,CAAC8jB,IAAI,CAAC,iCAAiC,CAAC,CAAA;CACnE,IAAIC,kBAAkB,GAAG/jB,KAAK,CAAC,CAAC,CAAC,CAAC8jB,IAAI,CAAC,kBAAkB,CAAC,CAAA;AAE1D,YAAe,IAAIjU,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,MAAM;CACVjF,EAAAA,IAAI,EAAE,MAAM;CACZ0B,EAAAA,IAAI,EAAEme,UAAU;GAChBzX,QAAQ,EAAEgE,GAAG,IAAI;CAChB;CACA;CACA;CACA,IAAA,OAAOA,GAAG,CAACrQ,GAAG,CAACsF,GAAG,IAAI;OACrB,IAAIG,IAAI,GAAGH,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;CAC3B,MAAA,IAAIlD,GAAG,GAAGkD,GAAG,GAAGG,IAAI,CAAA;OAEpB,IAAIrD,GAAG,GAAG,SAAS,EAAE;CACpB,QAAA,OAAOqD,IAAI,IAAI,KAAK,GAAIrD,GAAG,KAAK,CAAC,GAAG,GAAG,CAAE,GAAG,KAAK,CAAC,CAAA;CACnD,OAAA;OAEA,OAAO,KAAK,GAAGkD,GAAG,CAAA;CACnB,KAAC,CAAC,CAAA;IACF;GACDgH,MAAM,EAAE+D,GAAG,IAAI;CACd;CACA;CACA;CACA,IAAA,OAAOA,GAAG,CAACrQ,GAAG,CAACsF,GAAG,IAAI;OACrB,IAAIG,IAAI,GAAGH,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;CAC3B,MAAA,IAAIlD,GAAG,GAAGkD,GAAG,GAAGG,IAAI,CAAA;OAEpB,IAAIrD,GAAG,IAAI,OAAO,EAAE;SACnB,OAAOkD,GAAG,GAAG,KAAK,CAAA;CACnB,OAAA;OAEA,OAAOG,IAAI,GAAI,CAAC,CAACrD,GAAG,GAAG,KAAK,IAAI,KAAK,KAAK,GAAI,CAAA;CAC/C,KAAC,CAAC,CAAA;IACF;CACDqJ,EAAAA,OAAO,EAAE;CACR,IAAA,KAAK,EAAE;CACN1C,MAAAA,MAAM,EAAE/D,YAAAA;MACR;CACD,IAAA,YAAY,EAAE;CACbf,MAAAA,IAAI,EAAE,KAAK;CACXsf,MAAAA,MAAM,EAAE,IAAI;CACZxa,MAAAA,MAAM,EAAE8a,kBAAkB;CAC1BP,MAAAA,OAAO,EAAE,IAAA;MACT;KACD,OAAO,EAAE,oBAAsB;CAC/B,IAAA,MAAM,EAAE;CACPva,MAAAA,MAAM,EAAE/D,YAAY;CACpBue,MAAAA,MAAM,EAAE,IAAI;CACZ7X,MAAAA,SAAS,EAAE,IAAA;MACX;CACD,IAAA,aAAa,EAAE;CACdzH,MAAAA,IAAI,EAAE,MAAM;CACZsf,MAAAA,MAAM,EAAE,IAAI;CACZxa,MAAAA,MAAM,EAAE8a,kBAAAA;MACR;CACD,IAAA,KAAK,EAAE;CACNhjB,MAAAA,IAAI,EAAE,QAAQ;CACdqgB,MAAAA,OAAO,EAAE,IAAI;OACbvd,IAAI,EAAE/C,GAAG,IAAI,0BAA0B,CAAC+C,IAAI,CAAC/C,GAAG,CAAC;OACjDgJ,KAAKA,CAAEhJ,GAAG,EAAE;CACX,QAAA,IAAIA,GAAG,CAACf,MAAM,IAAI,CAAC,EAAE;CACpB;WACAe,GAAG,GAAGA,GAAG,CAACyC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;CACxC,SAAA;SAEA,IAAI0gB,IAAI,GAAG,EAAE,CAAA;CACbnjB,QAAAA,GAAG,CAACyC,OAAO,CAAC,eAAe,EAAE2gB,SAAS,IAAI;WACzCD,IAAI,CAAC/f,IAAI,CAACigB,QAAQ,CAACD,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAA;CACzC,SAAC,CAAC,CAAA;SAEF,OAAO;CACN7Y,UAAAA,OAAO,EAAE,MAAM;WACfpC,MAAM,EAAEgb,IAAI,CAACrgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;WACxBI,KAAK,EAAEigB,IAAI,CAACrgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;UACtB,CAAA;QACD;CACDqf,MAAAA,SAAS,EAAE,UAACha,MAAM,EAAEjF,KAAK,EAEd;SAAA,IAFgB;WAC1BogB,QAAQ,GAAG,IAAI;CAChB,SAAC,GAAAje,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;SACL,IAAInC,KAAK,GAAG,CAAC,EAAE;CACdiF,UAAAA,MAAM,CAAC/E,IAAI,CAACF,KAAK,CAAC,CAAA;CACnB,SAAA;CAEAiF,QAAAA,MAAM,GAAGA,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAIwB,IAAI,CAACiiB,KAAK,CAACzjB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;CAE7C,QAAA,IAAI0jB,WAAW,GAAGF,QAAQ,IAAInb,MAAM,CAACwE,KAAK,CAAC7M,CAAC,IAAIA,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAA;CAE7D,QAAA,IAAI2jB,GAAG,GAAGtb,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAI;CACzB,UAAA,IAAI0jB,WAAW,EAAE;aAChB,OAAO,CAAC1jB,CAAC,GAAG,EAAE,EAAEO,QAAQ,CAAC,EAAE,CAAC,CAAA;CAC7B,WAAA;CAEA,UAAA,OAAOP,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC,CAACqjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;CACvC,SAAC,CAAC,CAACrV,IAAI,CAAC,EAAE,CAAC,CAAA;SAEX,OAAO,GAAG,GAAGoV,GAAG,CAAA;CACjB,OAAA;MACA;CACD,IAAA,SAAS,EAAE;CACVxjB,MAAAA,IAAI,EAAE,QAAQ;OACd8C,IAAI,EAAE/C,GAAG,IAAI,WAAW,CAAC+C,IAAI,CAAC/C,GAAG,CAAC;OAClCgJ,KAAKA,CAAEhJ,GAAG,EAAE;CACXA,QAAAA,GAAG,GAAGA,GAAG,CAACQ,WAAW,EAAE,CAAA;CACvB,QAAA,IAAIX,GAAG,GAAG;CAAC0K,UAAAA,OAAO,EAAE,MAAM;CAAEpC,UAAAA,MAAM,EAAE,IAAI;CAAEjF,UAAAA,KAAK,EAAE,CAAA;UAAE,CAAA;SAEnD,IAAIlD,GAAG,KAAK,aAAa,EAAE;CAC1BH,UAAAA,GAAG,CAACsI,MAAM,GAAGwb,QAAQ,CAACjC,KAAK,CAAA;WAC3B7hB,GAAG,CAACqD,KAAK,GAAG,CAAC,CAAA;CACd,SAAC,MACI;CACJrD,UAAAA,GAAG,CAACsI,MAAM,GAAGwb,QAAQ,CAAC3jB,GAAG,CAAC,CAAA;CAC3B,SAAA;SAEA,IAAIH,GAAG,CAACsI,MAAM,EAAE;CACf,UAAA,OAAOtI,GAAG,CAAA;CACX,SAAA;CACD,OAAA;CACD,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;AC1HF,UAAe,IAAIkP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,IAAI;CACRsC,EAAAA,KAAK,EAAE,YAAY;CACnBvH,EAAAA,IAAI,EAAE,IAAI;CACV0B,EAAAA,IAAI,EAAE6e,QAAQ;CACd;GACAnY,QAAQ,EAAEoY,IAAI,CAACpY,QAAQ;GACvBC,MAAM,EAAEmY,IAAI,CAACnY,MAAAA;CACd,CAAC,CAAC;;CCFF;CACApB,QAAQ,CAACwZ,aAAa,GAAGD,IAAI,CAAA;CAE7B,IAAIE,YAAY,CAAA;CAEhB,IAAI,OAAOC,GAAG,KAAK,WAAW,IAAIA,GAAG,CAACC,QAAQ,EAAE;CAC/C;GACA,KAAK,IAAIhc,KAAK,IAAI,CAACuI,GAAG,EAAE0T,OAAO,EAAEC,EAAE,CAAC,EAAE;CACrC,IAAA,IAAIhc,MAAM,GAAGF,KAAK,CAACoF,YAAY,EAAE,CAAA;CACjC,IAAA,IAAIlE,KAAK,GAAG;OAAClB,KAAK;OAAEE,MAAM;CAAEjF,MAAAA,KAAK,EAAE,CAAA;MAAE,CAAA;CACrC,IAAA,IAAIlD,GAAG,GAAGmiB,SAAS,CAAChZ,KAAK,CAAC,CAAA;KAE1B,IAAI6a,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAEjkB,GAAG,CAAC,EAAE;OAC/BsK,QAAQ,CAACwZ,aAAa,GAAG7b,KAAK,CAAA;CAC9B,MAAA,MAAA;CACD,KAAA;CACD,GAAA;CACD,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACe,SAASmc,OAAOA,CAAEjb,KAAK,EAAqD;GAAA,IAAnD;KAAClB,KAAK,GAAGqC,QAAQ,CAACwZ,aAAa;KAAE,GAAGnc,OAAAA;CAAO,GAAC,GAAAtC,SAAA,CAAApG,MAAA,GAAAoG,CAAAA,IAAAA,SAAA,CAAAC,CAAAA,CAAAA,KAAAA,SAAA,GAAAD,SAAA,CAAG,CAAA,CAAA,GAAA,EAAE,CAAA;CACxF,EAAA,IAAIxF,GAAG,GAAGsiB,SAAS,CAAChZ,KAAK,EAAExB,OAAO,CAAC,CAAA;CAEnC,EAAA,IAAI,OAAOqc,GAAG,KAAK,WAAW,IAAIA,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAEpkB,GAAG,CAAC,IAAI,CAACyK,QAAQ,CAACwZ,aAAa,EAAE;CACxFjkB,IAAAA,GAAG,GAAG,IAAI0E,MAAM,CAAC1E,GAAG,CAAC,CAAA;KACrBA,GAAG,CAACsJ,KAAK,GAAGA,KAAK,CAAA;CAClB,GAAC,MACI;CACJ;KACA,IAAIkb,aAAa,GAAGlb,KAAK,CAAA;;CAEzB;CACA,IAAA,IAAImb,OAAO,GAAGnb,KAAK,CAAChB,MAAM,CAACoc,IAAI,CAACzjB,MAAM,CAAC,IAAIA,MAAM,CAACqI,KAAK,CAACjG,KAAK,CAAC,CAAA;CAE9D,IAAA,IAAIohB,OAAO,EAAE;CAAA,MAAA,IAAAE,aAAA,CAAA;CACZ;OACA,IAAI,EAAA,CAAAA,aAAA,GAAET,YAAY,cAAAS,aAAA,KAAA,KAAA,CAAA,GAAAA,aAAA,GAAZT,YAAY,GAAKC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE;CACnE;CACAI,QAAAA,aAAa,GAAG7O,KAAK,CAACrM,KAAK,CAAC,CAAA;SAC5Bkb,aAAa,CAAClc,MAAM,GAAGkc,aAAa,CAAClc,MAAM,CAAC/I,GAAG,CAAC+B,QAAQ,CAAC,CAAA;SACzDkjB,aAAa,CAACnhB,KAAK,GAAG/B,QAAQ,CAACkjB,aAAa,CAACnhB,KAAK,CAAC,CAAA;CAEnDrD,QAAAA,GAAG,GAAGsiB,SAAS,CAACkC,aAAa,EAAE1c,OAAO,CAAC,CAAA;SAEvC,IAAIqc,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAEpkB,GAAG,CAAC,EAAE;CAC/B;CACAA,UAAAA,GAAG,GAAG,IAAI0E,MAAM,CAAC1E,GAAG,CAAC,CAAA;WACrBA,GAAG,CAACsJ,KAAK,GAAGkb,aAAa,CAAA;CACzB,UAAA,OAAOxkB,GAAG,CAAA;CACX,SAAA;CACD,OAAA;CACD,KAAA;;CAEA;CACA;CACAwkB,IAAAA,aAAa,GAAGpgB,EAAE,CAACogB,aAAa,EAAEpc,KAAK,CAAC,CAAA;KACxCpI,GAAG,GAAG,IAAI0E,MAAM,CAAC4d,SAAS,CAACkC,aAAa,EAAE1c,OAAO,CAAC,CAAC,CAAA;KACnD9H,GAAG,CAACsJ,KAAK,GAAGkb,aAAa,CAAA;CAC1B,GAAA;CAEA,EAAA,OAAOxkB,GAAG,CAAA;CACX;;CChFe,SAAS4M,MAAMA,CAAEiJ,MAAM,EAAEC,MAAM,EAAE;CAC/CD,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;CAEzB,EAAA,OAAOD,MAAM,CAACzN,KAAK,KAAK0N,MAAM,CAAC1N,KAAK,IAC1ByN,MAAM,CAACxS,KAAK,KAAKyS,MAAM,CAACzS,KAAK,IAC7BwS,MAAM,CAACvN,MAAM,CAACwE,KAAK,CAAC,CAAC7M,CAAC,EAAEL,CAAC,KAAKK,CAAC,KAAK6V,MAAM,CAACxN,MAAM,CAAC1I,CAAC,CAAC,CAAC,CAAA;CAChE;;CCTA;CACA;CACA;CAKO,SAASglB,YAAYA,CAAEtb,KAAK,EAAE;CACpC;GACA,OAAO8B,GAAG,CAAC9B,KAAK,EAAE,CAACiV,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;CAClC,CAAA;CAEO,SAASsG,YAAYA,CAAEvb,KAAK,EAAErF,KAAK,EAAE;CAC3C;GACAiM,GAAG,CAAC5G,KAAK,EAAE,CAACiV,OAAO,EAAE,GAAG,CAAC,EAAEta,KAAK,CAAC,CAAA;CAClC,CAAA;CAEO,SAASyJ,UAAQA,CAAEoX,KAAK,EAAE;GAChCxkB,MAAM,CAAC+L,cAAc,CAACyY,KAAK,CAACvkB,SAAS,EAAE,WAAW,EAAE;CACnD6K,IAAAA,GAAGA,GAAI;OACN,OAAOwZ,YAAY,CAAC,IAAI,CAAC,CAAA;MACzB;KACD1U,GAAGA,CAAEjM,KAAK,EAAE;CACX4gB,MAAAA,YAAY,CAAC,IAAI,EAAE5gB,KAAK,CAAC,CAAA;CAC1B,KAAA;CACD,GAAC,CAAC,CAAA;CACH;;;;;;;;;CC1BA;CACA;CACA;;CAKe,SAAS8gB,cAAcA,CAAElP,MAAM,EAAEC,MAAM,EAAE;CACvDD,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;CAEzB,EAAA,IAAIkP,EAAE,GAAGvjB,IAAI,CAACqD,GAAG,CAAC8f,YAAY,CAAC/O,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;CAC1C,EAAA,IAAIoP,EAAE,GAAGxjB,IAAI,CAACqD,GAAG,CAAC8f,YAAY,CAAC9O,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;GAE1C,IAAImP,EAAE,GAAGD,EAAE,EAAE;KACZ,CAACA,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAED,EAAE,CAAC,CAAA;CACpB,GAAA;GAEA,OAAO,CAACA,EAAE,GAAG,GAAG,KAAKC,EAAE,GAAG,GAAG,CAAC,CAAA;CAC/B;;CCnBA;CACA;CACA;;;CAKA;CACA,MAAMC,MAAM,GAAG,IAAI,CAAA;CACnB,MAAMC,OAAO,GAAG,IAAI,CAAA;CACpB,MAAMC,MAAM,GAAG,IAAI,CAAA;CACnB,MAAMC,KAAK,GAAG,IAAI,CAAA;;CAElB;CACA,MAAMC,OAAO,GAAG,KAAK,CAAA;CACrB,MAAMC,OAAO,GAAG,KAAK,CAAA;CACrB,MAAMC,MAAM,GAAG,GAAG,CAAA;CAClB,MAAMC,SAAS,GAAG,MAAM,CAAA;;CAExB;CACA;CACA,MAAMC,QAAQ,GAAG,IAAI,CAAA;CACrB,MAAMC,WAAW,GAAG,KAAK,CAAA;CACzB,MAAMC,QAAQ,GAAG,IAAI,CAAA;CAGrB,SAASC,MAAMA,CAAEC,CAAC,EAAE;GACnB,IAAIA,CAAC,IAAIR,OAAO,EAAE;CACjB,IAAA,OAAOQ,CAAC,CAAA;CACT,GAAA;CACA,EAAA,OAAOA,CAAC,GAAG,CAACR,OAAO,GAAGQ,CAAC,KAAKP,OAAO,CAAA;CACpC,CAAA;CAEA,SAASQ,SAASA,CAAElhB,GAAG,EAAE;GACxB,IAAIG,IAAI,GAAGH,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;CAC3B,EAAA,IAAIlD,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACkD,GAAG,CAAC,CAAA;GACvB,OAAOG,IAAI,GAAGvD,IAAI,CAACmP,GAAG,CAACjP,GAAG,EAAE,GAAG,CAAC,CAAA;CACjC,CAAA;;CAEA;CACe,SAASqkB,YAAYA,CAAEC,UAAU,EAAEC,UAAU,EAAE;CAC7DA,EAAAA,UAAU,GAAG/a,QAAQ,CAAC+a,UAAU,CAAC,CAAA;CACjCD,EAAAA,UAAU,GAAG9a,QAAQ,CAAC8a,UAAU,CAAC,CAAA;CAEjC,EAAA,IAAIE,CAAC,CAAA;CACL,EAAA,IAAI1I,CAAC,CAAA;CACL,EAAA,IAAI2I,IAAI,CAAA;;CAER;CACA,EAAA,IAAIC,CAAC,EAAE/S,CAAC,EAAEpU,CAAC,CAAA;CAEXgnB,EAAAA,UAAU,GAAG9hB,EAAE,CAAC8hB,UAAU,EAAE,MAAM,CAAC,CAAA;CACnC;;CAEA;CACA;GACA,CAACG,CAAC,EAAE/S,CAAC,EAAEpU,CAAC,CAAC,GAAGgnB,UAAU,CAAC5d,MAAM,CAAA;GAC7B,IAAIge,MAAM,GAAGP,SAAS,CAACM,CAAC,CAAC,GAAG,SAAS,GAAGN,SAAS,CAACzS,CAAC,CAAC,GAAG,SAAS,GAAGyS,SAAS,CAAC7mB,CAAC,CAAC,GAAG,SAAS,CAAA;CAE3F+mB,EAAAA,UAAU,GAAG7hB,EAAE,CAAC6hB,UAAU,EAAE,MAAM,CAAC,CAAA;GACnC,CAACI,CAAC,EAAE/S,CAAC,EAAEpU,CAAC,CAAC,GAAG+mB,UAAU,CAAC3d,MAAM,CAAA;GAC7B,IAAIie,KAAK,GAAGR,SAAS,CAACM,CAAC,CAAC,GAAG,SAAS,GAAGN,SAAS,CAACzS,CAAC,CAAC,GAAG,SAAS,GAAGyS,SAAS,CAAC7mB,CAAC,CAAC,GAAG,SAAS,CAAA;;CAE1F;CACA,EAAA,IAAIsnB,IAAI,GAAGX,MAAM,CAACS,MAAM,CAAC,CAAA;CACzB,EAAA,IAAIG,GAAG,GAAGZ,MAAM,CAACU,KAAK,CAAC,CAAA;;CAEvB;CACA,EAAA,IAAIG,GAAG,GAAGD,GAAG,GAAGD,IAAI,CAAA;;CAEpB;CACA;CACA;GACA,IAAI/kB,IAAI,CAACE,GAAG,CAAC8kB,GAAG,GAAGD,IAAI,CAAC,GAAGf,SAAS,EAAE;CACrChI,IAAAA,CAAC,GAAG,CAAC,CAAA;CACN,GAAC,MACI;CACJ,IAAA,IAAIiJ,GAAG,EAAE;CACR;CACAP,MAAAA,CAAC,GAAGM,GAAG,IAAIvB,MAAM,GAAGsB,IAAI,IAAIrB,OAAO,CAAA;OACnC1H,CAAC,GAAG0I,CAAC,GAAGT,QAAQ,CAAA;CACjB,KAAC,MACI;CACJ;CACAS,MAAAA,CAAC,GAAGM,GAAG,IAAIpB,KAAK,GAAGmB,IAAI,IAAIpB,MAAM,CAAA;OACjC3H,CAAC,GAAG0I,CAAC,GAAGP,QAAQ,CAAA;CACjB,KAAA;CACD,GAAA;GACA,IAAInkB,IAAI,CAACE,GAAG,CAAC8b,CAAC,CAAC,GAAG+H,MAAM,EAAE;CACzBY,IAAAA,IAAI,GAAG,CAAC,CAAA;CACT,GAAC,MACI,IAAI3I,CAAC,GAAG,CAAC,EAAE;CACf;CACA;KACA2I,IAAI,GAAG3I,CAAC,GAAGkI,WAAW,CAAA;CACvB,GAAC,MACI;KACJS,IAAI,GAAG3I,CAAC,GAAGkI,WAAW,CAAA;CACvB,GAAA;GAEA,OAAOS,IAAI,GAAG,GAAG,CAAA;CAClB;;CCrGA;CACA;CACA;CACA;;CAKe,SAASO,iBAAiBA,CAAE9Q,MAAM,EAAEC,MAAM,EAAE;CAC1DD,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;CAEzB,EAAA,IAAIkP,EAAE,GAAGvjB,IAAI,CAACqD,GAAG,CAAC8f,YAAY,CAAC/O,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;CAC1C,EAAA,IAAIoP,EAAE,GAAGxjB,IAAI,CAACqD,GAAG,CAAC8f,YAAY,CAAC9O,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;GAE1C,IAAImP,EAAE,GAAGD,EAAE,EAAE;KACZ,CAACA,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAED,EAAE,CAAC,CAAA;CACpB,GAAA;CAEA,EAAA,IAAIhN,KAAK,GAAIgN,EAAE,GAAGC,EAAG,CAAA;GACrB,OAAOjN,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAACgN,EAAE,GAAGC,EAAE,IAAIjN,KAAK,CAAA;CAC3C;;CCrBA;CACA;CACA;CACA;;;CAKA;CACA;CACA;CACA;CACA,MAAMlT,GAAG,GAAG,KAAK,CAAA;CAEF,SAAS8hB,aAAaA,CAAE/Q,MAAM,EAAEC,MAAM,EAAE;CACtDD,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;CAEzB,EAAA,IAAIkP,EAAE,GAAGvjB,IAAI,CAACqD,GAAG,CAAC8f,YAAY,CAAC/O,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;CAC1C,EAAA,IAAIoP,EAAE,GAAGxjB,IAAI,CAACqD,GAAG,CAAC8f,YAAY,CAAC9O,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;GAE1C,IAAImP,EAAE,GAAGD,EAAE,EAAE;KACZ,CAACA,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAED,EAAE,CAAC,CAAA;CACpB,GAAA;GAEA,OAAOC,EAAE,KAAK,CAAC,GAAGngB,GAAG,GAAG,CAACkgB,EAAE,GAAGC,EAAE,IAAIA,EAAE,CAAA;CACvC;;CC1BA;CACA;CACA;;CAMe,SAAS4B,aAAaA,CAAEhR,MAAM,EAAEC,MAAM,EAAE;CACtDD,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;GAEzB,IAAIlD,EAAE,GAAGxH,GAAG,CAACyK,MAAM,EAAE,CAAC/C,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;GAChC,IAAIG,EAAE,GAAG7H,GAAG,CAAC0K,MAAM,EAAE,CAAChD,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;CAEhC,EAAA,OAAOrR,IAAI,CAACE,GAAG,CAACiR,EAAE,GAAGK,EAAE,CAAC,CAAA;CACzB;;CCZA;CACA,MAAM5H,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAM+E,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;CACnB,MAAMC,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;;CAErB,IAAIvE,OAAK,GAAGxE,MAAM,CAACE,GAAG,CAAA;AAEtB,eAAe,IAAIsC,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,SAAS;CACbjF,EAAAA,IAAI,EAAE,SAAS;CACf8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACD+M,IAAAA,CAAC,EAAE;CACFtH,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;MACpB;CACDqG,IAAAA,CAAC,EAAE;CACFrG,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;CACrB,KAAA;IACA;CAED;CACA;UACA6C,OAAK;CAEL5G,EAAAA,IAAI,EAAEqZ,OAAO;CACb;CACA;GACA3S,QAAQA,CAAE/D,GAAG,EAAE;CACd;CACA,IAAA,IAAIgI,GAAG,GAAGhI,GAAG,CAACtI,GAAG,CAAC,CAAC0E,KAAK,EAAErE,CAAC,KAAKqE,KAAK,GAAG6H,OAAK,CAAClM,CAAC,CAAC,CAAC,CAAA;;CAEjD;KACA,IAAI6Q,CAAC,GAAGZ,GAAG,CAACtQ,GAAG,CAAC0E,KAAK,IAAIA,KAAK,GAAGoH,GAAC,GAAG5J,IAAI,CAACiP,IAAI,CAACzM,KAAK,CAAC,GAAG,CAACoM,GAAC,GAAGpM,KAAK,GAAG,EAAE,IAAI,GAAG,CAAC,CAAA;KAE/E,OAAO,CACL,GAAG,GAAGwM,CAAC,CAAC,CAAC,CAAC,GAAI,EAAE;CAAI;KACrB,GAAG,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE;KACrB,GAAG,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAA;IACD;CACD;CACA;CACA;GACA5E,MAAMA,CAAE8E,GAAG,EAAE;CACZ;KACA,IAAIF,CAAC,GAAG,EAAE,CAAA;CACVA,IAAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAACE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;CAC1BF,IAAAA,CAAC,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGF,CAAC,CAAC,CAAC,CAAC,CAAA;CAC1BA,IAAAA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;;CAE1B;KACA,IAAId,GAAG,GAAG,CACTY,CAAC,CAAC,CAAC,CAAC,GAAKL,EAAE,GAAG3O,IAAI,CAACmP,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAkB,CAAC,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIJ,GAAC,EACtEM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAIlP,IAAI,CAACmP,GAAG,CAAC,CAACD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGN,GAAC,EAC3DI,CAAC,CAAC,CAAC,CAAC,GAAKL,EAAE,GAAG3O,IAAI,CAACmP,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAkB,CAAC,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIJ,GAAC,CACtE,CAAA;;CAED;CACA,IAAA,OAAOR,GAAG,CAACtQ,GAAG,CAAC,CAAC0E,KAAK,EAAErE,CAAC,KAAKqE,KAAK,GAAG6H,OAAK,CAAClM,CAAC,CAAC,CAAC,CAAA;IAC9C;CAEDoL,EAAAA,OAAO,EAAE;CACR,IAAA,SAAS,EAAE;CACV1C,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAA;CACrG,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCzEF;CACA;CACA;CACA;;CAOA,MAAMwe,GAAG,GAAGrlB,IAAI,CAACmP,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;;CAE1B,SAASmW,gBAAgBA,CAAElR,MAAM,EAAEC,MAAM,EAAE;CACzDD,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;GAEzB,IAAIkR,KAAK,GAAG5b,GAAG,CAACyK,MAAM,EAAE,CAACoR,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;GACvC,IAAIC,KAAK,GAAG9b,GAAG,CAAC0K,MAAM,EAAE,CAACmR,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;GAEvC,IAAIE,YAAY,GAAG1lB,IAAI,CAACE,GAAG,CAACF,IAAI,CAACmP,GAAG,CAACoW,KAAK,EAAEF,GAAG,CAAC,GAAGrlB,IAAI,CAACmP,GAAG,CAACsW,KAAK,EAAEJ,GAAG,CAAC,CAAC,CAAA;CAExE,EAAA,IAAIM,QAAQ,GAAG3lB,IAAI,CAACmP,GAAG,CAACuW,YAAY,EAAG,CAAC,GAAGL,GAAI,CAAC,GAAGrlB,IAAI,CAAC4lB,KAAK,GAAG,EAAE,CAAA;CAElE,EAAA,OAAQD,QAAQ,GAAG,GAAG,GAAI,GAAG,GAAGA,QAAQ,CAAA;CACzC;;;;;;;;;;;;CCnBe,SAASA,QAAQA,CAAEnB,UAAU,EAAEC,UAAU,EAAU;CAAA,EAAA,IAAR7lB,CAAC,GAAAmF,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CAC/D,EAAA,IAAItF,QAAQ,CAACG,CAAC,CAAC,EAAE;CAChBA,IAAAA,CAAC,GAAG;CAACinB,MAAAA,SAAS,EAAEjnB,CAAAA;MAAE,CAAA;CACnB,GAAA;GAEA,IAAI;KAACinB,SAAS;KAAE,GAAGC,IAAAA;CAAI,GAAC,GAAGlnB,CAAC,CAAA;GAE5B,IAAI,CAACinB,SAAS,EAAE;KACf,IAAIE,UAAU,GAAGlnB,MAAM,CAACgK,IAAI,CAACmd,eAAkB,CAAC,CAACloB,GAAG,CAACgR,CAAC,IAAIA,CAAC,CAAC3N,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC4L,IAAI,CAAC,IAAI,CAAC,CAAA;CAChG,IAAA,MAAM,IAAIzG,SAAS,CAAE,CAAyEyf,uEAAAA,EAAAA,UAAW,EAAC,CAAC,CAAA;CAC5G,GAAA;CAEAvB,EAAAA,UAAU,GAAG9a,QAAQ,CAAC8a,UAAU,CAAC,CAAA;CACjCC,EAAAA,UAAU,GAAG/a,QAAQ,CAAC+a,UAAU,CAAC,CAAA;CAEjC,EAAA,KAAK,IAAI3V,CAAC,IAAIkX,eAAkB,EAAE;CACjC,IAAA,IAAI,UAAU,GAAGH,SAAS,CAAC3mB,WAAW,EAAE,KAAK4P,CAAC,CAAC5P,WAAW,EAAE,EAAE;OAC7D,OAAO8mB,eAAkB,CAAClX,CAAC,CAAC,CAAC0V,UAAU,EAAEC,UAAU,EAAEqB,IAAI,CAAC,CAAA;CAC3D,KAAA;CACD,GAAA;CAEA,EAAA,MAAM,IAAIxf,SAAS,CAAE,CAA8Buf,4BAAAA,EAAAA,SAAU,EAAC,CAAC,CAAA;CAChE;;CCxBA;CACO,SAASI,EAAEA,CAAEpe,KAAK,EAAE;CAC1B;CACA,EAAA,IAAI,CAACqe,CAAC,EAAE7B,CAAC,EAAE8B,CAAC,CAAC,GAAG9X,MAAM,CAACxG,KAAK,EAAEiV,OAAO,CAAC,CAAA;GACtC,IAAIvG,KAAK,GAAG2P,CAAC,GAAG,EAAE,GAAG7B,CAAC,GAAG,CAAC,GAAG8B,CAAC,CAAA;CAC9B,EAAA,OAAO,CAAC,CAAC,GAAGD,CAAC,GAAG3P,KAAK,EAAE,CAAC,GAAG8N,CAAC,GAAG9N,KAAK,CAAC,CAAA;CACtC,CAAA;CAEO,SAAS6P,EAAEA,CAAEve,KAAK,EAAE;CAC1B;CACA,EAAA,IAAI,CAACqe,CAAC,EAAE7B,CAAC,EAAE8B,CAAC,CAAC,GAAG9X,MAAM,CAACxG,KAAK,EAAEiV,OAAO,CAAC,CAAA;CACtC,EAAA,IAAKuJ,GAAG,GAAGH,CAAC,GAAG7B,CAAC,GAAG8B,CAAC,CAAA;GACpB,OAAO,CAACD,CAAC,GAAGG,GAAG,EAAEhC,CAAC,GAAGgC,GAAG,CAAC,CAAA;CAC1B,CAAA;CAEO,SAASpa,UAAQA,CAAEoX,KAAK,EAAE;CAChC;CACA;GACAxkB,MAAM,CAAC+L,cAAc,CAACyY,KAAK,CAACvkB,SAAS,EAAE,IAAI,EAAE;CAC5C6K,IAAAA,GAAGA,GAAI;OACN,OAAOsc,EAAE,CAAC,IAAI,CAAC,CAAA;CAChB,KAAA;CACD,GAAC,CAAC,CAAA;GAEFpnB,MAAM,CAAC+L,cAAc,CAACyY,KAAK,CAACvkB,SAAS,EAAE,IAAI,EAAE;CAC5C6K,IAAAA,GAAGA,GAAI;OACN,OAAOyc,EAAE,CAAC,IAAI,CAAC,CAAA;CAChB,KAAA;CACD,GAAC,CAAC,CAAA;CACH;;;;;;;;;CC5Be,SAASvhB,MAAMA,CAAE6P,EAAE,EAAEC,EAAE,EAAU;CAAA,EAAA,IAAR/V,CAAC,GAAAmF,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CAC7C,EAAA,IAAItF,QAAQ,CAACG,CAAC,CAAC,EAAE;CAChBA,IAAAA,CAAC,GAAG;CAACggB,MAAAA,MAAM,EAAEhgB,CAAAA;MAAE,CAAA;CAChB,GAAA;GAEA,IAAI;KAACggB,MAAM,GAAG5V,QAAQ,CAACnE,MAAM;KAAE,GAAGihB,IAAAA;CAAI,GAAC,GAAGlnB,CAAC,CAAA;CAE3C,EAAA,KAAK,IAAIlB,CAAC,IAAI2hB,aAAa,EAAE;CAC5B,IAAA,IAAI,QAAQ,GAAGT,MAAM,CAAC1f,WAAW,EAAE,KAAKxB,CAAC,CAACwB,WAAW,EAAE,EAAE;OACxD,OAAOmgB,aAAa,CAAC3hB,CAAC,CAAC,CAACgX,EAAE,EAAEC,EAAE,EAAEmR,IAAI,CAAC,CAAA;CACtC,KAAA;CACD,GAAA;CAEA,EAAA,MAAM,IAAIxf,SAAS,CAAE,CAAyBsY,uBAAAA,EAAAA,MAAO,EAAC,CAAC,CAAA;CACxD;;CCfO,SAAS0H,OAAOA,CAAEze,KAAK,EAAgB;CAAA,EAAA,IAAd0e,MAAM,GAAAxiB,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,GAAG,CAAA;GAC3C,IAAI4C,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;CAC1C,EAAA,IAAI6c,SAAS,GAAG,CAAC7f,KAAK,EAAE,GAAG,CAAC,CAAA;CAC5B,EAAA,OAAO8H,GAAG,CAAC5G,KAAK,EAAE2e,SAAS,EAAE3X,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG0X,MAAM,CAAC,CAAC,CAAA;CACpD,CAAA;CAEO,SAASE,MAAMA,CAAE5e,KAAK,EAAgB;CAAA,EAAA,IAAd0e,MAAM,GAAAxiB,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,GAAG,CAAA;GAC1C,IAAI4C,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;CAC1C,EAAA,IAAI6c,SAAS,GAAG,CAAC7f,KAAK,EAAE,GAAG,CAAC,CAAA;CAC5B,EAAA,OAAO8H,GAAG,CAAC5G,KAAK,EAAE2e,SAAS,EAAE3X,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG0X,MAAM,CAAC,CAAC,CAAA;CACpD;;;;;;;;CCbA;CACA;CACA;;CAaA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAASG,GAAGA,CAAEhS,EAAE,EAAEC,EAAE,EAAkB;CAAA,EAAA,IAAhB3W,CAAC,GAAA+F,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CAAA,EAAA,IAAEnF,CAAC,GAAAmF,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CAC1C,EAAA,CAAC2Q,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACjL,QAAQ,CAACgL,EAAE,CAAC,EAAEhL,QAAQ,CAACiL,EAAE,CAAC,CAAC,CAAA;CAEvC,EAAA,IAAIhW,IAAI,CAACX,CAAC,CAAC,KAAK,QAAQ,EAAE;KACzB,CAACA,CAAC,EAAEY,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEZ,CAAC,CAAC,CAAA;CACjB,GAAA;GAEA,IAAI2P,CAAC,GAAG3K,KAAK,CAAC0R,EAAE,EAAEC,EAAE,EAAE/V,CAAC,CAAC,CAAA;GACxB,OAAO+O,CAAC,CAAC3P,CAAC,CAAC,CAAA;CACZ,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS2oB,KAAKA,CAAEjS,EAAE,EAAEC,EAAE,EAAgB;CAAA,EAAA,IAAdtO,OAAO,GAAAtC,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CAC1C,EAAA,IAAI6iB,UAAU,CAAA;CAEd,EAAA,IAAIC,OAAO,CAACnS,EAAE,CAAC,EAAE;CAChB;KACA,CAACkS,UAAU,EAAEvgB,OAAO,CAAC,GAAG,CAACqO,EAAE,EAAEC,EAAE,CAAC,CAAA;KAChC,CAACD,EAAE,EAAEC,EAAE,CAAC,GAAGiS,UAAU,CAACE,SAAS,CAACC,MAAM,CAAA;CACvC,GAAA;GAEA,IAAI;KACHC,SAAS;KAAEnI,YAAY;CACvB8H,IAAAA,KAAK,GAAG,CAAC;CAAEM,IAAAA,QAAQ,GAAG,IAAI;KAC1B,GAAGC,YAAAA;CACJ,GAAC,GAAG7gB,OAAO,CAAA;GAEX,IAAI,CAACugB,UAAU,EAAE;CAChB,IAAA,CAAClS,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACjL,QAAQ,CAACgL,EAAE,CAAC,EAAEhL,QAAQ,CAACiL,EAAE,CAAC,CAAC,CAAA;KACvCiS,UAAU,GAAG5jB,KAAK,CAAC0R,EAAE,EAAEC,EAAE,EAAEuS,YAAY,CAAC,CAAA;CACzC,GAAA;CAEA,EAAA,IAAIC,UAAU,GAAGtiB,MAAM,CAAC6P,EAAE,EAAEC,EAAE,CAAC,CAAA;GAC/B,IAAIyS,WAAW,GAAGJ,SAAS,GAAG,CAAC,GAAGhnB,IAAI,CAACqD,GAAG,CAACsjB,KAAK,EAAE3mB,IAAI,CAACqnB,IAAI,CAACF,UAAU,GAAGH,SAAS,CAAC,GAAG,CAAC,CAAC,GAAGL,KAAK,CAAA;GAChG,IAAIpoB,GAAG,GAAG,EAAE,CAAA;GAEZ,IAAI0oB,QAAQ,KAAKjjB,SAAS,EAAE;KAC3BojB,WAAW,GAAGpnB,IAAI,CAACmD,GAAG,CAACikB,WAAW,EAAEH,QAAQ,CAAC,CAAA;CAC9C,GAAA;GAEA,IAAIG,WAAW,KAAK,CAAC,EAAE;CACtB7oB,IAAAA,GAAG,GAAG,CAAC;CAACP,MAAAA,CAAC,EAAE,EAAE;OAAE6J,KAAK,EAAE+e,UAAU,CAAC,EAAE,CAAA;CAAC,KAAC,CAAC,CAAA;CACvC,GAAC,MACI;CACJ,IAAA,IAAIU,IAAI,GAAG,CAAC,IAAIF,WAAW,GAAG,CAAC,CAAC,CAAA;CAChC7oB,IAAAA,GAAG,GAAGX,KAAK,CAAC8E,IAAI,CAAC;CAAC/E,MAAAA,MAAM,EAAEypB,WAAAA;CAAW,KAAC,EAAE,CAAClpB,CAAC,EAAEC,CAAC,KAAK;CACjD,MAAA,IAAIH,CAAC,GAAGG,CAAC,GAAGmpB,IAAI,CAAA;OAChB,OAAO;SAACtpB,CAAC;SAAE6J,KAAK,EAAE+e,UAAU,CAAC5oB,CAAC,CAAA;QAAE,CAAA;CACjC,KAAC,CAAC,CAAA;CACH,GAAA;GAEA,IAAIgpB,SAAS,GAAG,CAAC,EAAE;CAClB;CACA,IAAA,IAAIO,QAAQ,GAAGhpB,GAAG,CAACiW,MAAM,CAAC,CAACC,GAAG,EAAE+S,GAAG,EAAErpB,CAAC,KAAK;OAC1C,IAAIA,CAAC,KAAK,CAAC,EAAE;CACZ,QAAA,OAAO,CAAC,CAAA;CACT,OAAA;CAEA,MAAA,IAAIspB,EAAE,GAAG5iB,MAAM,CAAC2iB,GAAG,CAAC3f,KAAK,EAAEtJ,GAAG,CAACJ,CAAC,GAAG,CAAC,CAAC,CAAC0J,KAAK,EAAEgX,YAAY,CAAC,CAAA;CAC1D,MAAA,OAAO7e,IAAI,CAACqD,GAAG,CAACoR,GAAG,EAAEgT,EAAE,CAAC,CAAA;MACxB,EAAE,CAAC,CAAC,CAAA;KAEL,OAAOF,QAAQ,GAAGP,SAAS,EAAE;CAC5B;CACA;CACAO,MAAAA,QAAQ,GAAG,CAAC,CAAA;CAEZ,MAAA,KAAK,IAAIppB,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAGI,GAAG,CAACZ,MAAM,IAAMY,GAAG,CAACZ,MAAM,GAAGspB,QAAS,EAAE9oB,CAAC,EAAE,EAAE;CACjE,QAAA,IAAIupB,IAAI,GAAGnpB,GAAG,CAACJ,CAAC,GAAG,CAAC,CAAC,CAAA;CACrB,QAAA,IAAIqpB,GAAG,GAAGjpB,GAAG,CAACJ,CAAC,CAAC,CAAA;SAEhB,IAAIH,CAAC,GAAG,CAACwpB,GAAG,CAACxpB,CAAC,GAAG0pB,IAAI,CAAC1pB,CAAC,IAAI,CAAC,CAAA;CAC5B,QAAA,IAAI6J,KAAK,GAAG+e,UAAU,CAAC5oB,CAAC,CAAC,CAAA;SACzBupB,QAAQ,GAAGvnB,IAAI,CAACqD,GAAG,CAACkkB,QAAQ,EAAE1iB,MAAM,CAACgD,KAAK,EAAE6f,IAAI,CAAC7f,KAAK,CAAC,EAAEhD,MAAM,CAACgD,KAAK,EAAE2f,GAAG,CAAC3f,KAAK,CAAC,CAAC,CAAA;CAClFtJ,QAAAA,GAAG,CAACopB,MAAM,CAACxpB,CAAC,EAAE,CAAC,EAAE;WAACH,CAAC;WAAE6J,KAAK,EAAE+e,UAAU,CAAC5oB,CAAC,CAAA;CAAC,SAAC,CAAC,CAAA;CAC3CG,QAAAA,CAAC,EAAE,CAAA;CACJ,OAAA;CACD,KAAA;CACD,GAAA;GAEAI,GAAG,GAAGA,GAAG,CAACT,GAAG,CAACgR,CAAC,IAAIA,CAAC,CAACjH,KAAK,CAAC,CAAA;CAE3B,EAAA,OAAOtJ,GAAG,CAAA;CACX,CAAA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAASyE,KAAKA,CAAEoR,MAAM,EAAEC,MAAM,EAAgB;CAAA,EAAA,IAAdhO,OAAO,GAAAtC,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;CAClD,EAAA,IAAI8iB,OAAO,CAACzS,MAAM,CAAC,EAAE;CACpB;KACA,IAAI,CAACzG,CAAC,EAAEtH,OAAO,CAAC,GAAG,CAAC+N,MAAM,EAAEC,MAAM,CAAC,CAAA;KAEnC,OAAOrR,KAAK,CAAC,GAAG2K,CAAC,CAACmZ,SAAS,CAACC,MAAM,EAAE;CAAC,MAAA,GAAGpZ,CAAC,CAACmZ,SAAS,CAACzgB,OAAO;OAAE,GAAGA,OAAAA;CAAO,KAAC,CAAC,CAAA;CAC1E,GAAA;GAEA,IAAI;KAACM,KAAK;KAAEihB,WAAW;KAAEC,WAAW;CAAEC,IAAAA,aAAAA;CAAa,GAAC,GAAGzhB,OAAO,CAAA;CAE9D+N,EAAAA,MAAM,GAAG1K,QAAQ,CAAC0K,MAAM,CAAC,CAAA;CACzBC,EAAAA,MAAM,GAAG3K,QAAQ,CAAC2K,MAAM,CAAC,CAAA;;CAEzB;CACAD,EAAAA,MAAM,GAAGF,KAAK,CAACE,MAAM,CAAC,CAAA;CACtBC,EAAAA,MAAM,GAAGH,KAAK,CAACG,MAAM,CAAC,CAAA;CAEtB,EAAA,IAAIyS,SAAS,GAAG;CAACC,IAAAA,MAAM,EAAE,CAAC3S,MAAM,EAAEC,MAAM,CAAC;CAAEhO,IAAAA,OAAAA;IAAQ,CAAA;CAEnD,EAAA,IAAIM,KAAK,EAAE;CACVA,IAAAA,KAAK,GAAG0B,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC,CAAA;CAC9B,GAAC,MACI;CACJA,IAAAA,KAAK,GAAG0B,UAAU,CAACe,QAAQ,CAACJ,QAAQ,CAAC+e,kBAAkB,CAAC,IAAI3T,MAAM,CAACzN,KAAK,CAAA;CACzE,GAAA;GAEAihB,WAAW,GAAGA,WAAW,GAAGvf,UAAU,CAACsB,GAAG,CAACie,WAAW,CAAC,GAAGjhB,KAAK,CAAA;CAE/DyN,EAAAA,MAAM,GAAGzR,EAAE,CAACyR,MAAM,EAAEzN,KAAK,CAAC,CAAA;CAC1B0N,EAAAA,MAAM,GAAG1R,EAAE,CAAC0R,MAAM,EAAE1N,KAAK,CAAC,CAAA;;CAE1B;CACAyN,EAAAA,MAAM,GAAG4K,OAAO,CAAC5K,MAAM,CAAC,CAAA;CACxBC,EAAAA,MAAM,GAAG2K,OAAO,CAAC3K,MAAM,CAAC,CAAA;;CAExB;CACA;CACA,EAAA,IAAI1N,KAAK,CAACE,MAAM,CAAC+I,CAAC,IAAIjJ,KAAK,CAACE,MAAM,CAAC+I,CAAC,CAACjR,IAAI,KAAK,OAAO,EAAE;KACtD,IAAI4Q,GAAG,GAAGlJ,OAAO,CAACyJ,GAAG,GAAGzJ,OAAO,CAACyJ,GAAG,IAAI,SAAS,CAAA;CAEhD,IAAA,IAAIA,GAAG,GAAG,CAACnJ,KAAK,EAAE,GAAG,CAAC,CAAA;KACtB,IAAI,CAACqhB,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACte,GAAG,CAACyK,MAAM,EAAEtE,GAAG,CAAC,EAAEnG,GAAG,CAAC0K,MAAM,EAAEvE,GAAG,CAAC,CAAC,CAAA;CACnD;CACA;CACA;KACA,IAAInQ,KAAK,CAACqoB,EAAE,CAAC,IAAI,CAACroB,KAAK,CAACsoB,EAAE,CAAC,EAAE;CAC5BD,MAAAA,EAAE,GAAGC,EAAE,CAAA;CACR,KAAC,MACI,IAAItoB,KAAK,CAACsoB,EAAE,CAAC,IAAI,CAACtoB,KAAK,CAACqoB,EAAE,CAAC,EAAE;CACjCC,MAAAA,EAAE,GAAGD,EAAE,CAAA;CACR,KAAA;CACA,IAAA,CAACA,EAAE,EAAEC,EAAE,CAAC,GAAGzY,MAAa,CAACD,GAAG,EAAE,CAACyY,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAA;CACvCxZ,IAAAA,GAAG,CAAC2F,MAAM,EAAEtE,GAAG,EAAEkY,EAAE,CAAC,CAAA;CACpBvZ,IAAAA,GAAG,CAAC4F,MAAM,EAAEvE,GAAG,EAAEmY,EAAE,CAAC,CAAA;CACrB,GAAA;CAEA,EAAA,IAAIH,aAAa,EAAE;CAClB;CACA1T,IAAAA,MAAM,CAACvN,MAAM,GAAGuN,MAAM,CAACvN,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAIA,CAAC,GAAG4V,MAAM,CAACxS,KAAK,CAAC,CAAA;CACxDyS,IAAAA,MAAM,CAACxN,MAAM,GAAGwN,MAAM,CAACxN,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAIA,CAAC,GAAG6V,MAAM,CAACzS,KAAK,CAAC,CAAA;CACzD,GAAA;CAEA,EAAA,OAAO/C,MAAM,CAACiK,MAAM,CAAC9K,CAAC,IAAI;KACzBA,CAAC,GAAG6pB,WAAW,GAAGA,WAAW,CAAC7pB,CAAC,CAAC,GAAGA,CAAC,CAAA;CACpC,IAAA,IAAI6I,MAAM,GAAGuN,MAAM,CAACvN,MAAM,CAAC/I,GAAG,CAAC,CAACuE,KAAK,EAAElE,CAAC,KAAK;CAC5C,MAAA,IAAImE,GAAG,GAAG+R,MAAM,CAACxN,MAAM,CAAC1I,CAAC,CAAC,CAAA;CAC1B,MAAA,OAAOiE,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAEtE,CAAC,CAAC,CAAA;CAClC,KAAC,CAAC,CAAA;CAEF,IAAA,IAAI4D,KAAK,GAAGQ,WAAW,CAACgS,MAAM,CAACxS,KAAK,EAAEyS,MAAM,CAACzS,KAAK,EAAE5D,CAAC,CAAC,CAAA;CACtD,IAAA,IAAIO,GAAG,GAAG;OAACoI,KAAK;OAAEE,MAAM;CAAEjF,MAAAA,KAAAA;MAAM,CAAA;CAEhC,IAAA,IAAIkmB,aAAa,EAAE;CAClB;CACAvpB,MAAAA,GAAG,CAACsI,MAAM,GAAGtI,GAAG,CAACsI,MAAM,CAAC/I,GAAG,CAACU,CAAC,IAAIA,CAAC,GAAGoD,KAAK,CAAC,CAAA;CAC5C,KAAA;KAEA,IAAIgmB,WAAW,KAAKjhB,KAAK,EAAE;CAC1BpI,MAAAA,GAAG,GAAGoE,EAAE,CAACpE,GAAG,EAAEqpB,WAAW,CAAC,CAAA;CAC3B,KAAA;CAEA,IAAA,OAAOrpB,GAAG,CAAA;CACX,GAAC,EAAE;CACFuoB,IAAAA,SAAAA;CACD,GAAC,CAAC,CAAA;CACH,CAAA;CAEO,SAASD,OAAOA,CAAEzjB,GAAG,EAAE;GAC7B,OAAOzE,IAAI,CAACyE,GAAG,CAAC,KAAK,UAAU,IAAI,CAAC,CAACA,GAAG,CAAC0jB,SAAS,CAAA;CACnD,CAAA;CAEA9d,QAAQ,CAAC+e,kBAAkB,GAAG,KAAK,CAAA;CAE5B,SAAS9b,QAAQA,CAAEoX,KAAK,EAAE;CAChCA,EAAAA,KAAK,CAAC6E,cAAc,CAAC,KAAK,EAAExB,GAAG,EAAE;CAAClY,IAAAA,OAAO,EAAE,OAAA;CAAO,GAAC,CAAC,CAAA;CACpD6U,EAAAA,KAAK,CAAC6E,cAAc,CAAC,OAAO,EAAEllB,KAAK,EAAE;CAACwL,IAAAA,OAAO,EAAE,iBAAA;CAAiB,GAAC,CAAC,CAAA;CAClE6U,EAAAA,KAAK,CAAC6E,cAAc,CAAC,OAAO,EAAEvB,KAAK,EAAE;CAACnY,IAAAA,OAAO,EAAE,cAAA;CAAc,GAAC,CAAC,CAAA;CAChE;;;;;;;;;;;AC1NA,WAAe,IAAInG,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACP+I,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;MACN;CACDkL,IAAAA,CAAC,EAAE;CACFjK,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,YAAA;MACN;CACD8M,IAAAA,CAAC,EAAE;CACF7L,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,WAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAE8e,IAAI;CAEV;GACApY,QAAQ,EAAEgE,GAAG,IAAI;KAChB,IAAI9K,GAAG,GAAGrD,IAAI,CAACqD,GAAG,CAAC,GAAG8K,GAAG,CAAC,CAAA;KAC1B,IAAIhL,GAAG,GAAGnD,IAAI,CAACmD,GAAG,CAAC,GAAGgL,GAAG,CAAC,CAAA;KAC1B,IAAI,CAACR,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGM,GAAG,CAAA;CACnB,IAAA,IAAI,CAACyB,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,GAAG,CAACnN,GAAG,EAAE,CAAC,EAAE,CAACyB,GAAG,GAAGE,GAAG,IAAI,CAAC,CAAC,CAAA;CACzC,IAAA,IAAIO,CAAC,GAAGP,GAAG,GAAGF,GAAG,CAAA;KAEjB,IAAIS,CAAC,KAAK,CAAC,EAAE;OACZqJ,CAAC,GAAI4B,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,CAACxL,GAAG,GAAGwL,CAAC,IAAI7O,IAAI,CAACmD,GAAG,CAAC0L,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,CAAA;CAE7D,MAAA,QAAQxL,GAAG;CACV,QAAA,KAAKsK,CAAC;CAAEiC,UAAAA,CAAC,GAAG,CAAChC,CAAC,GAAGC,CAAC,IAAIjK,CAAC,IAAIgK,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;CAAE,UAAA,MAAA;CAC3C,QAAA,KAAKD,CAAC;WAAEgC,CAAC,GAAG,CAAC/B,CAAC,GAAGF,CAAC,IAAI/J,CAAC,GAAG,CAAC,CAAA;CAAE,UAAA,MAAA;CAC7B,QAAA,KAAKiK,CAAC;WAAE+B,CAAC,GAAG,CAACjC,CAAC,GAAGC,CAAC,IAAIhK,CAAC,GAAG,CAAC,CAAA;CAC5B,OAAA;OAEAgM,CAAC,GAAGA,CAAC,GAAG,EAAE,CAAA;CACX,KAAA;;CAEA;CACA;CACA;KACA,IAAI3C,CAAC,GAAG,CAAC,EAAE;CACV2C,MAAAA,CAAC,IAAI,GAAG,CAAA;CACR3C,MAAAA,CAAC,GAAGjN,IAAI,CAACE,GAAG,CAAC+M,CAAC,CAAC,CAAA;CAChB,KAAA;KAEA,IAAI2C,CAAC,IAAI,GAAG,EAAE;CACbA,MAAAA,CAAC,IAAI,GAAG,CAAA;CACT,KAAA;KAEA,OAAO,CAACA,CAAC,EAAE3C,CAAC,GAAG,GAAG,EAAE4B,CAAC,GAAG,GAAG,CAAC,CAAA;IAC5B;CAED;GACAzE,MAAM,EAAE+d,GAAG,IAAI;KACd,IAAI,CAACvY,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,GAAGsZ,GAAG,CAAA;KACnBvY,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAA;KAEX,IAAIA,CAAC,GAAG,CAAC,EAAE;CACVA,MAAAA,CAAC,IAAI,GAAG,CAAA;CACT,KAAA;CAEA3C,IAAAA,CAAC,IAAI,GAAG,CAAA;CACR4B,IAAAA,CAAC,IAAI,GAAG,CAAA;KAER,SAASG,CAACA,CAAE5P,CAAC,EAAE;OACd,IAAI6b,CAAC,GAAG,CAAC7b,CAAC,GAAGwQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAA;CACzB,MAAA,IAAId,CAAC,GAAG7B,CAAC,GAAGjN,IAAI,CAACmD,GAAG,CAAC0L,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,CAAA;OAC9B,OAAOA,CAAC,GAAGC,CAAC,GAAG9O,IAAI,CAACqD,GAAG,CAAC,CAAC,CAAC,EAAErD,IAAI,CAACmD,GAAG,CAAC8X,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;CACvD,KAAA;CAEA,IAAA,OAAO,CAACjM,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACzB;CAEDzF,EAAAA,OAAO,EAAE;CACR,IAAA,KAAK,EAAE;CACN1C,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,cAAc,CAAA;MAC7D;CACD,IAAA,MAAM,EAAE;CACPA,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,cAAc,CAAC;CAC9Dwa,MAAAA,MAAM,EAAE,IAAI;CACZ7X,MAAAA,SAAS,EAAE,IAAA;CACZ,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCvFF;CACA;CACA;CACA;;AAEA,WAAe,IAAInB,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACP+I,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;MACN;CACDkL,IAAAA,CAAC,EAAE;CACFjK,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,YAAA;MACN;CACDmT,IAAAA,CAAC,EAAE;CACFlS,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,OAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAE2kB,GAAG;CACT;GACAje,QAAQA,CAAEge,GAAG,EAAE;KACd,IAAI,CAACvY,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,GAAGsZ,GAAG,CAAA;CACnBlb,IAAAA,CAAC,IAAI,GAAG,CAAA;CACR4B,IAAAA,CAAC,IAAI,GAAG,CAAA;CAER,IAAA,IAAIqG,CAAC,GAAGrG,CAAC,GAAG5B,CAAC,GAAGjN,IAAI,CAACmD,GAAG,CAAC0L,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,CAAA;CAElC,IAAA,OAAO,CACNe,CAAC;CAAE;CACHsF,IAAAA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAGrG,CAAC,GAAGqG,CAAC,CAAC;CAAE;KACjC,GAAG,GAAGA,CAAC,CACP,CAAA;IACD;CACD;GACA9K,MAAMA,CAAEie,GAAG,EAAE;KACZ,IAAI,CAACzY,CAAC,EAAE3C,CAAC,EAAEiI,CAAC,CAAC,GAAGmT,GAAG,CAAA;CAEnBpb,IAAAA,CAAC,IAAI,GAAG,CAAA;CACRiI,IAAAA,CAAC,IAAI,GAAG,CAAA;KAER,IAAIrG,CAAC,GAAGqG,CAAC,IAAI,CAAC,GAAGjI,CAAC,GAAG,CAAC,CAAC,CAAA;CAEvB,IAAA,OAAO,CACN2C,CAAC;CAAE;CACFf,IAAAA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAI,CAACqG,CAAC,GAAGrG,CAAC,IAAI7O,IAAI,CAACmD,GAAG,CAAC0L,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,GAAI,GAAG,EAC/DA,CAAC,GAAG,GAAG,CACP,CAAA;IACD;CAEDtF,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNb,MAAAA,EAAE,EAAE,OAAO;CACXH,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CC7DF;CACA;CACA;CACA;;AAEA,WAAe,IAAIwB,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACP+I,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;MACN;CACDumB,IAAAA,CAAC,EAAE;CACFtlB,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,WAAA;MACN;CACD8L,IAAAA,CAAC,EAAE;CACF7K,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,WAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAE8kB,GAAG;GACTpe,QAAQA,CAAEke,GAAG,EAAE;KACd,IAAI,CAACzY,CAAC,EAAE3C,CAAC,EAAEiI,CAAC,CAAC,GAAGmT,GAAG,CAAA;CAEnB,IAAA,OAAO,CAACzY,CAAC,EAAEsF,CAAC,IAAI,GAAG,GAAGjI,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGiI,CAAC,CAAC,CAAA;IACxC;GACD9K,MAAMA,CAAEoe,GAAG,EAAE;KACZ,IAAI,CAAC5Y,CAAC,EAAE0Y,CAAC,EAAEza,CAAC,CAAC,GAAG2a,GAAG,CAAA;;CAEnB;CACAF,IAAAA,CAAC,IAAI,GAAG,CAAA;CACRza,IAAAA,CAAC,IAAI,GAAG,CAAA;;CAER;CACA,IAAA,IAAIwY,GAAG,GAAGiC,CAAC,GAAGza,CAAC,CAAA;KACf,IAAIwY,GAAG,IAAI,CAAC,EAAE;CACb,MAAA,IAAIoC,IAAI,GAAGH,CAAC,GAAGjC,GAAG,CAAA;OAClB,OAAO,CAACzW,CAAC,EAAE,CAAC,EAAE6Y,IAAI,GAAG,GAAG,CAAC,CAAA;CAC1B,KAAA;CAEA,IAAA,IAAIvT,CAAC,GAAI,CAAC,GAAGrH,CAAE,CAAA;CACf,IAAA,IAAIZ,CAAC,GAAIiI,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,CAAC,GAAGoT,CAAC,GAAGpT,CAAC,CAAA;KACjC,OAAO,CAACtF,CAAC,EAAE3C,CAAC,GAAG,GAAG,EAAEiI,CAAC,GAAG,GAAG,CAAC,CAAA;IAC5B;CAED3L,EAAAA,OAAO,EAAE;CACR,IAAA,KAAK,EAAE;CACN1C,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCvDF;CACA;CACA;CACA;CACA;CACA;CACA,MAAMkH,SAAO,GAAG,CACf,CAAE,kBAAkB,EAAI,kBAAkB,EAAI,kBAAkB,CAAG,EACnE,CAAE,mBAAmB,EAAG,kBAAkB,EAAI,mBAAmB,CAAE,EACnE,CAAE,mBAAmB,EAAG,mBAAmB,EAAG,kBAAkB,CAAG,CACnE,CAAA;CAED,MAAMC,WAAS,GAAG,CACjB,CAAG,kBAAkB,EAAK,CAAC,kBAAkB,EAAI,CAAC,mBAAmB,CAAE,EACvE,CAAE,CAAC,kBAAkB,EAAM,kBAAkB,EAAK,mBAAmB,CAAE,EACvE,CAAG,oBAAoB,EAAG,CAAC,mBAAmB,EAAI,kBAAkB,CAAG,CACvE,CAAA;AAED,iBAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,eAAe;CACnBsC,EAAAA,KAAK,EAAE,kBAAkB;CACzBvH,EAAAA,IAAI,EAAE,iCAAiC;CACvCsI,EAAAA,KAAK,EAAE,KAAK;YACZ0D,SAAO;CACPC,aAAAA,WAAAA;CACD,CAAC,CAAC;;ACxBF,cAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,QAAQ;CACZsC,EAAAA,KAAK,EAAE,SAAS;CAChBvH,EAAAA,IAAI,EAAE,0BAA0B;CAChC0B,EAAAA,IAAI,EAAEilB,SAAS;CACfte,EAAAA,MAAM,EAAEqX,GAAG,IAAIA,GAAG,CAAC3jB,GAAG,CAACsF,GAAG,IAAIpD,IAAI,CAACmP,GAAG,CAACnP,IAAI,CAACE,GAAG,CAACkD,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAGpD,IAAI,CAACuD,IAAI,CAACH,GAAG,CAAC,CAAC;CAClF+G,EAAAA,QAAQ,EAAEsX,GAAG,IAAIA,GAAG,CAAC3jB,GAAG,CAACsF,GAAG,IAAIpD,IAAI,CAACmP,GAAG,CAACnP,IAAI,CAACE,GAAG,CAACkD,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAGpD,IAAI,CAACuD,IAAI,CAACH,GAAG,CAAC,CAAA;CACpF,CAAC,CAAC;;CCPF;CACA;CACA;CACA;CACA,MAAM2K,SAAO,GAAG,CACf,CAAE,mBAAmB,EAAG,mBAAmB,EAAG,mBAAmB,CAAE,EACnE,CAAE,mBAAmB,EAAG,mBAAmB,EAAG,mBAAmB,CAAE,EACnE,CAAE,mBAAmB,EAAG,mBAAmB,EAAG,mBAAmB,CAAE,CACnE,CAAA;CAED,MAAMC,WAAS,GAAG,CACjB,CAAG,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,CAAE,EACpE,CAAE,CAAC,mBAAmB,EAAG,mBAAmB,EAAG,mBAAmB,CAAE,EACpE,CAAG,mBAAmB,EAAG,mBAAmB,EAAG,mBAAmB,CAAE,CACpE,CAAA;AAED,sBAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,iBAAiB;CACrBsC,EAAAA,KAAK,EAAE,uBAAuB;CAC9BvH,EAAAA,IAAI,EAAE,iBAAiB;CACvBsI,EAAAA,KAAK,EAAE,KAAK;CACZ5G,EAAAA,IAAI,EAAEklB,OAAO;YACb5a,SAAO;CACPC,aAAAA,WAAAA;CACD,CAAC,CAAC;;CCxBF,MAAM4a,EAAE,GAAG,CAAC,GAAG,GAAG,CAAA;CAClB,MAAMC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAA;AAEpB,gBAAe,IAAIpb,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,UAAU;CACdsC,EAAAA,KAAK,EAAE,cAAc;CACrBvH,EAAAA,IAAI,EAAE,UAAU;CAChB0B,EAAAA,IAAI,EAAEqlB,cAAc;GACpB1e,MAAMA,CAAEqX,GAAG,EAAE;CACZ;CACA,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAACoX,CAAC,IAAIA,CAAC,GAAG2T,GAAG,GAAG3T,CAAC,GAAG,EAAE,GAAGA,CAAC,IAAI,GAAG,CAAC,CAAA;IAChD;GACD/K,QAAQA,CAAEsX,GAAG,EAAE;KACd,OAAOA,GAAG,CAAC3jB,GAAG,CAACoX,CAAC,IAAIA,CAAC,IAAI0T,EAAE,GAAG1T,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,GAAGA,CAAC,CAAC,CAAA;CACvD,GAAA;CACD,CAAC,CAAC;;ACdF,aAAe,IAAI7M,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,OAAO;CACXjF,EAAAA,IAAI,EAAE,OAAO;CACb8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;CAChBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACDvD,IAAAA,CAAC,EAAE;CACFgJ,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,QAAA;MACN;CACD6N,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;CACP,KAAA;IACA;CACDsI,EAAAA,KAAK,EAAE,KAAK;CAEZ5G,EAAAA,IAAI,EAAEqQ,KAAK;GACX3J,QAAQA,CAAE4J,KAAK,EAAE;CAChB;KACA,IAAI,CAAClE,CAAC,EAAEf,CAAC,EAAEjB,CAAC,CAAC,GAAGkG,KAAK,CAAA;CACrB,IAAA,IAAInE,CAAC,CAAA;CACL,IAAA,MAAMhG,CAAC,GAAG,MAAM,CAAC;;CAEjB,IAAA,IAAI5J,IAAI,CAACE,GAAG,CAAC4O,CAAC,CAAC,GAAGlF,CAAC,IAAI5J,IAAI,CAACE,GAAG,CAAC2N,CAAC,CAAC,GAAGjE,CAAC,EAAE;CACvCgG,MAAAA,CAAC,GAAGlO,GAAG,CAAA;CACR,KAAC,MACI;CACJkO,MAAAA,CAAC,GAAG5P,IAAI,CAAC+P,KAAK,CAAClC,CAAC,EAAEiB,CAAC,CAAC,GAAG,GAAG,GAAG9O,IAAI,CAACS,EAAE,CAAA;CACrC,KAAA;CAEA,IAAA,OAAO,CACNoP,CAAC;CAAE;KACH7P,IAAI,CAACgQ,IAAI,CAAClB,CAAC,IAAI,CAAC,GAAGjB,CAAC,IAAI,CAAC,CAAC;CAAE;KAC5BoC,SAAc,CAACL,CAAC,CAAC;MACjB,CAAA;IACD;CACD;GACAxF,MAAMA,CAAE2e,KAAK,EAAE;KACd,IAAI,CAAClZ,CAAC,EAAEmM,CAAC,EAAEpM,CAAC,CAAC,GAAGmZ,KAAK,CAAA;KACrB,IAAIja,CAAC,EAAEjB,CAAC,CAAA;;CAER;CACA,IAAA,IAAIlO,KAAK,CAACiQ,CAAC,CAAC,EAAE;CACbd,MAAAA,CAAC,GAAG,CAAC,CAAA;CACLjB,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI;CACJiB,MAAAA,CAAC,GAAGkN,CAAC,GAAGhc,IAAI,CAACsQ,GAAG,CAACV,CAAC,GAAG5P,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC,CAAA;CACnCoN,MAAAA,CAAC,GAAGmO,CAAC,GAAGhc,IAAI,CAACuQ,GAAG,CAACX,CAAC,GAAG5P,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC,CAAA;CACpC,KAAA;CAEA,IAAA,OAAO,CAAEoP,CAAC,EAAEf,CAAC,EAAEjB,CAAC,CAAE,CAAA;IAClB;CAEDtE,EAAAA,OAAO,EAAE;CACR,IAAA,OAAO,EAAE;CACR1C,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,8BAA8B,EAAE,oBAAoB,CAAA;CACzF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CC7DF,IAAIwD,KAAK,GAAGxE,MAAM,CAACE,GAAG,CAAA;CAEtB,MAAM6D,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAMgF,GAAC,GAAG,KAAK,GAAG,EAAE,CAAC;CACrB,MAAM,CAACoa,aAAa,EAAEC,aAAa,CAAC,GAAGhD,EAAE,CAAC;CAACtf,EAAAA,KAAK,EAAEmW,OAAO;CAAEjW,EAAAA,MAAM,EAAEwD,KAAAA;CAAK,CAAC,CAAC,CAAA;AAE1E,WAAe,IAAIhC,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,KAAK;CACTjF,EAAAA,IAAI,EAAE,KAAK;CACX8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACD;CACAmnB,IAAAA,CAAC,EAAE;CACF1hB,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;MACpB;CACD0N,IAAAA,CAAC,EAAE;CACF1N,MAAAA,QAAQ,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAA;CACrB,KAAA;IACA;CAED6C,EAAAA,KAAK,EAAEA,KAAK;CACZ5G,EAAAA,IAAI,EAAEqZ,OAAO;CAEb;CACA;GACA3S,QAAQA,CAAE/D,GAAG,EAAE;KACd,IAAIgI,GAAG,GAAG,CAACvO,QAAQ,CAACuG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEvG,QAAQ,CAACuG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEvG,QAAQ,CAACuG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAChE,IAAA,IAAImH,CAAC,GAAGa,GAAG,CAAC,CAAC,CAAC,CAAA;CAEd,IAAA,IAAI,CAAC+a,EAAE,EAAEC,EAAE,CAAC,GAAGnD,EAAE,CAAC;CAACtf,MAAAA,KAAK,EAAEmW,OAAO;CAAEjW,MAAAA,MAAM,EAAEuH,GAAAA;CAAG,KAAC,CAAC,CAAA;;CAEhD;CACA,IAAA,IAAI,CAAC1O,MAAM,CAAC2pB,QAAQ,CAACF,EAAE,CAAC,IAAI,CAACzpB,MAAM,CAAC2pB,QAAQ,CAACD,EAAE,CAAC,EAAE;CACjD,MAAA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACjB,KAAA;CAEA,IAAA,IAAIvZ,CAAC,GAAGtC,CAAC,IAAI3D,GAAC,GAAGgF,GAAC,GAAGrB,CAAC,GAAG,GAAG,GAAGvN,IAAI,CAACiP,IAAI,CAAC1B,CAAC,CAAC,GAAG,EAAE,CAAA;KAChD,OAAO,CACNsC,CAAC,EACD,EAAE,GAAGA,CAAC,IAAIsZ,EAAE,GAAGH,aAAa,CAAC,EAC7B,EAAE,GAAGnZ,CAAC,IAAIuZ,EAAE,GAAGH,aAAa,CAAC,CAC5B,CAAA;IACF;CAED;CACA;GACA7e,MAAMA,CAAEkf,GAAG,EAAE;KACZ,IAAI,CAACzZ,CAAC,EAAEqZ,CAAC,EAAEhU,CAAC,CAAC,GAAGoU,GAAG,CAAA;;CAEnB;KACA,IAAIzZ,CAAC,KAAK,CAAC,IAAIrQ,MAAM,CAACqQ,CAAC,CAAC,EAAE;CACzB,MAAA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACjB,KAAA;CAEAqZ,IAAAA,CAAC,GAAGrpB,QAAQ,CAACqpB,CAAC,CAAC,CAAA;CACfhU,IAAAA,CAAC,GAAGrV,QAAQ,CAACqV,CAAC,CAAC,CAAA;KAEf,IAAIiU,EAAE,GAAID,CAAC,IAAI,EAAE,GAAGrZ,CAAC,CAAC,GAAImZ,aAAa,CAAA;KACvC,IAAII,EAAE,GAAIlU,CAAC,IAAI,EAAE,GAAGrF,CAAC,CAAC,GAAIoZ,aAAa,CAAA;KAEvC,IAAI1b,CAAC,GAAGsC,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAGjB,GAAC,GAAG5O,IAAI,CAACmP,GAAG,CAAC,CAACU,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAA;CAEpD,IAAA,OAAO,CACNtC,CAAC,IAAK,CAAC,GAAG4b,EAAE,IAAK,CAAC,GAAGC,EAAE,CAAC,CAAC,EACzB7b,CAAC,EACDA,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG4b,EAAE,GAAG,EAAE,GAAGC,EAAE,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CACxC,CAAA;IACD;CAED7f,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNb,MAAAA,EAAE,EAAE,OAAO;CACXH,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,+BAA+B,EAAE,+BAA+B,CAAA;CACrG,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;AChFF,aAAe,IAAIwB,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,OAAO;CACXjF,EAAAA,IAAI,EAAE,OAAO;CACb8E,EAAAA,MAAM,EAAE;CACPgI,IAAAA,CAAC,EAAE;CACFrH,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,WAAA;MACN;CACDvD,IAAAA,CAAC,EAAE;CACFgJ,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClBzF,MAAAA,IAAI,EAAE,QAAA;MACN;CACD6N,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAE6lB,GAAG;GACTnf,QAAQA,CAAEmf,GAAG,EAAE;CACd;KACA,IAAI,CAACzZ,CAAC,EAAEqZ,CAAC,EAAEhU,CAAC,CAAC,GAAGoU,GAAG,CAAA;CACnB,IAAA,IAAIxZ,GAAG,CAAA;KACP,MAAMlG,CAAC,GAAG,IAAI,CAAA;CAEd,IAAA,IAAI5J,IAAI,CAACE,GAAG,CAACgpB,CAAC,CAAC,GAAGtf,CAAC,IAAI5J,IAAI,CAACE,GAAG,CAACgV,CAAC,CAAC,GAAGtL,CAAC,EAAE;CACvCkG,MAAAA,GAAG,GAAGpO,GAAG,CAAA;CACV,KAAC,MACI;CACJoO,MAAAA,GAAG,GAAG9P,IAAI,CAAC+P,KAAK,CAACmF,CAAC,EAAEgU,CAAC,CAAC,GAAG,GAAG,GAAGlpB,IAAI,CAACS,EAAE,CAAA;CACvC,KAAA;CAEA,IAAA,OAAO,CACNoP,CAAC;CAAE;KACH7P,IAAI,CAACgQ,IAAI,CAACkZ,CAAC,IAAI,CAAC,GAAGhU,CAAC,IAAI,CAAC,CAAC;CAAE;KAC5BjF,SAAc,CAACH,GAAG,CAAC;MACnB,CAAA;IACD;GACD1F,MAAMA,CAAE8F,GAAG,EAAE;CACZ;KACA,IAAI,CAACC,SAAS,EAAEC,MAAM,EAAEC,GAAG,CAAC,GAAGH,GAAG,CAAA;CAClC;KACA,IAAIE,MAAM,GAAG,CAAC,EAAE;CACfA,MAAAA,MAAM,GAAG,CAAC,CAAA;CACX,KAAA;CACA;CACA,IAAA,IAAIzQ,KAAK,CAAC0Q,GAAG,CAAC,EAAE;CACfA,MAAAA,GAAG,GAAG,CAAC,CAAA;CACR,KAAA;CACA,IAAA,OAAO,CACNF,SAAS;CAAE;CACXC,IAAAA,MAAM,GAAGpQ,IAAI,CAACsQ,GAAG,CAACD,GAAG,GAAGrQ,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;CAAE;CACxC2P,IAAAA,MAAM,GAAGpQ,IAAI,CAACuQ,GAAG,CAACF,GAAG,GAAGrQ,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;MACtC,CAAA;IACD;CAED8I,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNb,MAAAA,EAAE,EAAE,SAAS;CACbH,MAAAA,MAAM,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,oBAAoB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCnEF;CACA;AACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQA,MAAM+C,GAAC,GAAG,GAAG,GAAG,KAAK,CAAC;CACtB,MAAMgF,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;;CAErB,MAAM2a,IAAI,GAAGvb,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAMwb,IAAI,GAAGxb,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAMyb,IAAI,GAAGzb,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAM0b,IAAI,GAAG1b,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAM2b,IAAI,GAAG3b,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAM4b,IAAI,GAAG5b,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAM6b,IAAI,GAAG7b,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAM8b,IAAI,GAAG9b,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B,MAAM+b,IAAI,GAAG/b,WAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CAE5B,SAASgc,uBAAuBA,CAAEC,KAAK,EAAEC,SAAS,EAAE7a,KAAK,EAAE;CAC1D,EAAA,MAAMzL,CAAC,GAAGsmB,SAAS,IAAIlqB,IAAI,CAACuQ,GAAG,CAAClB,KAAK,CAAC,GAAG4a,KAAK,GAAGjqB,IAAI,CAACsQ,GAAG,CAACjB,KAAK,CAAC,CAAC,CAAA;CACjE,EAAA,OAAOzL,CAAC,GAAG,CAAC,GAAG2Z,QAAQ,GAAG3Z,CAAC,CAAA;CAC5B,CAAA;CAEO,SAASumB,sBAAsBA,CAAEtb,CAAC,EAAE;CAC1C,EAAA,MAAMub,IAAI,GAAGpqB,IAAI,CAACmP,GAAG,CAACN,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAA;GAC1C,MAAMwb,IAAI,GAAGD,IAAI,GAAGxgB,GAAC,GAAGwgB,IAAI,GAAGvb,CAAC,GAAGD,CAAC,CAAA;GACpC,MAAM0b,GAAG,GAAGD,IAAI,IAAI,MAAM,GAAGd,IAAI,GAAG,KAAK,GAAGE,IAAI,CAAC,CAAA;CACjD,EAAA,MAAMc,GAAG,GAAGF,IAAI,IAAI,MAAM,GAAGZ,IAAI,GAAG,MAAM,GAAGD,IAAI,GAAG,MAAM,GAAGD,IAAI,CAAC,CAAA;GAClE,MAAMiB,GAAG,GAAGH,IAAI,IAAI,MAAM,GAAGZ,IAAI,GAAG,MAAM,GAAGD,IAAI,CAAC,CAAA;GAClD,MAAMiB,GAAG,GAAGJ,IAAI,IAAI,MAAM,GAAGX,IAAI,GAAG,KAAK,GAAGE,IAAI,CAAC,CAAA;CACjD,EAAA,MAAMc,GAAG,GAAGL,IAAI,IAAI,MAAM,GAAGT,IAAI,GAAG,MAAM,GAAGD,IAAI,GAAG,MAAM,GAAGD,IAAI,CAAC,CAAA;GAClE,MAAMiB,GAAG,GAAGN,IAAI,IAAI,MAAM,GAAGT,IAAI,GAAG,MAAM,GAAGD,IAAI,CAAC,CAAA;GAClD,MAAMiB,GAAG,GAAGP,IAAI,IAAI,MAAM,GAAGR,IAAI,GAAG,KAAK,GAAGE,IAAI,CAAC,CAAA;CACjD,EAAA,MAAMc,GAAG,GAAGR,IAAI,IAAI,MAAM,GAAGN,IAAI,GAAG,MAAM,GAAGD,IAAI,GAAG,MAAM,GAAGD,IAAI,CAAC,CAAA;GAClE,MAAMiB,GAAG,GAAGT,IAAI,IAAI,MAAM,GAAGN,IAAI,GAAG,MAAM,GAAGD,IAAI,CAAC,CAAA;GAElD,OAAO;KACNiB,GAAG,EAAET,GAAG,GAAGE,GAAG;CACdQ,IAAAA,GAAG,EAAET,GAAG,GAAG1b,CAAC,GAAG2b,GAAG;CAClBS,IAAAA,GAAG,EAAEX,GAAG,IAAIE,GAAG,GAAG,MAAM,CAAC;KACzBU,GAAG,EAAE,CAACX,GAAG,GAAG,MAAM,IAAI1b,CAAC,IAAI2b,GAAG,GAAG,MAAM,CAAC;KACxCW,GAAG,EAAEV,GAAG,GAAGE,GAAG;CACdS,IAAAA,GAAG,EAAEV,GAAG,GAAG7b,CAAC,GAAG8b,GAAG;CAClBU,IAAAA,GAAG,EAAEZ,GAAG,IAAIE,GAAG,GAAG,MAAM,CAAC;KACzBW,GAAG,EAAE,CAACZ,GAAG,GAAG,MAAM,IAAI7b,CAAC,IAAI8b,GAAG,GAAG,MAAM,CAAC;KACxCY,GAAG,EAAEX,GAAG,GAAGE,GAAG;CACdU,IAAAA,GAAG,EAAEX,GAAG,GAAGhc,CAAC,GAAGic,GAAG;CAClBW,IAAAA,GAAG,EAAEb,GAAG,IAAIE,GAAG,GAAG,MAAM,CAAC;KACzBY,GAAG,EAAE,CAACb,GAAG,GAAG,MAAM,IAAIhc,CAAC,IAAIic,GAAG,GAAG,MAAM,CAAA;IACvC,CAAA;CACF,CAAA;CAEA,SAASa,kBAAkBA,CAAEC,KAAK,EAAEhc,CAAC,EAAE;GACtC,MAAMic,MAAM,GAAGjc,CAAC,GAAG,GAAG,GAAG5P,IAAI,CAACS,EAAE,GAAG,CAAC,CAAA;CACpC,EAAA,MAAMqrB,EAAE,GAAG9B,uBAAuB,CAAC4B,KAAK,CAACb,GAAG,EAAEa,KAAK,CAACZ,GAAG,EAAEa,MAAM,CAAC,CAAA;CAChE,EAAA,MAAME,EAAE,GAAG/B,uBAAuB,CAAC4B,KAAK,CAACX,GAAG,EAAEW,KAAK,CAACV,GAAG,EAAEW,MAAM,CAAC,CAAA;CAChE,EAAA,MAAMG,EAAE,GAAGhC,uBAAuB,CAAC4B,KAAK,CAACT,GAAG,EAAES,KAAK,CAACR,GAAG,EAAES,MAAM,CAAC,CAAA;CAChE,EAAA,MAAMI,EAAE,GAAGjC,uBAAuB,CAAC4B,KAAK,CAACP,GAAG,EAAEO,KAAK,CAACN,GAAG,EAAEO,MAAM,CAAC,CAAA;CAChE,EAAA,MAAMK,EAAE,GAAGlC,uBAAuB,CAAC4B,KAAK,CAACL,GAAG,EAAEK,KAAK,CAACJ,GAAG,EAAEK,MAAM,CAAC,CAAA;CAChE,EAAA,MAAMza,EAAE,GAAG4Y,uBAAuB,CAAC4B,KAAK,CAACH,GAAG,EAAEG,KAAK,CAACF,GAAG,EAAEG,MAAM,CAAC,CAAA;CAEhE,EAAA,OAAO7rB,IAAI,CAACmD,GAAG,CAAC2oB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE9a,EAAE,CAAC,CAAA;CACxC,CAAA;AAEA,aAAe,IAAI/I,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,OAAO;CACXjF,EAAAA,IAAI,EAAE,OAAO;CACb8E,EAAAA,MAAM,EAAE;CACP+I,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;MACN;CACDkL,IAAAA,CAAC,EAAE;CACFjK,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,YAAA;MACN;CACD8M,IAAAA,CAAC,EAAE;CACF7L,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,WAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAE0oB,KAAK;CACX5hB,EAAAA,UAAU,EAAEgY,IAAI;CAEhB;GACApY,QAAQA,CAAEoH,GAAG,EAAE;CACd,IAAA,IAAI,CAAC1C,CAAC,EAAErQ,CAAC,EAAEoR,CAAC,CAAC,GAAG,CAAC/P,QAAQ,CAAC0R,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE1R,QAAQ,CAAC0R,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE1R,QAAQ,CAAC0R,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CACtE,IAAA,IAAItE,CAAC,CAAA;KAEL,IAAI4B,CAAC,GAAG,UAAU,EAAE;CACnB5B,MAAAA,CAAC,GAAG,CAAC,CAAA;CACL4B,MAAAA,CAAC,GAAG,GAAG,CAAA;CACR,KAAC,MACI,IAAIA,CAAC,GAAG,UAAU,EAAE;CACxB5B,MAAAA,CAAC,GAAG,CAAC,CAAA;CACL4B,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI;CACJ,MAAA,IAAI+c,KAAK,GAAGzB,sBAAsB,CAACtb,CAAC,CAAC,CAAA;CACrC,MAAA,IAAIxL,GAAG,GAAGsoB,kBAAkB,CAACC,KAAK,EAAEhc,CAAC,CAAC,CAAA;CACtC3C,MAAAA,CAAC,GAAGzO,CAAC,GAAG6E,GAAG,GAAG,GAAG,CAAA;CAClB,KAAA;CAEA,IAAA,OAAO,CAACuM,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,CAAA;IAChB;CAED;GACAzE,MAAMA,CAAE+d,GAAG,EAAE;CACZ,IAAA,IAAI,CAACvY,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,GAAG,CAAChP,QAAQ,CAACsoB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEtoB,QAAQ,CAACsoB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEtoB,QAAQ,CAACsoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CACtE,IAAA,IAAI3pB,CAAC,CAAA;KAEL,IAAIqQ,CAAC,GAAG,UAAU,EAAE;CACnBA,MAAAA,CAAC,GAAG,GAAG,CAAA;CACPrQ,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI,IAAIqQ,CAAC,GAAG,UAAU,EAAE;CACxBA,MAAAA,CAAC,GAAG,CAAC,CAAA;CACLrQ,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI;CACJ,MAAA,IAAIotB,KAAK,GAAGzB,sBAAsB,CAACtb,CAAC,CAAC,CAAA;CACrC,MAAA,IAAIxL,GAAG,GAAGsoB,kBAAkB,CAACC,KAAK,EAAEhc,CAAC,CAAC,CAAA;CACtCpR,MAAAA,CAAC,GAAG6E,GAAG,GAAG,GAAG,GAAG4J,CAAC,CAAA;CAClB,KAAA;CAEA,IAAA,OAAO,CAAC4B,CAAC,EAAErQ,CAAC,EAAEoR,CAAC,CAAC,CAAA;IAChB;CAEDrG,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNb,MAAAA,EAAE,EAAE,SAAS;CACbH,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CCjKF;CACA;AACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;;AAWamH,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;AACfA,YAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;CAE5B,SAASoe,kBAAkBA,CAAEnC,KAAK,EAAEC,SAAS,EAAE;GAC9C,OAAOlqB,IAAI,CAACE,GAAG,CAACgqB,SAAS,CAAC,GAAGlqB,IAAI,CAACgQ,IAAI,CAAChQ,IAAI,CAACmP,GAAG,CAAC8a,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;CAC/D,CAAA;CAEA,SAASoC,kBAAkBA,CAAET,KAAK,EAAE;GACnC,IAAIE,EAAE,GAAGM,kBAAkB,CAACR,KAAK,CAACb,GAAG,EAAEa,KAAK,CAACZ,GAAG,CAAC,CAAA;GACjD,IAAIe,EAAE,GAAGK,kBAAkB,CAACR,KAAK,CAACX,GAAG,EAAEW,KAAK,CAACV,GAAG,CAAC,CAAA;GACjD,IAAIc,EAAE,GAAGI,kBAAkB,CAACR,KAAK,CAACT,GAAG,EAAES,KAAK,CAACR,GAAG,CAAC,CAAA;GACjD,IAAIa,EAAE,GAAGG,kBAAkB,CAACR,KAAK,CAACP,GAAG,EAAEO,KAAK,CAACN,GAAG,CAAC,CAAA;GACjD,IAAIY,EAAE,GAAGE,kBAAkB,CAACR,KAAK,CAACL,GAAG,EAAEK,KAAK,CAACJ,GAAG,CAAC,CAAA;GACjD,IAAIpa,EAAE,GAAGgb,kBAAkB,CAACR,KAAK,CAACH,GAAG,EAAEG,KAAK,CAACF,GAAG,CAAC,CAAA;CAEjD,EAAA,OAAO1rB,IAAI,CAACmD,GAAG,CAAC2oB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE9a,EAAE,CAAC,CAAA;CACxC,CAAA;AAEA,aAAe,IAAI/I,UAAU,CAAC;CAC7BrB,EAAAA,EAAE,EAAE,OAAO;CACXjF,EAAAA,IAAI,EAAE,OAAO;CACb8E,EAAAA,MAAM,EAAE;CACP+I,IAAAA,CAAC,EAAE;CACFpI,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CAClB7I,MAAAA,IAAI,EAAE,OAAO;CACboD,MAAAA,IAAI,EAAE,KAAA;MACN;CACDkL,IAAAA,CAAC,EAAE;CACFjK,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,YAAA;MACN;CACD8M,IAAAA,CAAC,EAAE;CACF7L,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;CACfjB,MAAAA,IAAI,EAAE,WAAA;CACP,KAAA;IACA;CAED0B,EAAAA,IAAI,EAAE0oB,KAAK;CACX5hB,EAAAA,UAAU,EAAE,MAAM;CAElB;GACAJ,QAAQA,CAAEoH,GAAG,EAAE;CACd,IAAA,IAAI,CAAC1C,CAAC,EAAErQ,CAAC,EAAEoR,CAAC,CAAC,GAAG,CAAC/P,QAAQ,CAAC0R,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE1R,QAAQ,CAAC0R,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE1R,QAAQ,CAAC0R,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CACtE,IAAA,IAAItE,CAAC,CAAA;KAEL,IAAI4B,CAAC,GAAG,UAAU,EAAE;CACnB5B,MAAAA,CAAC,GAAG,CAAC,CAAA;CACL4B,MAAAA,CAAC,GAAG,GAAG,CAAA;CACR,KAAC,MACI,IAAIA,CAAC,GAAG,UAAU,EAAE;CACxB5B,MAAAA,CAAC,GAAG,CAAC,CAAA;CACL4B,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI;CACJ,MAAA,IAAI+c,KAAK,GAAGzB,sBAAsB,CAACtb,CAAC,CAAC,CAAA;CACrC,MAAA,IAAIxL,GAAG,GAAGgpB,kBAAkB,CAACT,KAAK,CAAC,CAAA;CACnC3e,MAAAA,CAAC,GAAGzO,CAAC,GAAG6E,GAAG,GAAG,GAAG,CAAA;CAClB,KAAA;CACA,IAAA,OAAO,CAACuM,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,CAAA;IAChB;CAED;GACAzE,MAAMA,CAAE+d,GAAG,EAAE;CACZ,IAAA,IAAI,CAACvY,CAAC,EAAE3C,CAAC,EAAE4B,CAAC,CAAC,GAAG,CAAChP,QAAQ,CAACsoB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEtoB,QAAQ,CAACsoB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEtoB,QAAQ,CAACsoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;CACtE,IAAA,IAAI3pB,CAAC,CAAA;KAEL,IAAIqQ,CAAC,GAAG,UAAU,EAAE;CACnBA,MAAAA,CAAC,GAAG,GAAG,CAAA;CACPrQ,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI,IAAIqQ,CAAC,GAAG,UAAU,EAAE;CACxBA,MAAAA,CAAC,GAAG,CAAC,CAAA;CACLrQ,MAAAA,CAAC,GAAG,CAAC,CAAA;CACN,KAAC,MACI;CACJ,MAAA,IAAIotB,KAAK,GAAGzB,sBAAsB,CAACtb,CAAC,CAAC,CAAA;CACrC,MAAA,IAAIxL,GAAG,GAAGgpB,kBAAkB,CAACT,KAAQ,CAAC,CAAA;CACtCptB,MAAAA,CAAC,GAAG6E,GAAG,GAAG,GAAG,GAAG4J,CAAC,CAAA;CAClB,KAAA;CAEA,IAAA,OAAO,CAAC4B,CAAC,EAAErQ,CAAC,EAAEoR,CAAC,CAAC,CAAA;IAChB;CAEDrG,EAAAA,OAAO,EAAE;CACR1B,IAAAA,KAAK,EAAE;CACNb,MAAAA,EAAE,EAAE,SAAS;CACbH,MAAAA,MAAM,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,yBAAyB,CAAA;CACpF,KAAA;CACD,GAAA;CACD,CAAC,CAAC;;CC9HF,MAAMoO,EAAE,GAAG,GAAG,CAAC;CACf,MAAM7V,CAAC,GAAG,IAAI,GAAI,CAAC,IAAI,EAAG,CAAA;CAC1B,MAAMgW,IAAI,GAAI,CAAC,IAAI,EAAE,GAAI,IAAI,CAAA;CAC7B,MAAM1X,CAAC,GAAG,IAAI,GAAI,CAAC,IAAI,CAAE,CAAA;CACzB,MAAM4uB,IAAI,GAAI,CAAC,IAAI,CAAC,GAAI,IAAI,CAAA;CAC5B,MAAM5X,EAAE,GAAG,IAAI,GAAI,CAAC,IAAI,EAAG,CAAA;CAC3B,MAAMC,EAAE,GAAG,IAAI,GAAI,CAAC,IAAI,CAAE,CAAA;CAC1B,MAAMU,EAAE,GAAG,IAAI,GAAI,CAAC,IAAI,CAAE,CAAA;AAE1B,iBAAe,IAAI5H,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,WAAW;CACfsC,EAAAA,KAAK,EAAE,YAAY;CACnBvH,EAAAA,IAAI,EAAE,aAAa;CACnB0B,EAAAA,IAAI,EAAE+d,aAAa;GACnBpX,MAAMA,CAAEqX,GAAG,EAAE;CACZ;CACA;CACA,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;OAC7B,IAAIrF,CAAC,GAAI,CAACiC,IAAI,CAACqD,GAAG,CAAGD,GAAG,IAAIkpB,IAAI,GAAI5X,EAAE,EAAG,CAAC,CAAC,IAAIC,EAAE,GAAIU,EAAE,GAAIjS,GAAG,IAAIkpB,IAAM,CAAC,KAAKlX,IAAK,CAAA;CACnF,MAAA,OAAQrX,CAAC,GAAG,KAAK,GAAGkX,EAAE,CAAE;CACzB,KAAC,CAAC,CAAA;IACF;GACD9K,QAAQA,CAAEsX,GAAG,EAAE;CACd;CACA;CACA,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;CAC7B,MAAA,IAAIrF,CAAC,GAAGiC,IAAI,CAACqD,GAAG,CAACD,GAAG,GAAG6R,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;OACtC,IAAIqB,GAAG,GAAI5B,EAAE,GAAIC,EAAE,GAAI5W,CAAC,IAAIqB,CAAI,CAAA;OAChC,IAAImX,KAAK,GAAI,CAAC,GAAIlB,EAAE,GAAItX,CAAC,IAAIqB,CAAI,CAAA;CAEjC,MAAA,OAAQ,CAACkX,GAAG,GAAGC,KAAK,KAAM7Y,CAAC,CAAA;CAC5B,KAAC,CAAC,CAAA;CACH,GAAA;CACD,CAAC,CAAC;;CCjCF;;CAEA,MAAMoR,CAAC,GAAG,UAAU,CAAA;CACpB,MAAMjB,CAAC,GAAG,UAAU,CAAC;CACrB,MAAMrP,CAAC,GAAG,UAAU,CAAC;;CAErB,MAAM+tB,KAAK,GAAG,MAAM,CAAC;;AAErB,kBAAe,IAAI9e,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,YAAY;CAChBsC,EAAAA,KAAK,EAAE,aAAa;CACpBvH,EAAAA,IAAI,EAAE,cAAc;CACpB4I,EAAAA,QAAQ,EAAE,OAAO;CAEjBlH,EAAAA,IAAI,EAAE+d,aAAa;GACnBpX,MAAMA,CAAEqX,GAAG,EAAE;CACZ;CACA;CACA,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;CAC7B;CACA;CACA;CACA;OACA,IAAIA,GAAG,IAAI,GAAG,EAAE;CACf,QAAA,OAAQA,GAAG,IAAI,CAAC,GAAI,CAAC,GAAGmpB,KAAK,CAAA;CAC9B,OAAA;CACA,MAAA,OAAQ,CAACvsB,IAAI,CAAC0D,GAAG,CAAC,CAACN,GAAG,GAAG5E,CAAC,IAAIsQ,CAAC,CAAC,GAAGjB,CAAC,IAAI,EAAE,GAAI0e,KAAK,CAAA;CACpD,KAAC,CAAC,CAAA;IACF;GACDpiB,QAAQA,CAAEsX,GAAG,EAAE;CACd;CACA;CACA;CACA,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;CAC7B;CACAA,MAAAA,GAAG,IAAImpB,KAAK,CAAA;CACZ;CACA;CACA;CACA,MAAA,IAAInpB,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE;CAClB,QAAA,OAAOpD,IAAI,CAACgQ,IAAI,CAAC,CAAC,GAAG5M,GAAG,CAAC,CAAA;CAC1B,OAAA;CACA,MAAA,OAAO0L,CAAC,GAAG9O,IAAI,CAAC6d,GAAG,CAAC,EAAE,GAAGza,GAAG,GAAGyK,CAAC,CAAC,GAAGrP,CAAC,CAAA;CACtC,KAAC,CAAC,CAAA;CACH,GAAA;CACD,CAAC,CAAC;;CC5CK,MAAMguB,IAAI,GAAG,EAAE,CAAA;CAEtB7nB,KAAK,CAACP,GAAG,CAAC,4BAA4B,EAAEK,GAAG,IAAI;CAC9C,EAAA,IAAIA,GAAG,CAAC4B,OAAO,CAACuY,MAAM,EAAE;CACvBna,IAAAA,GAAG,CAAC8B,CAAC,GAAGN,KAAK,CAACxB,GAAG,CAACyB,EAAE,EAAEzB,GAAG,CAAC0B,EAAE,EAAE1B,GAAG,CAAC4B,OAAO,CAACuY,MAAM,CAAC,CAAA;CAClD,GAAA;CACD,CAAC,CAAC,CAAA;CAEFja,KAAK,CAACP,GAAG,CAAC,0BAA0B,EAAEK,GAAG,IAAI;CAC5C,EAAA,IAAI,CAACA,GAAG,CAAC8B,CAAC,EAAE;CACX9B,IAAAA,GAAG,CAAC8B,CAAC,GAAGN,KAAK,CAACxB,GAAG,CAACyB,EAAE,EAAEzB,GAAG,CAAC0B,EAAE,EAAE1B,GAAG,CAAC4B,OAAO,CAACuY,MAAM,CAAC,CAAA;CAClD,GAAA;CACD,CAAC,CAAC,CAAA;CAEK,SAAS6N,SAASA,CAAAptB,IAAA,EAA8B;GAAA,IAA5B;KAAC2H,EAAE;KAAE0lB,QAAQ;CAAEC,IAAAA,UAAAA;CAAU,GAAC,GAAAttB,IAAA,CAAA;CACpD;CACAmtB,EAAAA,IAAI,CAACxlB,EAAE,CAAC,GAAGjD,SAAS,CAAC,CAAC,CAAC,CAAA;CACxB,CAAA;CAEO,SAASkC,KAAKA,CAAEC,EAAE,EAAEC,EAAE,EAAmB;CAAA,EAAA,IAAjBa,EAAE,GAAAjD,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAG,UAAU,CAAA;CAC7C;CACA;CACA;CACA;CACA,EAAA,IAAI6a,MAAM,GAAG4N,IAAI,CAACxlB,EAAE,CAAC,CAAA;CAErB,EAAA,IAAI,CAAC4lB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGvvB,gBAAgB,CAACqhB,MAAM,CAAC8N,QAAQ,EAAExmB,EAAE,CAAC,CAAA;CACxD,EAAA,IAAI,CAAC6mB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG1vB,gBAAgB,CAACqhB,MAAM,CAAC8N,QAAQ,EAAEvmB,EAAE,CAAC,CAAA;;CAExD;CACA,EAAA,IAAIomB,KAAK,GAAG,CACX,CAACQ,EAAE,GAAGH,EAAE,EAAG,CAAC,EAAS,CAAC,CAAO,EAC7B,CAAC,CAAC,EAASI,EAAE,GAAGH,EAAE,EAAG,CAAC,CAAO,EAC7B,CAAC,CAAC,EAAS,CAAC,EAASI,EAAE,GAAGH,EAAE,CAAC,CAC7B,CAAA;CACD;;GAEA,IAAII,aAAa,GAAG3vB,gBAAgB,CAACgvB,KAAK,EAAE3N,MAAM,CAAC8N,QAAQ,CAAC,CAAA;GAC5D,IAAIS,OAAO,GAAG5vB,gBAAgB,CAACqhB,MAAM,CAAC+N,UAAU,EAAEO,aAAa,CAAC,CAAA;CAChE;CACA,EAAA,OAAOC,OAAO,CAAA;CACf,CAAA;CAEAV,SAAS,CAAC;CACTzlB,EAAAA,EAAE,EAAE,WAAW;GACf0lB,QAAQ,EAAE,CACT,CAAG,SAAS,EAAG,SAAS,EAAE,CAAC,SAAS,CAAE,EACtC,CAAE,CAAC,SAAS,EAAG,SAAS,EAAG,SAAS,CAAE,EACtC,CAAG,SAAS,EAAG,SAAS,EAAG,SAAS,CAAE,CACtC;GACDC,UAAU,EAAE,CACX,CAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAI,mBAAmB,CAAM,EACtE,CAAE,kBAAkB,EAAG,kBAAkB,EAAG,CAAC,uBAAuB,CAAE,EACtE,CAAE,CAAC,EAAoB,CAAC,EAAqB,kBAAkB,CAAO,CAAA;CAExE,CAAC,CAAC,CAAA;CAEFF,SAAS,CAAC;CACTzlB,EAAAA,EAAE,EAAE,UAAU;CACd;CACA;CACA0lB,EAAAA,QAAQ,EAAE,CACT,CAAG,SAAS,EAAG,SAAS,EAAE,CAAC,SAAS,CAAE,EACtC,CAAE,CAAC,SAAS,EAAG,SAAS,EAAG,SAAS,CAAE,EACtC,CAAG,SAAS,EAAE,CAAC,SAAS,EAAG,SAAS,CAAE,CACtC;CACD;GACAC,UAAU,EAAE,CACX,CAAG,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAG,EACnE,CAAG,kBAAkB,EAAG,kBAAkB,EAAG,oBAAoB,CAAE,EACnE,CAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,CAAM,CAAA;CAErE,CAAC,CAAC,CAAA;CAEFF,SAAS,CAAC;CACTzlB,EAAAA,EAAE,EAAE,OAAO;CACX;GACA0lB,QAAQ,EAAE,CACT,CAAG,SAAS,EAAG,SAAS,EAAE,CAAC,SAAS,CAAE,EACtC,CAAE,CAAC,SAAS,EAAG,SAAS,EAAG,SAAS,CAAE,EACtC,CAAG,SAAS,EAAG,SAAS,EAAG,SAAS,CAAE,CACtC;GACDC,UAAU,EAAE,CACX,CAAG,kBAAkB,EAAI,CAAC,mBAAmB,EAAE,mBAAmB,CAAE,EACpE,CAAG,kBAAkB,EAAK,kBAAkB,EAAG,mBAAmB,CAAE,EACpE,CAAE,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAG,CAAA;CAEtE,CAAC,CAAC,CAAA;CAEFF,SAAS,CAAC;CACTzlB,EAAAA,EAAE,EAAE,OAAO;CACX0lB,EAAAA,QAAQ,EAAE,CACT,CAAG,QAAQ,EAAG,QAAQ,EAAE,CAAC,QAAQ,CAAE,EACnC,CAAE,CAAC,QAAQ,EAAG,QAAQ,EAAG,QAAQ,CAAE,EACnC,CAAE,CAAC,QAAQ,EAAG,QAAQ,EAAG,QAAQ,CAAE,CACnC;CACD;CACAC,EAAAA,UAAU,EAAE,CACX,CAAG,iBAAiB,EAAI,CAAC,kBAAkB,EAAG,mBAAmB,CAAG,EACpE,CAAG,kBAAkB,EAAI,kBAAkB,EAAE,CAAC,oBAAoB,CAAE,EACpE,CAAE,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,CAAI,CAAA;CAEtE,CAAC,CAAC,CAAA;CAEF9tB,MAAM,CAACiK,MAAM,CAACjD,MAAM,EAAE;CACrB;CACA;CACA;CACArI,EAAAA,CAAC,EAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CAEhC;CACAwe,EAAAA,CAAC,EAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;CAEjC;CACA;CACA;CACAoR,EAAAA,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CAChCC,EAAAA,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CAEhC;CACA1M,EAAAA,CAAC,EAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CAEhC;CACA2M,EAAAA,EAAE,EAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CAChCC,EAAAA,EAAE,EAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;CAChCC,EAAAA,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAA;CAChC,CAAC,CAAC;;CC9HF;CACA;CACA;CACA;CACA3nB,MAAM,CAAC4nB,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,CAAA;;CAEnF;CACA,MAAM1f,OAAO,GAAG,CACf,CAAG,kBAAkB,EAAI,mBAAmB,EAAG,kBAAkB,CAAG,EACpE,CAAG,mBAAmB,EAAG,kBAAkB,EAAI,mBAAmB,CAAE,EACpE,CAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,CAAG,CACpE,CAAA;CACD,MAAMC,SAAS,GAAG,CACjB,CAAG,kBAAkB,EAAI,CAAC,gBAAgB,EAAK,CAAC,mBAAmB,CAAG,EACtE,CAAE,CAAC,kBAAkB,EAAK,kBAAkB,EAAI,oBAAoB,CAAE,EACtE,CAAG,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAI,CACtE,CAAA;AAED,cAAe,IAAIP,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,QAAQ;CACZsC,EAAAA,KAAK,EAAE,UAAU;CACjBvH,EAAAA,IAAI,EAAE,QAAQ;CAEd;CACA;CACA;CACA8E,EAAAA,MAAM,EAAE;CACP8G,IAAAA,CAAC,EAAE;CACF3K,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACjBjB,MAAAA,IAAI,EAAE,KAAA;MACN;CACD6L,IAAAA,CAAC,EAAE;CACF5K,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACjBjB,MAAAA,IAAI,EAAE,OAAA;MACN;CACD8L,IAAAA,CAAC,EAAE;CACF7K,MAAAA,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;CACjBjB,MAAAA,IAAI,EAAE,MAAA;CACP,KAAA;IACA;CAED4I,EAAAA,QAAQ,EAAE,OAAO;GAEjBN,KAAK,EAAExE,MAAM,CAAC4nB,IAAI;GAElB1f,OAAO;CACPC,EAAAA,SAAAA;CACD,CAAC,CAAC,CAAA;;CAEF;;CCjDA,MAAMpE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;;CAElB;CACA;CACA,MAAM8jB,gBAAgB,GAAG,CAAC,UAAU,CAAA;;CAEpC;CACA,MAAMC,WAAW,GAAG,CAAC3tB,IAAI,CAAC4tB,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;;AAEtD,cAAe,IAAIngB,aAAa,CAAC;CAChCzG,EAAAA,EAAE,EAAE,QAAQ;CACZsC,EAAAA,KAAK,EAAE,UAAU;CACjBvH,EAAAA,IAAI,EAAE,QAAQ;CACd;CACA;CACA;;CAEA;CACA;CACA;CACA;CACA8E,EAAAA,MAAM,EAAE;CACP8G,IAAAA,CAAC,EAAE;CACF3K,MAAAA,KAAK,EAAE,CAAC0qB,gBAAgB,EAAEC,WAAW,CAAC;CACtC5rB,MAAAA,IAAI,EAAE,KAAA;MACN;CACD6L,IAAAA,CAAC,EAAE;CACF5K,MAAAA,KAAK,EAAE,CAAC0qB,gBAAgB,EAAEC,WAAW,CAAC;CACtC5rB,MAAAA,IAAI,EAAE,OAAA;MACN;CACD8L,IAAAA,CAAC,EAAE;CACF7K,MAAAA,KAAK,EAAE,CAAC0qB,gBAAgB,EAAEC,WAAW,CAAC;CACtC5rB,MAAAA,IAAI,EAAE,MAAA;CACP,KAAA;IACA;CACD4I,EAAAA,QAAQ,EAAE,OAAO;CAEjBlH,EAAAA,IAAI,EAAEoqB,MAAM;CACZ;GACAzjB,MAAMA,CAAEqX,GAAG,EAAE;KACZ,MAAM9B,GAAG,GAAG,CAAC,IAAI,GAAG,EAAE,IAAI,KAAK,CAAC;;CAEhC,IAAA,OAAO8B,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;OAC7B,IAAIA,GAAG,IAAIuc,GAAG,EAAE;CACf,QAAA,OAAO,CAAC,CAAC,KAAMvc,GAAG,GAAG,KAAK,GAAI,IAAI,CAAC,GAAGwG,CAAC,IAAI,CAAC,CAAC;CAC9C,OAAC,MACI,IAAIxG,GAAG,GAAGuqB,WAAW,EAAE;CAC3B,QAAA,OAAO,CAAC,KAAMvqB,GAAG,GAAG,KAAK,GAAI,IAAI,CAAC,CAAA;CACnC,OAAC,MACI;CAAE;CACN,QAAA,OAAO,KAAK,CAAA;CACb,OAAA;CACD,KAAC,CAAC,CAAA;IACF;CAED;GACA+G,QAAQA,CAAEsX,GAAG,EAAE;CACd,IAAA,OAAOA,GAAG,CAAC3jB,GAAG,CAAC,UAAUsF,GAAG,EAAE;OAC7B,IAAIA,GAAG,IAAI,CAAC,EAAE;CACb,QAAA,OAAO,CAACpD,IAAI,CAAC4tB,IAAI,CAAChkB,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;CACtC,OAAC,MACI,IAAIxG,GAAG,GAAGwG,CAAC,EAAE;CACjB,QAAA,OAAQ,CAAC5J,IAAI,CAAC4tB,IAAI,CAAChkB,CAAC,GAAGxG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,CAAA;CAClD,OAAC,MACI;CAAE;SACN,OAAQ,CAACpD,IAAI,CAAC4tB,IAAI,CAACxqB,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,CAAA;CACxC,OAAA;CACD,KAAC,CAAC,CAAA;CACH,GAAA;CACA;CACA;CACD,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CClDF;CACA;CACA;CACe,MAAMigB,KAAK,CAAC;CAC1B;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACCxZ,EAAAA,WAAWA,GAAW;CACrB,IAAA,IAAIhC,KAAK,CAAA;CAAC,IAAA,KAAA,IAAAuE,IAAA,GAAArI,SAAA,CAAApG,MAAA,EADKuD,IAAI,GAAAtD,IAAAA,KAAA,CAAAwO,IAAA,GAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;CAAJpL,MAAAA,IAAI,CAAAoL,IAAA,CAAAvI,GAAAA,SAAA,CAAAuI,IAAA,CAAA,CAAA;CAAA,KAAA;CAGnB,IAAA,IAAIpL,IAAI,CAACvD,MAAM,KAAK,CAAC,EAAE;CACtBkK,MAAAA,KAAK,GAAG6B,QAAQ,CAACxI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;CAC1B,KAAA;CAEA,IAAA,IAAIyF,KAAK,EAAEE,MAAM,EAAEjF,KAAK,CAAA;CAExB,IAAA,IAAIiG,KAAK,EAAE;CACVlB,MAAAA,KAAK,GAAGkB,KAAK,CAAClB,KAAK,IAAIkB,KAAK,CAACoB,OAAO,CAAA;OACpCpC,MAAM,GAAGgB,KAAK,CAAChB,MAAM,CAAA;OACrBjF,KAAK,GAAGiG,KAAK,CAACjG,KAAK,CAAA;CACpB,KAAC,MACI;CACJ;CACA,MAAA,CAAC+E,KAAK,EAAEE,MAAM,EAAEjF,KAAK,CAAC,GAAGV,IAAI,CAAA;CAC9B,KAAA;CAEArC,IAAAA,MAAM,CAAC+L,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;CACpCpI,MAAAA,KAAK,EAAE6F,UAAU,CAACsB,GAAG,CAAChD,KAAK,CAAC;CAC5BoE,MAAAA,QAAQ,EAAE,KAAK;CACfC,MAAAA,UAAU,EAAE,IAAI;OAChBC,YAAY,EAAE,IAAI;CACnB,KAAC,CAAC,CAAA;CAEF,IAAA,IAAI,CAACpE,MAAM,GAAGA,MAAM,GAAGA,MAAM,CAACrF,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;;CAEjD;CACA,IAAA,IAAI,CAACI,KAAK,GAAGA,KAAK,GAAG,CAAC,IAAIA,KAAK,KAAKoC,SAAS,GAAG,CAAC,GAAIpC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAM,CAAA;;CAE3E;CACA,IAAA,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC0I,MAAM,CAAClJ,MAAM,EAAEQ,CAAC,EAAE,EAAE;OAC5C,IAAI,IAAI,CAAC0I,MAAM,CAAC1I,CAAC,CAAC,KAAK,KAAK,EAAE;CAC7B,QAAA,IAAI,CAAC0I,MAAM,CAAC1I,CAAC,CAAC,GAAGuD,GAAG,CAAA;CACrB,OAAA;CACD,KAAA;;CAEA;KACA,KAAK,IAAIsF,EAAE,IAAI,IAAI,CAACL,KAAK,CAACE,MAAM,EAAE;CACjChI,MAAAA,MAAM,CAAC+L,cAAc,CAAC,IAAI,EAAE5D,EAAE,EAAE;SAC/B2C,GAAG,EAAEA,MAAM,IAAI,CAACA,GAAG,CAAC3C,EAAE,CAAC;SACvByH,GAAG,EAAEjM,KAAK,IAAI,IAAI,CAACiM,GAAG,CAACzH,EAAE,EAAExE,KAAK,CAAA;CACjC,OAAC,CAAC,CAAA;CACH,KAAA;CACD,GAAA;GAEA,IAAIyG,OAAOA,GAAI;CACd,IAAA,OAAO,IAAI,CAACtC,KAAK,CAACK,EAAE,CAAA;CACrB,GAAA;CAEAkN,EAAAA,KAAKA,GAAI;CACR,IAAA,OAAO,IAAImP,KAAK,CAAC,IAAI,CAAC1c,KAAK,EAAE,IAAI,CAACE,MAAM,EAAE,IAAI,CAACjF,KAAK,CAAC,CAAA;CACtD,GAAA;CAEAksB,EAAAA,MAAMA,GAAI;KACT,OAAO;OACN7kB,OAAO,EAAE,IAAI,CAACA,OAAO;OACrBpC,MAAM,EAAE,IAAI,CAACA,MAAM;OACnBjF,KAAK,EAAE,IAAI,CAACA,KAAAA;MACZ,CAAA;CACF,GAAA;CAEAkhB,EAAAA,OAAOA,GAAW;CAAA,IAAA,KAAA,IAAAiL,KAAA,GAAAhqB,SAAA,CAAApG,MAAA,EAANuD,IAAI,GAAAtD,IAAAA,KAAA,CAAAmwB,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;CAAJ9sB,MAAAA,IAAI,CAAA8sB,KAAA,CAAAjqB,GAAAA,SAAA,CAAAiqB,KAAA,CAAA,CAAA;CAAA,KAAA;KACf,IAAIzvB,GAAG,GAAGukB,OAAO,CAAC,IAAI,EAAE,GAAG5hB,IAAI,CAAC,CAAA;;CAEhC;KACA3C,GAAG,CAACsJ,KAAK,GAAG,IAAIwb,KAAK,CAAC9kB,GAAG,CAACsJ,KAAK,CAAC,CAAA;CAEhC,IAAA,OAAOtJ,GAAG,CAAA;CACX,GAAA;;CAEA;CACD;CACA;CACA;GACC,OAAOoL,GAAGA,CAAE9B,KAAK,EAAW;KAC3B,IAAIA,KAAK,YAAYwb,KAAK,EAAE;CAC3B,MAAA,OAAOxb,KAAK,CAAA;CACb,KAAA;KAAC,KAAAomB,IAAAA,KAAA,GAAAlqB,SAAA,CAAApG,MAAA,EAHoBuD,IAAI,OAAAtD,KAAA,CAAAqwB,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;CAAJhtB,MAAAA,IAAI,CAAAgtB,KAAA,GAAAnqB,CAAAA,CAAAA,GAAAA,SAAA,CAAAmqB,KAAA,CAAA,CAAA;CAAA,KAAA;CAKzB,IAAA,OAAO,IAAI7K,KAAK,CAACxb,KAAK,EAAE,GAAG3G,IAAI,CAAC,CAAA;CACjC,GAAA;CAEA,EAAA,OAAOgnB,cAAcA,CAAEnmB,IAAI,EAAEosB,IAAI,EAAY;CAAA,IAAA,IAAVvvB,CAAC,GAAAmF,SAAA,CAAApG,MAAA,GAAA,CAAA,IAAAoG,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAGoqB,IAAI,CAAA;KAC1C,IAAI;CAACC,MAAAA,QAAQ,GAAG,IAAI;CAAE5f,MAAAA,OAAAA;CAAO,KAAC,GAAG5P,CAAC,CAAA;CAElC,IAAA,IAAIyvB,IAAI,GAAG,YAAmB;CAC7B,MAAA,IAAI9vB,GAAG,GAAG4vB,IAAI,CAAC,GAAApqB,SAAO,CAAC,CAAA;OAEvB,IAAIyK,OAAO,KAAK,OAAO,EAAE;CACxBjQ,QAAAA,GAAG,GAAG8kB,KAAK,CAAC1Z,GAAG,CAACpL,GAAG,CAAC,CAAA;CACrB,OAAC,MACI,IAAIiQ,OAAO,KAAK,iBAAiB,EAAE;SACvC,IAAIQ,CAAC,GAAGzQ,GAAG,CAAA;SACXA,GAAG,GAAG,YAAmB;CACxB,UAAA,IAAIA,GAAG,GAAGyQ,CAAC,CAAC,GAAAjL,SAAO,CAAC,CAAA;CACpB,UAAA,OAAOsf,KAAK,CAAC1Z,GAAG,CAACpL,GAAG,CAAC,CAAA;UACrB,CAAA;CACD;CACAM,QAAAA,MAAM,CAACiK,MAAM,CAACvK,GAAG,EAAEyQ,CAAC,CAAC,CAAA;CACtB,OAAC,MACI,IAAIR,OAAO,KAAK,cAAc,EAAE;CACpCjQ,QAAAA,GAAG,GAAGA,GAAG,CAACT,GAAG,CAACU,CAAC,IAAI6kB,KAAK,CAAC1Z,GAAG,CAACnL,CAAC,CAAC,CAAC,CAAA;CACjC,OAAA;CAEA,MAAA,OAAOD,GAAG,CAAA;MACV,CAAA;CAED,IAAA,IAAI,EAAEwD,IAAI,IAAIshB,KAAK,CAAC,EAAE;CACrBA,MAAAA,KAAK,CAACthB,IAAI,CAAC,GAAGssB,IAAI,CAAA;CACnB,KAAA;CAEA,IAAA,IAAID,QAAQ,EAAE;CACb/K,MAAAA,KAAK,CAACvkB,SAAS,CAACiD,IAAI,CAAC,GAAG,YAAmB;CAAA,QAAA,KAAA,IAAAusB,KAAA,GAAAvqB,SAAA,CAAApG,MAAA,EAANuD,IAAI,GAAAtD,IAAAA,KAAA,CAAA0wB,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;CAAJrtB,UAAAA,IAAI,CAAAqtB,KAAA,CAAAxqB,GAAAA,SAAA,CAAAwqB,KAAA,CAAA,CAAA;CAAA,SAAA;CACxC,QAAA,OAAOF,IAAI,CAAC,IAAI,EAAE,GAAGntB,IAAI,CAAC,CAAA;QAC1B,CAAA;CACF,KAAA;CACD,GAAA;GAEA,OAAOstB,eAAeA,CAAE5vB,CAAC,EAAE;CAC1B,IAAA,KAAK,IAAImD,IAAI,IAAInD,CAAC,EAAE;CACnBykB,MAAAA,KAAK,CAAC6E,cAAc,CAACnmB,IAAI,EAAEnD,CAAC,CAACmD,IAAI,CAAC,EAAEnD,CAAC,CAACmD,IAAI,CAAC,CAAC,CAAA;CAC7C,KAAA;CACD,GAAA;GAEA,OAAO0sB,MAAMA,CAAEC,OAAO,EAAE;KACvB,IAAIA,OAAO,CAACziB,QAAQ,EAAE;CACrByiB,MAAAA,OAAO,CAACziB,QAAQ,CAACoX,KAAK,CAAC,CAAA;CACxB,KAAC,MACI;CACJ;CACA,MAAA,KAAK,IAAIthB,IAAI,IAAI2sB,OAAO,EAAE;SACzBrL,KAAK,CAAC6E,cAAc,CAACnmB,IAAI,EAAE2sB,OAAO,CAAC3sB,IAAI,CAAC,CAAC,CAAA;CAC1C,OAAA;CACD,KAAA;CACD,GAAA;CACD,CAAA;CAEAshB,KAAK,CAACmL,eAAe,CAAC;GACrB7kB,GAAG;GACH0E,MAAM;GACNI,GAAG;GACHF,MAAM;GACN5L,EAAE;GACFwI,MAAM;GACNT,OAAO;GACPsU,OAAO;GACP7K,QAAQ;CACRpV,EAAAA,QAAQ,EAAE8hB,SAAAA;CACX,CAAC,CAAC,CAAA;CAEFhiB,MAAM,CAACiK,MAAM,CAACua,KAAK,EAAE;GACpB5b,IAAI;GACJ9C,KAAK;GACLkB,MAAM;CACN8oB,EAAAA,KAAK,EAAEtmB,UAAU;GACjBumB,MAAM,EAAEvmB,UAAU,CAACe,QAAQ;GAC3B1B,KAAK;CAEL;CACAsB,EAAAA,QAAAA;CACD,CAAC,CAAC;;CCnMF,KAAK,IAAI6lB,GAAG,IAAIhwB,MAAM,CAACgK,IAAI,CAAC+lB,MAAM,CAAC,EAAE;CACpCvmB,EAAAA,UAAU,CAAC4D,QAAQ,CAAC2iB,MAAM,CAACC,GAAG,CAAC,CAAC,CAAA;CACjC;;;;;;;;;;CCNA,CAAA,IAAI,cAAc,GAAGjyB,2BAA8C,EAAA,CAAC,CAAC,CAAC;EACtE,IAAI,MAAM,GAAGC,qBAAA,EAAwC,CAAC;EACtD,IAAI,eAAe,GAAGC,sBAAA,EAAyC,CAAC;AAChE;CACA,CAAA,IAAI,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;AACnD;CACA,CAAA,cAAc,GAAG,UAAU,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;IAC9C,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;IACjD,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;CAChD,KAAI,cAAc,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;KAC3E;GACF,CAAA;;;;;;;;;ECXD,IAAI,CAAC,GAAGF,cAAA,EAA8B,CAAC;EACvC,IAAI,MAAM,GAAGC,aAAA,EAA8B,CAAC;EAC5C,IAAI,cAAc,GAAGC,qBAAA,EAAyC,CAAC;AAC/D;CACA,CAAA,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;AACrC;CACA;CACA;EACA,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;;;;;;CCD/C;CACA,KAAK,IAAIkK,EAAE,IAAIqB,UAAU,CAACe,QAAQ,EAAE;GACnC0lB,iBAAiB,CAAC9nB,EAAE,EAAEqB,UAAU,CAACe,QAAQ,CAACpC,EAAE,CAAC,CAAC,CAAA;CAC/C,CAAA;;CAEA;CACArC,KAAK,CAACP,GAAG,CAAC,qBAAqB,EAAEuC,KAAK,IAAI;CAAA,EAAA,IAAAooB,cAAA,CAAA;CACzCD,EAAAA,iBAAiB,CAACnoB,KAAK,CAACK,EAAE,EAAEL,KAAK,CAAC,CAAA;CAClC,EAAA,CAAAooB,cAAA,GAAApoB,KAAK,CAACuD,OAAO,MAAA,IAAA,IAAA6kB,cAAA,KAAA,KAAA,CAAA,IAAbA,cAAA,CAAexqB,OAAO,CAAC2H,KAAK,IAAI;CAC/B4iB,IAAAA,iBAAiB,CAAC5iB,KAAK,EAAEvF,KAAK,CAAC,CAAA;CAChC,GAAC,CAAC,CAAA;CACH,CAAC,CAAC,CAAA;CAEF,SAASmoB,iBAAiBA,CAAE9nB,EAAE,EAAEL,KAAK,EAAE;GACtC,IAAIqoB,MAAM,GAAGhoB,EAAE,CAAC7F,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;GAElCtC,MAAM,CAAC+L,cAAc,CAACyY,KAAK,CAACvkB,SAAS,EAAEkwB,MAAM,EAAE;CAC9C;CACA;CACA;CACArlB,IAAAA,GAAGA,GAAI;CACN,MAAA,IAAIpL,GAAG,GAAG,IAAI,CAAC8P,MAAM,CAACrH,EAAE,CAAC,CAAA;CAEzB,MAAA,IAAI,OAAOioB,KAAK,KAAK,WAAW,EAAE;CACjC;CACA,QAAA,OAAO1wB,GAAG,CAAA;CACX,OAAA;;CAEA;CACA,MAAA,OAAO,IAAI0wB,KAAK,CAAC1wB,GAAG,EAAE;CACrB6I,QAAAA,GAAG,EAAEA,CAAC8nB,GAAG,EAAEC,QAAQ,KAAK;WACvB,IAAI;aACH9mB,UAAU,CAACkE,YAAY,CAAC,CAAC5F,KAAK,EAAEwoB,QAAQ,CAAC,CAAC,CAAA;CAC1C,YAAA,OAAO,IAAI,CAAA;CACZ,WAAC,CACD,OAAOjW,CAAC,EAAE,EAAC;CAEX,UAAA,OAAOkW,OAAO,CAAChoB,GAAG,CAAC8nB,GAAG,EAAEC,QAAQ,CAAC,CAAA;UACjC;CACDxlB,QAAAA,GAAG,EAAEA,CAACulB,GAAG,EAAEC,QAAQ,EAAEE,QAAQ,KAAK;CACjC,UAAA,IAAIF,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,EAAEA,QAAQ,IAAID,GAAG,CAAC,EAAE;aACnE,IAAI;CAACtiB,cAAAA,KAAAA;cAAM,GAAGvE,UAAU,CAACkE,YAAY,CAAC,CAAC5F,KAAK,EAAEwoB,QAAQ,CAAC,CAAC,CAAA;aAExD,IAAIviB,KAAK,IAAI,CAAC,EAAE;eACf,OAAOsiB,GAAG,CAACtiB,KAAK,CAAC,CAAA;CAClB,aAAA;CACD,WAAA;WAEA,OAAOwiB,OAAO,CAACzlB,GAAG,CAACulB,GAAG,EAAEC,QAAQ,EAAEE,QAAQ,CAAC,CAAA;UAC3C;SACD5gB,GAAG,EAAEA,CAACygB,GAAG,EAAEC,QAAQ,EAAE3sB,KAAK,EAAE6sB,QAAQ,KAAK;CACxC,UAAA,IAAIF,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,EAAEA,QAAQ,IAAID,GAAG,CAAC,IAAIC,QAAQ,IAAI,CAAC,EAAE;aACpF,IAAI;CAACviB,cAAAA,KAAAA;cAAM,GAAGvE,UAAU,CAACkE,YAAY,CAAC,CAAC5F,KAAK,EAAEwoB,QAAQ,CAAC,CAAC,CAAA;aAExD,IAAIviB,KAAK,IAAI,CAAC,EAAE;CACfsiB,cAAAA,GAAG,CAACtiB,KAAK,CAAC,GAAGpK,KAAK,CAAA;;CAElB;CACA,cAAA,IAAI,CAAC+L,MAAM,CAACvH,EAAE,EAAEkoB,GAAG,CAAC,CAAA;CAEpB,cAAA,OAAO,IAAI,CAAA;CACZ,aAAA;CACD,WAAA;WAEA,OAAOE,OAAO,CAAC3gB,GAAG,CAACygB,GAAG,EAAEC,QAAQ,EAAE3sB,KAAK,EAAE6sB,QAAQ,CAAC,CAAA;CACnD,SAAA;CACD,OAAC,CAAC,CAAA;MACF;CACD;CACA;CACA;KACA5gB,GAAGA,CAAE5H,MAAM,EAAE;CACZ,MAAA,IAAI,CAAC0H,MAAM,CAACvH,EAAE,EAAEH,MAAM,CAAC,CAAA;MACvB;CACDoE,IAAAA,YAAY,EAAE,IAAI;CAClBD,IAAAA,UAAU,EAAE,IAAA;CACb,GAAC,CAAC,CAAA;CACH;;CCrFA;CAUAqY,KAAK,CAACoL,MAAM,CAACpP,aAAa,CAAC,CAAA;CAC3BgE,KAAK,CAACoL,MAAM,CAAC;CAAC5pB,EAAAA,MAAAA;CAAM,CAAC,CAAC,CAAA;CACtBhG,MAAM,CAACiK,MAAM,CAACua,KAAK,EAAE;CAAChE,EAAAA,aAAAA;CAAa,CAAC,CAAC,CAAA;CAIrCgE,KAAK,CAACoL,MAAM,CAACa,UAAU,CAAC,CAAA;CAGxBjM,KAAK,CAACoL,MAAM,CAAC;CAAC9I,EAAAA,QAAAA;CAAQ,CAAC,CAAC,CAAA;CAGxBtC,KAAK,CAACoL,MAAM,CAACc,YAAY,CAAC,CAAA;CAG1BlM,KAAK,CAACoL,MAAM,CAACe,SAAS,CAAC,CAAA;CAGvBnM,KAAK,CAACoL,MAAM,CAACgB,aAAa,CAAC,CAAA;CAG3BpM,KAAK,CAACoL,MAAM,CAACiB,eAAe,CAAC;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 124, 125, 168, 169]}