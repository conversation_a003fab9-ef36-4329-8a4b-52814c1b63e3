// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
export * from "./types.js";
export * from "./is-message.js";
export * from "./create.js";
export * from "./clone.js";
export * from "./descriptors.js";
export * from "./equals.js";
export * from "./fields.js";
export * from "./registry.js";
export { toBinary } from "./to-binary.js";
export { fromBinary, mergeFromBinary } from "./from-binary.js";
export * from "./to-json.js";
export * from "./from-json.js";
export * from "./merge.js";
export { hasExtension, getExtension, setExtension, clearExtension, hasOption, getOption, } from "./extensions.js";
export * from "./proto-int64.js";
