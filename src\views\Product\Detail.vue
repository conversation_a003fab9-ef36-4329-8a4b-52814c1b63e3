<template>
  <div>
    <!-- 顶部Banner -->
    <div class="banner-container"
      style="position: relative; background-image: url('/images/pp1.jpg');background-repeat: no-repeat; background-size:auto; background-position: center;">
      <div class="container" style="position: relative; ">
        <div style="position: absolute;left: 60%;top:200px">
          <h2 style="color: #222; font-size: 3rem; font-weight: bold;font-style: italic; margin-bottom: 12px;">方草捆集垛机</h2>
          <p style="color: #222; font-size: 1.4rem;">操作方便&#160;&#160;&#160;&#160;  经济高效</p>
          <div class="mt-5 font-light">
            提升带强压功能，机具入土轻松，作业效率高，配置2组液压输出，满足多样化机具作业；
          </div>
          <div style="margin-top: 24px;">
            <button class="btn" style="background: #f14d4d; color: #fff; margin-right: 16px;">获取报价</button>
            <!-- <button class="btn" style="background: #222; color: #fff;">产品对比</button> -->
          </div>
        </div>
        <div style="position: absolute;left: 5%;top:120px">
          <img src="/images/pp1_3.png" alt="产品图片" style="width: 600px; height: auto;mix-blend-mode: darken;"></img>
        </div>
      </div>
    </div>


    <div style="background-color: #fff;">
      <!-- 产品详情内容（全部用图片占位） -->
      <section class="container" style="padding: 48px;">
        <a name="js"></a>
        <div class="flex-around" style="margin-bottom: 32px;">
          <img src="/images/pp1_1.png" alt="产品特点"
            style=" object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px #0001;">
        </div>

        <a name="td"></a>
        <div class="section-title" style="">
          产品特点
        </div>

        <div>

          <img src="/images/pp11.jpg" alt="产品特点" style=" object-fit: cover; border-radius: 8px; ">
        </div>

        <a name="cs"></a>
        <div class="section-title" style="">
          产品参数
        </div>

        <div class="flex-around" style="margin-bottom: 32px;">
          <img src="/images/pp1_4.png" alt="产品参数"
            style=" object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px #0001;">
        </div>

      </section>


      <!-- 相关产品 -->

      <section style="background: #f6f6f6; padding: 48px 0;">
        <div class="container">
          <h3 class="section-title" style="margin-bottom: 32px;">相关产品</h3>
          <div></div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
function scrollToAnchor(name: string) {
  document.querySelector("a[name=" + name + "]")?.scrollIntoView({ behavior: "smooth" })
}
// 暂无逻辑
</script>

<style scoped>
.container {
  background-color: #fff;

}

.banner-container {
  height: 900px;

}

.tab-item {
  color: #fff;
  padding: 0 32px;
  height: 48px;
  line-height: 48px;
  font-size: 1.1rem;
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  background: #fff;
  color: var(--primary-color);
  border-radius: 4px 4px 0 0;
}

.section-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 32px 0 16px 0;
  line-height: 1.2;
  color: #222;
  letter-spacing: 1px;
}
</style>