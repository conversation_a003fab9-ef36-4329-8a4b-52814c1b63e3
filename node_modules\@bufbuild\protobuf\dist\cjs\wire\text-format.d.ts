import { type DescEnum, ScalarType } from "../descriptors.js";
/**
 * Parse an enum value from the Protobuf text format.
 *
 * @private
 */
export declare function parseTextFormatEnumValue(descEnum: DescEnum, value: string): number;
/**
 * Parse a scalar value from the Protobuf text format.
 *
 * @private
 */
export declare function parseTextFormatScalarValue(type: ScalarType, value: string): number | boolean | string | bigint | Uint8Array;
